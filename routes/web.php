<?php

use App\Http\Controllers\Admin\UsersController;
use App\Http\Controllers\Front\HomepageController;
use App\Http\Controllers\Front\PropertiesController;
use App\Http\Controllers\Front\SettingsController;
use App\Http\Controllers\Front\ContactController;
use App\Http\Controllers\Front\FranchiseController;
use App\Http\Controllers\Front\NewsletterController;
use App\Http\Controllers\Front\MagazineController;
use App\Http\Controllers\Front\DevelopmentsController;
use App\Http\Controllers\Front\GeographiesController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\Front\StaticController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\Front\ImagesController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Crm\DealsController;
use App\Http\Controllers\Crm\TextareaController;
use App\Http\Controllers\Front\SitemapsController;
use App\Http\Controllers\API\CurrencyController;
use App\Http\Controllers\Crm\DashboardControllerCrm;
use App\Http\Controllers\Crm\LeadsControllerCrm;
use App\Http\Controllers\Front\AccountInteractionController;
use App\Http\Controllers\Front\AgentsController;
use App\Http\Controllers\Front\InvestmentOpportunitiesController;
use App\Http\Controllers\Front\ShortStayController;
use App\Http\Controllers\XML\FeedController;
use App\Http\Controllers\Front\ThankYouController;
use App\Http\Controllers\Front\WebhooksController;
use App\Models\Language;
use App\Services\MenuHelperService;
use App\Services\PropertiesService;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('sitemap.xml', [SitemapsController::class, 'index'])->name('sitemap.index');
Route::get('sitemap.xsl', [SitemapsController::class, 'sitemapXSL'])->name('sitemap-xsl');
Route::get('sitemap/clickable', [SitemapsController::class, 'sitemapClickable'])->name('sitemap-clickable');

Route::group(['prefix' => 'sitemap'], function () {
    Route::get('pages.xml', [SitemapsController::class, 'pages'])->name('sitemap_pages');
    // Route::get('{propertyType}-for-{operationType}.xml', 'front\SitemapController@listingSitemap')->where()->name('sitemap_listing_prop_op');
    Route::get('properties.xml', [SitemapsController::class, 'listings'])->name('sitemap_properties');
    Route::get('properties-table', [SitemapsController::class, 'listingsTable'])->name('sitemap_properties_table');
    Route::get('{sitemapFileName}.xml', [SitemapsController::class, 'sitemapFileName'])->name('sitemap_placeholder');
    Route::get('{propertyType}-for-{operationType}-{regionSlug}.xml', [SitemapsController::class, 'listingSitemap'])->name('sitemap_listing_prop_op_region');
    Route::get('{propertyType}-for-{operationType}.xml', [SitemapsController::class, 'listingIndexSitemap'])->name('sitemap_listing_prop_op');
});

// redirect old urls
Route::get('/{operationType}/{propertyURL}', function ($operationType, $propertyURL) {
    return redirect(route('search.dynamic', ['locale' => 'en', 'operationType' => $operationType, 'propertyURL' => $propertyURL]), 301);
})->where('operationType', 'rent|buy')->name('old.search.dynamic');

Route::get('/{operationType}/{regionSlug}/{propertyURL}', function ($operationType, $regionSlug, $propertyURL) {
    return redirect(route('search.dynamic.region', ['locale' => 'en', 'operationType' => $operationType, 'regionSlug' => $regionSlug, 'propertyURL' => $propertyURL]), 301);
})->where('operationType', 'rent|buy')->name('old.search.dynamic.region');

Route::prefix('property')->group(function () {
    Route::get('{id}/{refNo}', function ($id, $refNo) {
        $snapshot = app()->make(PropertiesService::class)->getSnapshot($id, $refNo, app()->getLocale());
        if (!is_null($snapshot)) {
            $nextURL = MenuHelperService::createURLForSnapshot($snapshot, app()->getLocale());
            return redirect($nextURL, 301);
        } else {
            abort(404);
        }
        // return redirect(route('properties.single', ['locale' => 'en', 'propertyWord' => __('property'), 'id' => $id, 'refNo' => $refNo]), 301);
    })->name('old.properties.single');
});
Route::get('properties/office-for-rent-lusail--1773', function () {
    return redirect('en/property/1773/OF15LML1-28', 301);
});
Route::get('properties/apartment-for-rent-doha-the-pearl--209', function () {
    return redirect('en/rent/doha/apartments-for-rent-in-the-pearl', 301);
});
Route::get('en/rent/short-stay-apartment', function () {
    return redirect('en/rent/short-stay-property', 301);
});
Route::get('en/buy/doha/apartments-for-rent-the-pearl', function () {
    return redirect('en/buy/doha/apartments-for-sale-the-pearl', 301);
});

Route::get('en/buy/qatar/villas.html', function () {
    return redirect('en/buy/villas-for-sale', 301);
});
Route::get('en/buy/villas.html', function () {
    return redirect('en/buy/villas-for-sale', 301);
});
Route::get('en/buy/villas', function () {
    return redirect('en/buy/villas-for-sale', 301);
});
Route::get('en/buy/qatar/offices.html', function () {
    return redirect('en/buy/offices-for-sale', 301);
});
Route::get('en/buy/offices.html', function () {
    return redirect('en/buy/offices-for-sale', 301);
});
Route::get('en/buy/offices', function () {
    return redirect('en/buy/offices-for-sale', 301);
});
Route::get('en/buy/lusail/apartments-for-rent', function () {
    return redirect('en/buy/lusail/apartments-for-sale', 301);
});
Route::get('en/buy/qatar/lands.html', function () {
    return redirect('en/buy/land-for-sale', 301);
});
Route::get('en/buy/lands.html', function () {
    return redirect('en/buy/land-for-sale', 301);
});
Route::get('en/buy/lands', function () {
    return redirect('en/buy/land-for-sale', 301);
});


// handle this in this place to handle it fast
Route::get('properties/{slug}', function ($slug) {
    $parts = explode('--', $slug);
    if (count($parts) == 2 && is_numeric($parts[1])) {
        $ps = \App\Models\PropertySnapshot::where('listing_id', $parts[1])->first();
        if (!is_null($ps)) {
            $nextURL = MenuHelperService::createURLForSnapshot($ps);
            return redirect($nextURL, 301);
        }
    }
});
Route::get('en/magazine/44', function () {
    return redirect('/', 302);
});
Route::get('en/company', function () {
    return redirect('en/about-us', 301);
});
Route::get('rent', function () {
    return redirect('en/rent/properties-for-rent', 301);
});
Route::get('en/rent/qatar/offices.html', function () {
    return redirect('en/rent/offices-for-rent', 301);
});
Route::get('en/rent/qatar/villas.html', function () {
    return redirect('en/rent/villas-for-rent', 301);
});
Route::get('en/rent/qatar/apartments.html', function () {
    return redirect('en/rent/apartments-for-rent', 301);
});
Route::get('en/buy/qatar/apartments.html', function () {
    return redirect('en/buy/apartments-for-sale', 301);
});
Route::get('buy', function () {
    return redirect('en/buy/properties-for-sale ', 301);
});
Route::get('en/contact', function () {
    return redirect('en/contact-us ', 301);
});

Route::get('en', function () {
    return redirect(route('home'), 301);
});

foreach (['contact-us', 'about-us', 'international', 'info-guide'] as $staticPageSlug) {
    Route::get($staticPageSlug, function () use ($staticPageSlug) {
        return redirect(route('pages.static', ['locale' => 'en', 'slug' => $staticPageSlug]), 301);
    });
}

foreach (
    [
        'regions/doha--msheireb--msheireb-downtown' => 'msheireb-downtown',
        'regions/doha--the-pearl--porto-arabia' => 'porto-arabia',
        'regions/doha--the-pearl--viva-bahriya' => 'viva-bahriya'
    ] as $url => $areaSlug
) {
    Route::get($url, function () use ($areaSlug) {
        return redirect(route('areas.single.get', ['locale' => 'en', 'slug' => $areaSlug]), 301);
    });
}

// end redirect urls
Route::get('{locale}', function ($locale) {
    if (!in_array($locale, ['en', 'ar', 'fr', 'pt', 'zh'])) {
        abort(400);
    }

    App::setLocale($locale);
})->where(['locale' => 'en|ar']);

Route::prefix('{locale}')->where(['locale' => 'en|ar'])->group(function () {

    Route::get('listing/{agentSlug}/{listingId}', [PropertiesController::class, 'singleShare'])->name('single.listing.share-personal');

    Route::get('', [HomepageController::class, 'home'])->name('home.localized');

    Route::get('i', [ImagesController::class, 'index'])->name('images.index');

    Route::prefix('{rentWord}/{shortStayPropertyWord}')->where(['rentWord' => 'rent|للايجار', 'shortStayPropertyWord' => 'short-stay-property|اقامة-قصيرة'])->group(function () {
        Route::get('', [ShortStayController::class, 'index'])->name('book-your-stay.index');
        // Route::get('email-test', [ShortStayController::class, 'testMail'])->name('book-your-stay.email-test ');
        Route::get('checkout-return', [ShortStayController::class, 'confirmAndPayReturn'])->name('book-your-stay.confirm-and-pay-checkout-return');
        Route::get('checkout', [ShortStayController::class, 'checkout'])->name('book-your-stay.checkout');
        Route::post('checkout', [ShortStayController::class, 'confirmAndPay'])->name('book-your-stay.confirm-and-pay');
        Route::get('thank-you', [ShortStayController::class, 'thankyou'])->name('book-your-stay.thankyou');
    });


    Route::prefix('{searchWord}')->where(['searchWord' => 'search|بحث'])->group(function () {
        Route::get('', [SearchController::class, 'index'])->name('properties.search');
        Route::get('{exclusiveWord}', [SearchController::class, 'index'])->where(['exclusiveWord' => 'exclusive|حصرية'])->name('properties.search.exclusive');
        Route::get('{propertyType}', [SearchController::class, 'index'])->where(['propertyType' => 'apartments|villas|offices|commercial-villas|shops|land|penthouses|townhouses|challets|hotel-apart|villa-compound|whole-building|شقق|فلل|مكاتب|فلل-تجارية|محلات|قطعة-من-الأرض|بنتهاوس|تاون-هاوس|شاليه|شقة-فندقية|مجمع-فيلات|المبنى-كله|'])->name('properties.search.allPropertyTypes');
        Route::get('{location}/{propertyType}', [SearchController::class, 'index'])->where(['propertyType' => 'apartments|villas|offices|commercial-villas|shops|land|penthouses|townhouses|challets|hotel-apart|villa-compound|whole-building|شقق|فلل|مكاتب|فلل-تجارية|محلات|قطعة-من-الأرض|بنتهاوس|تاون-هاوس|شاليه|شقة-فندقية|مجمع-فيلات|المبنى-كله|'])->name('properties.search.allPropertyTypesWithLocation');
    });
    //    Route::prefix('بحث')->group(function () {
    //        Route::get('', [SearchController::class, 'index'])->name('ar.properties.search');
    //        Route::get('حصرية', [SearchController::class, 'index'])->name('ar.properties.search.exclusive');
    //    });

    //    Route::get('/{propertyURL}/{operationType}', [SearchController::class, 'index'])->where('operationType', 'للايجار|للبيع')->name('ar.search.dynamic');
    Route::get('/{operationType}/{propertyURL}', [SearchController::class, 'index'])->where('operationType', 'rent|buy|commercial-rent|commercial-buy|للايجار|للبيع')->name('search.dynamic');
    Route::get('/{operationType}/{regionSlug}/{propertyURL}', [SearchController::class, 'index'])->where('operationType', 'rent|buy|للايجار|للبيع')->name('search.dynamic.region');
    //    Route::get('/{operationType}/{regionSlug}/{propertyURL}', [SearchController::class, 'index'])->where('locale', 'ar')->name('ar.search.dynamic.region');

    Route::prefix('{propertyWord}')->where(['propertyWord' => 'property|ملكية'])->group(function () {
        // for-rent للايجار
        Route::get('{operationType}/{propertyURL}', [PropertiesController::class, 'single'])->where(['operationType' => 'rent|buy|' . __('rent', [], Language::AR) . '|' . __('buy', [], Language::AR)])->name('properties.single');
        // Route::get('{id}/{refNo}', [PropertiesController::class, 'single'])->name('properties.single');
        Route::get('{id}/{refNo}', [PropertiesController::class, 'singleOld'])->name('properties.single.old');
        Route::get('{id}/{refNo}/brochure', [PropertiesController::class, 'downloadBrochure'])->name('properties.brochure.download');
        Route::get('{id}/{refNo}/layout', [PropertiesController::class, 'downloadLayout'])->name('properties.layout.download');
    });
    Route::prefix('snapshot')->group(function () {
        Route::get('{id}/{refNo}/brochure', [PropertiesController::class, 'downloadSnapshotBrochure'])->name('snapshot.brochure.download');
        Route::get('{id}/{refNo}/layout', [PropertiesController::class, 'downloadSnapshotLayout'])->name('snapshot.layout.download');
    });
    Route::get('map', [PropertiesController::class, 'allOnMap'])->name('map.all');

    Route::prefix('{magazineWord}')->where(['magazineWord' => 'magazine|المجلة'])->group(function () {
        Route::get('', [MagazineController::class, 'index'])->name('magazine');
        Route::get('{id}', [MagazineController::class, 'magazineIssue'])->name('magazine.issue');
    });

    Route::post('/contact', [ContactController::class, 'index'])->name('contact');
    Route::get('/account-interaction', [AccountInteractionController::class, 'index'])->name('index');
    Route::post('/account-interaction', [AccountInteractionController::class, 'save'])->name('account-interaction');
    Route::get('thank-you', [ThankYouController::class, 'index'])->name('pages.thank-you');
    Route::post('/franchise', [FranchiseController::class, 'franchise'])->name('franchise');
    Route::post('/newsletter', [NewsletterController::class, 'newsletter'])->name('newsletter');
    //    Route::post('/developments', [DevelopmentsController::class, 'developments'])->name('developments.index');

    Route::prefix('areas')->group(function () {
        Route::get('', [GeographiesController::class, 'index'])->name('areas.index');
        Route::get('{slug}', [GeographiesController::class, 'single'])->name('areas.single.get');
    });

    Route::prefix('المناطق')->group(function () {
        Route::get('', [GeographiesController::class, 'index'])->name('areas.ar.index');
        Route::get('{slug}', [GeographiesController::class, 'single'])->name('areas.ar.single.get');
    });

    Route::get('{developmentsWord}', [DevelopmentsController::class, 'index'])->where(['developmentsWord' => 'developments|استثمار'])->name('developments');
    Route::get('{internationalWord}/{section}', [SearchController::class, 'international'])->where(['internationalWord' => 'international|دولي', 'section' => 'dubai|دبي|saudi-arabia|سعودي|romania|رومانيا|lebanon|لبنان|oman|سلطنة-عمان|spain|إسبانيا|uk|المملكة-المتحدة|ontario|أونتاريو|paris|باريس|kamena-vourla|كامينا-فورلا'])->name('international');
    Route::get('{investmentOpportunitiesWord}', [InvestmentOpportunitiesController::class, 'index'])->where(['investmentOpportunitiesWord' => 'investment-opportunities|فرص-الاستثمار'])->name('investment-opportunities');

    Route::get('/find-agent', [AgentsController::class, 'index'])->name('agents.index');
    Route::get('/find-agent/{slug}', [AgentsController::class, 'single'])->name('agents.single');

    //projects
    Route::get('/projects/lusail-marina-heights', [DevelopmentsController::class, 'marinaHeightsLandingPage'])->name('projects.marina.heights.landing');
    Route::get('/projects/lusail-marina-heights/download', [DevelopmentsController::class, 'marinaHeightsDownloadBrochure'])->name('project.marinaHeights.download');
    Route::get('/{projectsWord}/cview-landing-page', [DevelopmentsController::class, 'cviewLanding'])->where(['projectsWord' => 'projects|المشاريع'])->name('project.cview.landing');
    Route::get('/المشاريع/صفحة-الهبوط-لـ-cview', [DevelopmentsController::class, 'cviewLanding'])->where(['projectsWord' => 'projects|المشاريع'])->name('project.cview.landing.ar');
    Route::get('/{projectsWord}/cview-landing-page/download', [DevelopmentsController::class, 'cviewLandingDownloadBrochure'])->name('project.cview.download');
    Route::get('/{projectsWord}/cview-thankyou', [DevelopmentsController::class, 'cviewLandingThankyou'])->name('project.cview.landing.thankyou');
    Route::get('/{projectsWord}/{projectSlug}', [DevelopmentsController::class, 'projectIndex'])->name('project.index');
    Route::get('/{projectsWord}/{projectSlug}/3dwalkthrough', [DevelopmentsController::class, 'walkthrough'])->name('project.3dwalkthrough');
    Route::get('/{projectsWord}/{projectSlug}/360', [DevelopmentsController::class, 'project360View'])->name('project.360');
    Route::get('/{projectsWord}/{projectSlug}/thankyou', [DevelopmentsController::class, 'thankyou'])->name('project.thankyou');
    Route::get('/{projectsWord}/{projectSlug}/floor/{floorId}', [DevelopmentsController::class, 'floorView'])->name('project.floorView');

});
Route::get('ar/{slug}', [StaticController::class, 'page'])
    ->where([
        'slug' => 'فرص-الاستثمار|استجابة-كوفيد-19|الشروط-والأحكام|سياسة-ملفات-تعريف-الإرتباط|سياسة-الخصوصية|امتياز|بيع-الممتلكات|دولي|اتصل-بنا|من-نحن|وظائف|دليل-المعلومات|النشرة-الإخبارية|أصبح-وكيلا|خدمة-كونسيرج|دليل-المستثمرين'
    ])
    ->name('ar.pages.static');
Route::get('en/{slug}', [StaticController::class, 'page'])
    ->where([
        'slug' => 'contact-us|sell-property|franchise|privacy-policy|cookie-policy|terms-and-conditions|covid19-response|about-us|careers|international|info-guide|newsletter|become-an-agent|concierge-service|investors-guide',
    ])
    ->name('pages.static');
Route::post('/settings', [SettingsController::class, 'settings'])->name('settings');
// Route::get('/encode-geographies-images', [HomepageController::class, 'encodeGeographiesImages'])->name('encodeGeographiesImages');
Route::get('get-route', [\App\Http\Controllers\API\SEOContentController::class, 'getRoute'])->name('test.get-route');

//auth routes
Route::get('login', [AuthController::class, 'login'])->name('login');
Route::post('login', [AuthController::class, 'postLogin'])->name('login.post');
Route::get('logout', [AuthController::class, 'logout'])->name('logout');

//Route::get('manual-import', [\App\Http\Controllers\Front\ManualImportController::class, 'index'])->name('manual-import-index');

Route::get('', [HomepageController::class, 'home'])->name('home');
Route::get('properties-stats', [HomepageController::class, 'propertiesStats'])->name('homePropertiesStats');

Route::prefix('crm')->middleware(['auth', 'check.wifi'])->group(base_path('routes/crm/index.php'));
Route::prefix('admin')->middleware('auth')->group(base_path('routes/admin/index.php'));
Route::prefix('short-term-rentals')->middleware('auth')->group(base_path('routes/short-term-rentals/index.php'));

Route::prefix('feed')->group(function () {
    Route::get("{tag}/JamesEdition_feed_1489260.xml", [FeedController::class, 'feed'])->name("export-jamesedition-feed");
    Route::get("{tag}/feed.xml", [FeedController::class, 'feed'])->name("export-feed");
});

Route::prefix('webhooks')->group(function () {
    Route::post('enquire', [WebhooksController::class, 'enquire'])->name('webhooks.enquire');
    Route::prefix('google-lead')->group(function () {
        Route::post('', [WebhooksController::class, 'googleLead'])->name('webhooks.google-lead');
        Route::post('{leadType}', [WebhooksController::class, 'googleLead'])->where('leadType', 'jmj|seef|blossom')->name('webhooks.google-lead-with-type');
    });
});

Route::get('marketing-campaign-contacts', [DashboardControllerCrm::class, 'exportContactsForMarketingCampaign'])->name('marketing-contacts-export');

// jsonp routes
Route::group(['prefix' => '/jsonp', 'middleware' => ['web']], function () {
    Route::get('/initializeEditor/{id}/', [TextareaController::class, 'initalizeEditor'])->name('jsonp-wysiwyg-init');
});

Route::get('ip', function() {
    return ['ip' => getenv('REMOTE_ADDR')];
});
// exchange rates
Route::get('/sync-exchange-rates', [CurrencyController::class, 'syncRates'])->name('sync-exchange-rates');
Route::prefix('artisan-commands')->group(function () {
    Route::get('update-contact-prefixes', function () {
        Artisan::call('contacts:update-prefixes');
    });
    Route::get('cache-clear', function () {
        Artisan::call('cache:clear');
    });
    Route::get('view-clear', function () {
        Artisan::call('view:clear');
    });
    Route::get('route-clear', function () {
        Artisan::call('route:clear');
    });
    Route::get('view-cache', function () {
        Artisan::call('view:cache');
    });
    Route::get('route-cache', function () {
        Artisan::call('route:cache');
    });
    Route::get('storage-link', function () {
        Artisan::call('storage:link');
    });
    Route::get('permission-cache-reset', function () {
        Artisan::call('permission:cache-reset');
    });
    Route::get('generate-webps', function () {
        Artisan::call('images:create-webp-versions');
    });
    Route::get('ui-auth', function () {
        Artisan::call('ui:auth');
    });
    Route::get('ui-controllers', function () {
        Artisan::call('ui:controllers');
    });
    Route::get('notifications-push', function () {
        Artisan::call('notifications:push');
    });
    Route::get('export-listing-to-feeds', function () {
        Artisan::call('export:listings-to-feeds');
    });
    Route::get('fgr-contacts-newsletters', function () {
        Artisan::call('sync:fgr-contacts-newsletters');
    });
    Route::get('send-reminders', function () {
        Artisan::call('agent:send-reminders');
    });
    Route::get('send-reminders-contract-expires', function () {
        Artisan::call('agent:send-reminders-contract-expires');
    });
    Route::get('send-report', function () {
        Artisan::call('reports:send-stats-report');
    });
    Route::get('import-translations', function () {
        Artisan::call('import:translations');
    });
    Route::get('send-dob-reminder', function () {
        Artisan::call('command:send-dob-reminder');
    });
    Route::get('sync-pf-snapshots', function () {
        Artisan::call('sync:pf-snapshots');
    });
    Route::get('send-note-reminder', function () {
        Artisan::call('reports:send-note-reminder');
    });
    Route::get('unpublish-older-listings', function () {
        Artisan::call('listings:unpublish-older-listings');
    });
    Route::get('landlords-expiring-contracts', function () {
        Artisan::call('landlord:contract-expiration-notification');
    });
    Route::get('sync-pf-published-listings', function () {
        Artisan::call('propertyfinder:sync-published-listings');
    });
    Route::get('import-the-pearl-landlords', function () {
        Artisan::call('import:landlords2');
    });
    Route::get('user-birthday-reminder', function () {
        Artisan::call('user:birthday-reminder');
    });
    Route::get('send-hb-email', function () {
        Artisan::call('command:send-hb-email');
    });
    Route::get('import-emaar-contacts', function () {
        Artisan::call('import:emaar-list');
    });
    Route::get('send-reminders-deals-expires', function () {
        Artisan::call('admin:send-reminders-deals-expires');
    });
    Route::get('agents-listings-reports', function () {
        Artisan::call('agent:listings-reports');
    });
    Route::get('leads-bedrooms-migrate', function () {
        Artisan::call('lead:bedrooms-migrate');
    });
    Route::get('leads-property-types-migrate', function () {
        Artisan::call('lead:property-types-migrate');
    });
    Route::get('competition-winner-email', function () {
        Artisan::call('command:competition-winner-email');
    });
    Route::get('room-price-per-period', [HomepageController::class, 'roomPrices']);
    Route::get('categorize-contacts', function() {
        Artisan::call('contacts:categorize');
    });
    Route::get('tasks-create-followup', function() {
        Artisan::call('tasks:create-followup');
    });
    Route::get('send-leads-with-rating-report', function() {
        Artisan::call('app:send-leads-per-rating-report');
    });
    Route::get('send-tasks-report', function() {
        Artisan::call('app:send-tasks-report');
    });
    Route::get('daily-short-stats', function() {
        Artisan::call('app:daily-short-stats');
    });
    Route::get('automations_run', function() {
        Artisan::call('tasks:create-from-automatization');
    });
    Route::get('update-lead-utm-from-metadata', function() {
        Artisan::call('leads:update-utm-from-metadata --chunk=1000');
        return 'Command executed successfully!';
    });
    Route::get('landlords:handle-expiring', function() {
        Artisan::call('landlords:handle-expiring');
        return 'Command executed successfully!';
    });
    Route::get('invoices:generate-invoices-for-deals', function() {
        Artisan::call('invoices:generate-invoices-for-deals');
        return 'Command executed successfully!';
    });
});
Route::get('/regenerate-stats', [PropertiesController::class, 'regenerateStats'])->name('properties.regenerate-stats');
Route::get('expo-test', [\App\Http\Controllers\ExpoController::class, 'sendDummyNotification'])->name('expo.test');
Route::get('/email-validation', [HomepageController::class, 'emailValidationAfter'])->name('email.validation.after');
Route::get('attributes-sync', [HomepageController::class, 'attributesSync'])->name('attributes.sync');
Route::get('all-listing-descriptions', [PropertiesController::class, 'allListingDescriptions'])->name('properties.listings');
Route::get('translation-to-db', [PropertiesController::class, 'fromTranslationFileToDB'])->name('properties.listings.trans-to-db');
Route::get('stats-emulation', [PropertiesController::class, 'statsEmulation'])->name('properties.listings.stats-emulation');
Route::get('check-next-ref-no', [PropertiesController::class, 'checkNextRefNo']);
Route::get('create-user-slugs', [HomepageController::class, 'createUserSlugs']);
// comment these routes
// Route::get('leads-history-create', [HomepageController::class, 'leadsHistoryCreate']);
// Route::get('leads-cleanup', [HomepageController::class, 'leadsCleanup']);
// Route::get('add-contact-to-remarks', [HomepageController::class, 'addContactsRemarks']);
Auth::routes([
    'register' => false, // Register Routes...
    'reset' => false, // Reset Password Routes...
    'verify' => true, // Email Verification Routes...
]);
Route::get('documents-downloads/{dealId}', [DealsController::class, 'downloadDocuments'])->name('deals.documents.download');

Route::post('qnb-funding', [LeadsControllerCrm::class, 'leadByQnbFunding'])->name('leads.qnb-funding');
Route::get('users/{id}/user-license', [UsersController::class, 'downloadLicense'])->name('web.user.license.download');
