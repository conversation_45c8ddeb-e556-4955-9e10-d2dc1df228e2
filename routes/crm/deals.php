<?php
use App\Http\Controllers\Crm\DealsController;
use App\Http\Controllers\Crm\DealDashboardController;
use App\Http\Controllers\Crm\DealNotesController;

Route::get("", [DealsController::class, 'index'])->name("crm.deals.index");
Route::get("handle-reminders", [DealsController::class, 'handleOldDealsReminders'])->name("crm.deals.handle-deals-reminders");
Route::group(["prefix" => "{id}"], function () {

    Route::get("", [DealsController::class, 'read'])->name("crm.deals.read");
    Route::get('/details', [DealsController::class, 'getDealDetails'])->name('deal.deals.details');
    Route::get('/download-form', [DealsController::class, 'downloadForm'])->name('deal.forms.download');
    Route::get('/preview-form', [DealsController::class, 'previewForm'])->name('deal.forms.preview');
    Route::get('/pdf-document', [DealsController::class, 'downloadDealDocument'])->name('deal.pdf.generateDealPDF');
    Route::get('/invoice/{invoiceId}/view', [DealsController::class, 'viewInvoicePDF'])->name('deal.invoice.view');
    Route::get('/invoice/{invoiceId}/download', [DealsController::class, 'downloadInvoicePDF'])->name('deal.invoice.download');
    Route::get("edit", [DealsController::class, 'update'])->name("crm.deals.edit");
    Route::get("delete", [DealsController::class, 'delete'])->name("crm.deals.delete");
    Route::post("delete/post", [DealsController::class, 'postDelete'])->name("crm.deals.delete.post");
    Route::post("edit/post", [DealsController::class, 'postUpdateCustom'])->name("crm.deals.update");

    Route::group(['prefix' => 'manage'], function () {
        Route::get("", [DealDashboardController::class, 'index'])->name("deal-dashboard");

        Route::group(['prefix' => 'notes'], function () {
            Route::post("add", [DealNotesController::class, 'addNote'])->name("deal-dashboard-add-note");
            Route::post("remove/{noteId}", [DealNotesController::class, 'remove']);
            Route::post("update/{noteId}", [DealNotesController::class, 'update']);
            Route::post("update/{noteId}/add-reminder", [DealNotesController::class, 'addReminder']);
        });

        Route::group(['prefix' => 'reminders'], function () {
            Route::post("/remove/{reminderId}", [DealNotesController::class, 'removeReminder']);
            Route::post("/update/{reminderId}", [DealNotesController::class, 'updateReminder']);
        });

        Route::get("/get-notes", [DealNotesController::class, 'index'])->name("deal-dashboard-notes-list");
        Route::get("/calendar-view", [DealNotesController::class, 'calendarView'])->name("deal-dashboard-notes-calendar-view");
    });

    Route::post('status', [DealsController::class, 'updateStatus'])->name('crm.deals.status');
    Route::post('payment-status', [DealsController::class, 'paymentStatusToDeal'])->name('crm.deals.status.payment');

    Route::get("notes-list.js", [DealDashboardController::class, 'notesListJavascript'])->name('crm.deal.dashboard.notes-list-js');
    Route::get("notes-common.js", [DealDashboardController::class, 'notesCommonJavascript'])->name('crm.deal.dashboard.notes-common-js');
    Route::get("notes-calendar.js", [DealDashboardController::class, 'notesCalendarJavascript'])->name('crm.deal.dashboard.notes-calendar-js');
    Route::get("notes-reminders.js", [DealDashboardController::class, 'notesRemindersJavascript'])->name('crm.deal.dashboard.notes-reminders-js');
});

