<?php

use App\Http\Controllers\Admin\UsersController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Crm\DashboardControllerCrm;
use App\Http\Controllers\Crm\AttachmentsController;
use App\Http\Controllers\Crm\CheckInController;
use App\Http\Controllers\Crm\TestController;
use App\Http\Controllers\Crm\ExportController;
use App\Http\Controllers\Crm\LeadDashboardNextController;
use App\Http\Controllers\Crm\TrainingsController;
use App\Http\Controllers\Crm\ContactsHandlerController;
use App\Http\Controllers\Crm\GlobalSearchController;
use App\Http\Controllers\Crm\MetricsController;
use App\Http\Controllers\Crm\OpenAIController;
use App\Models\Crm\PermissionsDef;

Route::prefix('')->group(function () {
    Route::get('', [DashboardControllerCrm::class, 'index'])->name('dashboard.index');
    Route::get('global-search', [GlobalSearchController::class, 'search']);
    Route::get('closed-deals-export', [DashboardControllerCrm::class, 'exportClosedDeals'])->name('closedDealsExport');
    Route::get('2024-deals-export', [DashboardControllerCrm::class, 'export2024Deals'])->name('export2024Deals');
    Route::get('all-2024-deals-export', [DashboardControllerCrm::class, 'exportAll2024Deals'])->name('exportAll2024Deals');
    Route::get('dubai-investors-export', [DashboardControllerCrm::class, 'exportDubaiInvestors'])->name('exportDubaiInvestors');
    Route::get('thepearl-sale-leads', [DashboardControllerCrm::class, 'showFileUpload'])->name('showFileUpload');
    Route::post('thepearl-sale-leads', [DashboardControllerCrm::class, 'thePearlSaleLeads'])->name('thePearlSaleLeads');
    Route::get('first-quarter-2025', [DashboardControllerCrm::class, 'firstQuarter2025'])->name('firstQuarter2025');
    Route::get('first-quarter-2025/export', [DashboardControllerCrm::class, 'firstQuarter2025Export'])->name('firstQuarter2025Export');
    Route::prefix('dashboard')
        ->group(base_path('routes/crm/dashboard.php'));
    Route::prefix('short-stay')
        ->group(base_path('routes/crm/short-stay.php'));
    Route::prefix('marketing-projects')
        ->group(base_path('routes/crm/marketing-projects.php'));
    Route::prefix('bookings')
        ->group(base_path('routes/crm/bookings.php'));
    Route::prefix('inventory')
        ->middleware(['permission:' . PermissionsDef::INVENTORY_MASTER_READ . '|' . PermissionsDef::INVENTORY_UPDATE . '|' . PermissionsDef::INVENTORY_DELETE . '|' . PermissionsDef::DASHBOARD . '|' . PermissionsDef::LEADS_READ . '|' . PermissionsDef::CRM_ADMIN . '|' . PermissionsDef::SEO_MODULE . '|' . PermissionsDef::INVENTORY_WAITING_FOR_PICTURES . '|' . PermissionsDef::SHORT_STAY_USER])
        ->group(base_path('routes/crm/inventory.php'));

    Route::prefix('developments')->group(base_path('routes/crm/developments.php'));
    Route::prefix('geographies')->group(base_path('routes/crm/geographies.php'));
    Route::prefix('leads')
        ->middleware(['permission:' . PermissionsDef::LEADS_CREATE . '|' . PermissionsDef::LEADS_READ . '|' . PermissionsDef::LEADS_UPDATE . '|' . PermissionsDef::LEADS_DELETE])
        ->group(base_path('routes/crm/leads.php'));
    Route::prefix('tasks')
        ->group(base_path('routes/crm/tasks.php'));
    Route::prefix('deals')
        ->middleware(['permission:' . PermissionsDef::DEALS_READ])
        ->group(base_path('routes/crm/deals.php'));
    Route::prefix('accountant')
        ->group(base_path('routes/crm/accountant.php'));
    Route::prefix('automatizations')
        ->group(base_path('routes/crm/automatizations.php'));
    Route::prefix('broker-landlords')
        ->middleware(['permission:' . PermissionsDef::INVENTORY_MASTER_READ . '|' . PermissionsDef::INVENTORY_UPDATE . '|' . PermissionsDef::INVENTORY_DELETE . '|' . PermissionsDef::SHORT_STAY_USER])
        ->group(base_path('routes/crm/broker-landlords.php'));
    Route::prefix('magazine')
        ->middleware(['permission:' . PermissionsDef::SITE_CONTENT_READ . '|' . PermissionsDef::SITE_CONTENT_ADD . '|' . PermissionsDef::CRM_ADMIN])
        ->group(base_path('routes/crm/magazine.php'));
    Route::prefix('seo')
        ->middleware(['permission:' . PermissionsDef::SEO_MODULE . '|' . PermissionsDef::SEO_CONTENT_EDIT . '|' . PermissionsDef::SEO_DYNAMIC_MENU_EDIT])
        ->group(base_path('routes/crm/seo.php'));
    Route::prefix('contacts')->group(base_path('routes/crm/contacts.php'));
    Route::prefix('contacts-list')
        ->middleware(['permission:' . PermissionsDef::SECTION_CONTACTS])
        ->group(base_path('routes/crm/contacts-list.php'));

    Route::group(['prefix' => 'attach'], function () {
        Route::post("delete/{imageName}", [AttachmentsController::class, 'delete'])->name('attachment.delete');
        Route::get("download/{imageName}", [AttachmentsController::class, 'download'])->name('attachment.download');
        Route::group(['prefix' => '{group}'], function () {
            Route::post('/store/{objectId}', [AttachmentsController::class, 'store'])->name("attachment.store");
        });
    });

    Route::get('att', [TestController::class, 'upl'])->name('test.getUpl');
    Route::post('att', [TestController::class, 'postUpl'])->name('test.postUpl');
    Route::prefix('export')->group(function () {
        Route::get('contact', [ExportController::class, 'contactEntries']);
        Route::get('agent-contact', [ExportController::class, 'agentContactEntries']);
    });
    // Route::get('user-settings', [UserSettingsController::class, 'getUserSettings']);
    // Route::post('user-settings', [UserSettingsController::class, 'postUserSettings']);

    Route::middleware(['permission:' . PermissionsDef::TRAININGS])->group(function () {
        Route::get('trainings', [TrainingsController::class, 'index'])->name('crm.trainings.index');
        Route::post('trainings', [TrainingsController::class, 'postTraining'])->name('crm.trainings.post');
        Route::get('trainings/{id}/download', [TrainingsController::class, 'downloadTraining'])->name('crm.trainings.download');
        Route::post('trainings/{id}/delete', [TrainingsController::class, 'deleteTraining'])->name('crm.trainings.delete');
    });

    Route::get('lead-dashboard', [LeadDashboardNextController::class, 'index'])->name('crm.lead-dashboard.index');
    Route::post("lead-dashboard/create", [LeadDashboardNextController::class, 'createLead'])->name("crm.lead-dashboard.create");
    Route::post("lead-dashboard/reassign-lead", [LeadDashboardNextController::class, 'reassignLead'])->name("crm.lead-dashboard.reassignLead");

    Route::get('user-team', [UsersController::class, 'userTeamList'])->name('crm.user-team');
    Route::get('jmj-contacts-tags', [DashboardControllerCrm::class, 'putTagsOnJmjContacts'])->name('crm.jmj-contacts-tags');
    Route::get('jmj', [DashboardControllerCrm::class, 'exportJMJSituation'])->name('crm.jmj-situation');
    Route::get('fgrealty-database', [DashboardControllerCrm::class, 'fgrealtyDatabase'])->name('crm.fgrealty-database');
    Route::get('reimport-google-leads', [DashboardControllerCrm::class, 'reimportGoogleLeads'])->name('crm.reimport-google-leads');
    Route::get('export-google-contacts-to-csv', [DashboardControllerCrm::class, 'exportGmailContactsToCSV'])->name('crm.export-google-contacts-to-csv');
    Route::get('export-op-type-google-contacts-to-csv', [DashboardControllerCrm::class, 'exportOpTypeContactsWithLeadsToCSV'])->name('crm.export-google-contacts-to-csv');
    Route::get('export-jmj-custom', [DashboardControllerCrm::class, 'exportJMJContactsCustom'])->name('crm.export-jmj-custom');
    Route::get('delete-contacts-and-leads', [DashboardControllerCrm::class, 'deleteContactsAndLeads'])->name('crm.delete-contacts-and-leads');
    Route::get('nohas-leads', [DashboardControllerCrm::class, 'nohasLeads'])->name('crm.nohas-leads');

    Route::prefix('contacts-handler')->group(function () {
        Route::get('upload', [ContactsHandlerController::class, 'showForm'])->name('contacts-handler.upload');
        Route::post('upload', [ContactsHandlerController::class, 'processUpload'])->name('contacts-handler.process');
    });

    Route::prefix('check-in')->group(function () {
        Route::post('', [CheckInController::class, 'store'])->name('check-in.store');
    });

    Route::prefix('open-ai')->group(function () {
        Route::post('generate-description', [OpenAIController::class, 'generateDescription']);
    });

    Route::middleware(['permission:' . PermissionsDef::GLOBAL_REPORTS_ACCESS])->group(function () {
        Route::prefix('metrics')->group(function () {
            Route::get('dashboard', [MetricsController::class, 'board'])->name('metrics.dashboard');
        });
    });
});
