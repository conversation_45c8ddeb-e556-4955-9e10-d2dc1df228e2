<?php

use App\Http\Controllers\Crm\MarketingProjectControllerCrm;

Route::prefix('')->group(function () {
    Route::get('', [MarketingProjectControllerCrm::class, 'index'])->name('crm.projects.index');
    Route::get('create', [MarketingProjectControllerCrm::class, 'create'])->name('crm.projects.create');
    Route::post("store", [MarketingProjectControllerCrm::class, 'store'])->name('crm.projects.create.post');
    Route::get("{id}/edit", [MarketingProjectControllerCrm::class, 'edit'])->name('crm.projects.edit');
    Route::post("{id}/update", [MarketingProjectControllerCrm::class, 'update'])->name('crm.projects.update');
    Route::get("{id}/delete", [MarketingProjectControllerCrm::class, 'destroy'])->name('crm.projects.destroy');
    Route::post("{id}/campaign/create", [MarketingProjectControllerCrm::class, 'campaign_create'])->name('crm.projects.campaign.create');
    Route::get("{id}/campaign/{campaign_id}/destroy", [MarketingProjectControllerCrm::class, 'campaign_destroy'])->name('crm.projects.campaign.destroy');
    Route::post("{id}/campaign/{campaign_id}/update", [MarketingProjectControllerCrm::class, 'campaign_update'])->name('crm.projects.campaign.update');
});
