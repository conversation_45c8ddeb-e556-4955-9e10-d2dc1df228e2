<?php
use App\Http\Controllers\Crm\AccountantController;

Route::get("", [AccountantController::class, 'index'])->name("crm.accountant.index");
Route::get("export-data", [AccountantController::class, 'exportData'])->name("crm.accountant.export-data");
Route::get("summary", [AccountantController::class, 'summary'])->name("crm.accountant.summary");
Route::post("payment/{paymentId}/update-cashed-in", [AccountantController::class, 'updateCashedIn'])->name("crm.accountant.update-cashed-in");

Route::prefix('{id}')->group(function () {
    Route::get('details', [AccountantController::class, 'getInvocesDetails'])->name('crm.accountant.details');
    Route::post('details/save-remark', [AccountantController::class, 'saveRemark'])->name('crm.accountant.details.remark');

    Route::get('edit', [AccountantController::class, 'edit'])->name('crm.accountant.edit');
    Route::post('update', [AccountantController::class, 'update'])->name('crm.accountant.update');
    Route::get('download-document/{documentId}', [AccountantController::class, 'downloadDocument'])->name('crm.accountant.download-document');
    Route::get('delete-document/{documentId}', [AccountantController::class, 'deleteDocument'])->name('crm.accountant.delete-document');
    Route::get('landlord_invoice/{inoviceId}', [AccountantController::class, 'downloadLandlordInvoice'])->name('crm.accountant.landlord.download-invoice');
    Route::get('client_invoice/{inoviceId}', [AccountantController::class, 'downloadClientInvoice'])->name('crm.accountant.client.download-invoice');
    Route::get('landlord/download-document/{documentId}', [AccountantController::class, 'downloadLandlordDocument'])->name('crm.accountant.landlord.download-document');
    Route::get('landlord/preview-document/{documentId}', [AccountantController::class, 'previewLandlordDocument'])->name('crm.accountant.landlord.preview-document');
    Route::get('client/download-document/{documentId}', [AccountantController::class, 'downloadClientDocument'])->name('crm.accountant.client.download-document');
    Route::get('client/preview-document/{documentId}', [AccountantController::class, 'previewClientDocument'])->name('crm.accountant.client.preview-document');

});

