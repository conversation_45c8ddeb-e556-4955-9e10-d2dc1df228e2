<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCommissionFieldsToDealsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('deals', function (Blueprint $table) {
            // Only add fields that don't already exist
            if (!Schema::hasColumn('deals', 'listing_agent_shared_commission')) {
                $table->decimal('listing_agent_shared_commission', 15, 2)->nullable()->after('referred_closing_agent_id');
            }
            if (!Schema::hasColumn('deals', 'referred_listing_agent_shared_commission')) {
                $table->decimal('referred_listing_agent_shared_commission', 15, 2)->nullable()->after('listing_agent_shared_commission');
            }
            if (!Schema::hasColumn('deals', 'closing_agent_shared_commission')) {
                $table->decimal('closing_agent_shared_commission', 15, 2)->nullable()->after('referred_listing_agent_shared_commission');
            }
            if (!Schema::hasColumn('deals', 'referred_closing_agent_shared_commission')) {
                $table->decimal('referred_closing_agent_shared_commission', 15, 2)->nullable()->after('closing_agent_shared_commission');
            }
            if (!Schema::hasColumn('deals', 'listing_agent_cash_in')) {
                $table->boolean('listing_agent_cash_in')->nullable()->default(false)->after('referred_closing_agent_shared_commission');
            }
            if (!Schema::hasColumn('deals', 'referred_listing_agent_cash_in')) {
                $table->boolean('referred_listing_agent_cash_in')->nullable()->default(false)->after('listing_agent_cash_in');
            }
            if (!Schema::hasColumn('deals', 'closing_agent_cash_in')) {
                $table->boolean('closing_agent_cash_in')->nullable()->default(false)->after('referred_listing_agent_cash_in');
            }
            if (!Schema::hasColumn('deals', 'referred_closing_agent_cash_in')) {
                $table->boolean('referred_closing_agent_cash_in')->nullable()->default(false)->after('closing_agent_cash_in');
            }
            // Add month fields if they don't exist
            if (!Schema::hasColumn('deals', 'listing_agent_month')) {
                $table->date('listing_agent_month')->nullable()->after('referred_closing_agent_cash_in');
            }
            if (!Schema::hasColumn('deals', 'referred_listing_agent_month')) {
                $table->date('referred_listing_agent_month')->nullable()->after('listing_agent_month');
            }
            if (!Schema::hasColumn('deals', 'closing_agent_month')) {
                $table->date('closing_agent_month')->nullable()->after('referred_listing_agent_month');
            }
            if (!Schema::hasColumn('deals', 'referred_closing_agent_month')) {
                $table->date('referred_closing_agent_month')->nullable()->after('closing_agent_month');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('deals', function (Blueprint $table) {
            $columnsToRemove = [];

            if (Schema::hasColumn('deals', 'listing_agent_shared_commission')) {
                $columnsToRemove[] = 'listing_agent_shared_commission';
            }
            if (Schema::hasColumn('deals', 'referred_listing_agent_shared_commission')) {
                $columnsToRemove[] = 'referred_listing_agent_shared_commission';
            }
            if (Schema::hasColumn('deals', 'closing_agent_shared_commission')) {
                $columnsToRemove[] = 'closing_agent_shared_commission';
            }
            if (Schema::hasColumn('deals', 'referred_closing_agent_shared_commission')) {
                $columnsToRemove[] = 'referred_closing_agent_shared_commission';
            }
            if (Schema::hasColumn('deals', 'listing_agent_cash_in')) {
                $columnsToRemove[] = 'listing_agent_cash_in';
            }
            if (Schema::hasColumn('deals', 'referred_listing_agent_cash_in')) {
                $columnsToRemove[] = 'referred_listing_agent_cash_in';
            }
            if (Schema::hasColumn('deals', 'closing_agent_cash_in')) {
                $columnsToRemove[] = 'closing_agent_cash_in';
            }
            if (Schema::hasColumn('deals', 'referred_closing_agent_cash_in')) {
                $columnsToRemove[] = 'referred_closing_agent_cash_in';
            }
            if (Schema::hasColumn('deals', 'listing_agent_month')) {
                $columnsToRemove[] = 'listing_agent_month';
            }
            if (Schema::hasColumn('deals', 'referred_listing_agent_month')) {
                $columnsToRemove[] = 'referred_listing_agent_month';
            }
            if (Schema::hasColumn('deals', 'closing_agent_month')) {
                $columnsToRemove[] = 'closing_agent_month';
            }
            if (Schema::hasColumn('deals', 'referred_closing_agent_month')) {
                $columnsToRemove[] = 'referred_closing_agent_month';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
}
