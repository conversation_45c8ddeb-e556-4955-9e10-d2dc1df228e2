<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMarketingCampaignIdToLeadSourcesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('lead_sources', function (Blueprint $table) {
            $table->unsignedBigInteger('marketing_campaign_id')->nullable()->after('name');
            $table->foreign('marketing_campaign_id')
                  ->references('id')
                  ->on('marketing__campaigns')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('lead_sources', function (Blueprint $table) {
            $table->dropForeign(['marketing_campaign_id']);
            $table->dropColumn('marketing_campaign_id');
        });
    }
}
