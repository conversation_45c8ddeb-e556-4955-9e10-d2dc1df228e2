-- SQL to create the invoice_documents table for storing document information
-- This replaces the JSON storage approach with a proper relational table

-- Create the invoice_documents table
CREATE TABLE IF NOT EXISTS invoice_documents (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    invoice_id BIGINT UNSIGNED NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    document_type ENUM('receipt', 'cheque') NOT NULL,
    receipt_no VARCHAR(100) NULL COMMENT 'Receipt number when document_type is receipt',
    cheque_number VARCHAR(100) NULL COMMENT 'Cheque number when document_type is cheque',
    observations TEXT NULL COMMENT 'User observations about the document',
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_invoice_documents_invoice_id 
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- Indexes for better performance
    INDEX idx_invoice_documents_invoice_id (invoice_id),
    INDEX idx_invoice_documents_document_type (document_type),
    INDEX idx_invoice_documents_upload_date (upload_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for better query performance on invoices table
CREATE INDEX IF NOT EXISTS idx_invoices_deal_id ON invoices(deal_id);
CREATE INDEX IF NOT EXISTS idx_invoices_is_client ON invoices(is_client);
CREATE INDEX IF NOT EXISTS idx_invoices_deal_client ON invoices(deal_id, is_client);

-- Example data structure:
-- invoice_documents table will contain records like:
-- +----+------------+------------------+--------------------------------+---------------+------------+---------------+------------------+---------------------+
-- | id | invoice_id | file_name        | file_path                      | document_type | receipt_no | cheque_number | observations     | upload_date         |
-- +----+------------+------------------+--------------------------------+---------------+------------+---------------+------------------+---------------------+
-- | 1  | 123        | receipt_001.pdf  | landlord_documents/123/rec.pdf | receipt       | REC-001    | NULL          | Payment receipt  | 2024-01-15 10:30:00 |
-- | 2  | 123        | cheque_copy.jpg  | landlord_documents/123/chq.jpg | cheque        | NULL       | CHQ-12345     | Bank cheque copy | 2024-01-15 10:35:00 |
-- +----+------------+------------------+--------------------------------+---------------+------------+---------------+------------------+---------------------+

-- Notes:
-- 1. document_type is an ENUM with only 'receipt' and 'cheque' values
-- 2. receipt_no is populated only when document_type = 'receipt'
-- 3. cheque_number is populated only when document_type = 'cheque'
-- 4. observations field stores user notes about the document
-- 5. Foreign key ensures referential integrity with invoices table
-- 6. CASCADE DELETE ensures documents are removed when invoice is deleted
