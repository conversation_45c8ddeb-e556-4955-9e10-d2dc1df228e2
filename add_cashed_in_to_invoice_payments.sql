-- SQL to add cashed_in column to invoice_payments table
-- This field will track whether a payment has been cashed in or not

-- Add the cashed_in column to the invoice_payments table
ALTER TABLE invoice_payments 
ADD COLUMN cashed_in BOOLEAN DEFAULT FALSE 
COMMENT 'Indicates whether the payment has been cashed in';

-- Create an index for better performance when querying by cashed_in status
CREATE INDEX IF NOT EXISTS idx_invoice_payments_cashed_in ON invoice_payments(cashed_in);

-- Example usage:
-- UPDATE invoice_payments SET cashed_in = TRUE WHERE id = 123;
-- SELECT * FROM invoice_payments WHERE cashed_in = FALSE;
