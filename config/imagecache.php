<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Name of route
    |--------------------------------------------------------------------------
    |
    | Enter the routes name to enable dynamic imagecache manipulation.
    | This handle will define the first part of the URI:
    |
    | {route}/{template}/{filename}
    |
    | Examples: "images", "img/cache"
    |
    */

    'route' => 'ic',

    /*
    |--------------------------------------------------------------------------
    | Storage paths
    |--------------------------------------------------------------------------
    |
    | The following paths will be searched for the image filename, submitted
    | by URI.
    |
    | Define as many directories as you like.
    |
    */

    'paths' => [
        storage_path('app/public/magazine'),
        storage_path('app/public/developments'),
        storage_path('app/public/developments/logo'),
        storage_path('app/public/geographies'),
        storage_path('app/public/profiles'),
        storage_path('app/profiles'),
        storage_path(env('IMAGES_DIR_LISTING')),
        storage_path(env('IMAGES_DIR_LISTING_SNAPSHOT')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Manipulation templates
    |--------------------------------------------------------------------------
    |
    | Here you may specify your own manipulation filter templates.
    | The keys of this array will define which templates
    | are available in the URI:
    |
    | {route}/{template}/{filename}
    |
    | The values of this array will define which filter class
    | will be applied, by its fully qualified name.
    |
    */

    'templates' => [
        'small' => 'Intervention\Image\Templates\Small',
        'medium' => 'Intervention\Image\Templates\Medium',
        'large' => 'Intervention\Image\Templates\Large',
        'crm-list' => 'App\Helpers\ImageFilters\CRMListFilter',
        'list-item' => 'App\Helpers\ImageFilters\ListItemFilter', // 334x170, 238x160 single - items on right
        'icon' => 'App\Helpers\ImageFilters\IconFilter',// 375x275, 310x250 -photo gallery 860
        'gallery-small' => 'App\Helpers\ImageFilters\GallerySmallFilter',// 375x275, 310x250 -photo gallery 860
        'gallery-medium' => 'App\Helpers\ImageFilters\GalleryMediumFilter',// 500x275
        'gallery-normal' => 'App\Helpers\ImageFilters\GalleryNormalFilter',// 600x400, 493x328
        'gallery-large' => 'App\Helpers\ImageFilters\GalleryLargeFilter',// 768x400
        'gallery-square' => 'App\Helpers\ImageFilters\GallerySquareFilter', // 550x500, 652x500
        'gallery-square-small' => 'App\Helpers\ImageFilters\GallerySquareSmallFilter', // 130x120
        'geography-home' => 'App\Helpers\ImageFilters\GeographiesHomepageFilter', // 250x340
        'geography-list' => 'App\Helpers\ImageFilters\GeographiesPageFilter', // 676x340
        'geography-page-header' => 'App\Helpers\ImageFilters\GeographyPageHeaderFilter', // 1200x350
        'development-big' => 'App\Helpers\ImageFilters\DevelopmentBigFilter', // 1200x350
        'development-home' => 'App\Helpers\ImageFilters\DevelopmentHomeFilter',
        'development-card-logo' => 'App\Helpers\ImageFilters\DevelopmentCardLogoFilter', // 100x50 max, preserves aspect ratio
        'listing-big' => 'App\Helpers\ImageFilters\ListingBigFilter', // 1200x900
        'square-150' => 'App\Helpers\ImageFilters\Square150Filter', // 150x150
        'agent-big' => 'App\Helpers\ImageFilters\AgentBigFilter',
        'agent-normal' => 'App\Helpers\ImageFilters\AgentNormalFilter',
        'agent-mobile-big' => 'App\Helpers\ImageFilters\AgentMobileBigFilter',
        'og' => 'App\Helpers\ImageFilters\OGFilter', // 1200x630

        'mobile-list-item' => 'App\Helpers\ImageFilters\MobileListItemFilter',
        'mobile-list-item-small' => 'App\Helpers\ImageFilters\MobileListItemSmallFilter',
        'mobile-listing-big' => 'App\Helpers\ImageFilters\MobileListingBigFilter',
        'website-mobile-1' => 'App\Helpers\ImageFilters\WebsiteMobile1Filter', // 130 x 120px
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Cache Lifetime
    |--------------------------------------------------------------------------
    |
    | Lifetime in minutes of the images handled by the imagecache route.
    |
    */

    'lifetime' => 518400,
];
