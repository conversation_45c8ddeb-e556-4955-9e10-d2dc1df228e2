<?php

namespace App\Console\Commands;

use App\Services\InventoryService;
use Illuminate\Console\Command;

class UnpublishOlderListings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'listings:unpublish-older-listings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unpublish listings older than 3 months';

    protected $inventoryService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(InventoryService $inventoryService)
    {
        parent::__construct();
        $this->inventoryService = $inventoryService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->inventoryService->takeDownOlderSnapshots();
        return 0;
    }
}
