<?php

namespace App\Console\Commands;

use App\Models\Crm\RolesDef;
use App\Models\QueryParamsDef;
use App\Services\EmailService;
use App\Services\InventoryService;
use Illuminate\Console\Command;
use App\Services\PropertiesService;
use DB;

class SendAgentReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send bi-monthly reminder to agents';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    private $propertiesService;
    public function __construct(EmailService $emailService, PropertiesService $propertiesService, InventoryService $inventoryService)
    {
        parent::__construct();
        $this->emailService = $emailService;
        $this->propertiesService = $propertiesService;
        $this->inventoryService = $inventoryService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $agentData = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', RolesDef::AGENT)
            ->select(['email', 'users.id', 'users.name'])
            ->get();

        foreach ($agentData as $data) {
            $properties = $this->propertiesService->searchLite([QueryParamsDef::PROPERTY_CREATED_BY => $data->id], null, ['p.updated_at', 'DESC'], 100);
            $agentListings = $properties->items();
            if (count($agentListings) > 0) {
                $this->emailService->sendAgentReminders($data, $properties->items());
            }
        }
    }
}
