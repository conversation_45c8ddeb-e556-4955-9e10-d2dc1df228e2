<?php

namespace App\Console\Commands;

use App\Models\Crm\Reminder;
use App\Services\EmailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SendNoteReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:send-note-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send note reminders email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $nowMinute = date('Y-m-d H:i');

        $reminders = DB::select(DB::raw("
            SELECT
                *,
                DATE_FORMAT(r.due_date, '%Y-%m-%d') as due_date
            FROM
                reminders as r
            WHERE
                DATE_FORMAT(r.send_email_date, '%Y-%m-%d %H:%i') BETWEEN '$nowMinute' AND DATE_ADD('$nowMinute', INTERVAL 6 MINUTE)
                AND r.sent_at IS NULL
                AND r.send_email = 1
                AND r.deleted_at IS NULL
        "));
    
        foreach ($reminders as $reminder) {
            $destinationEmail = $reminder->email;
            $this->emailService->sendAgentNoteReminders($reminder, $destinationEmail);
            Reminder::where('id', $reminder->id)->update(['sent_at' => $nowMinute]);
        }
    }
}
