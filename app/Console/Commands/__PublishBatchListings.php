<?php
//
//namespace App\Console\Commands;
//
//use App\Models\Property;
//use App\Services\InventoryService;
//use Illuminate\Console\Command;
//
//class PublishBatchListing extends Command
//{
//    private $inventoryService;
//
//    public function __construct(InventoryService $inventoryService) {
//        $this->inventoryService = $inventoryService;
//    }
//    /**
//     * The name and signature of the console command.
//     *
//     * @var string
//     */
//    protected $signature = 'images:create-snapshots';
//
//    /**
//     * The console command description.
//     *
//     * @var string
//     */
//    protected $description = 'Create snapshots for listings - batch';
//
//
//    /**
//     * Execute the console command.
//     *
//     * @return int
//     */
//    public function handle()
//    {
//        $itemId = 0;
//
//        while($itemId < 1991) {
//            $dbItem = Property::where('id', $itemId)->first();
//            if(!is_null($dbItem)) {
//                $this->inventoryService->snapshot($dbItem);
//            }
//            $itemId++;
//        }
//    }
//}
