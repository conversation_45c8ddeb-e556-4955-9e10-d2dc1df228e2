<?php

namespace App\Console\Commands;

use App\Models\CacheKeys;
use App\Models\Property;
use App\Services\InventoryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class SnapshotBatchCreate extends Command
{
    private $inventoryService;

    public function __construct(InventoryService $inventoryService) {
        $this->inventoryService = $inventoryService;
        parent::__construct();
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'snapshot:batch-create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create snapshots for listings - batch';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $itemId = 0;

        while($itemId < 1991) {
            $dbItem = Property::where('id', $itemId)->first();
            if(!is_null($dbItem) && !empty($dbItem->ref_no)) {
                $snapshot = $this->inventoryService->snapshot($dbItem);
                foreach (['eur', 'usd', 'gbp', 'qar'] as $currency) {
                    foreach (['', 'en', 'ar'] as $locale) {
                        Cache::forget(CacheKeys::HOMEPAGE_RECENT_PROPERTIES_MAP."_".$currency."_".$locale);
                        Cache::forget(CacheKeys::SNAPSHOT_PART.$snapshot->listing_id."_".$locale);
                    }
                }
            }
            $itemId++;
        }
    }
}
