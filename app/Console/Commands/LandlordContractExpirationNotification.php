<?php

namespace App\Console\Commands;

use App\Models\Contact;
use App\Services\EmailService;
use Illuminate\Console\Command;
use DB;

class LandlordContractExpirationNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'landlord:contract-expiration-notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send an email 1 MONTH before a contract expires. - contract end date';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $expiringLandlordContracts = Contact::where('contact_type', 'landlord')->whereRaw("DATE_FORMAT(contract_end_date, '%Y-%m-%d') LIKE DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-%d')")->select('id', 'record_no', 'name')->get();
        if($expiringLandlordContracts->count() > 0) {
            $this->emailService->sendLandlordContractExpiringNotification($expiringLandlordContracts);
        }
    }
}
