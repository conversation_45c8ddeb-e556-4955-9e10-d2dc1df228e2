<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateLeadUtmFromMetadata extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leads:update-utm-from-metadata {--chunk=100 : Number of records to process per chunk}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update leads UTM fields from lead_metadata JSON';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Set memory limit to avoid memory exhaustion
        ini_set('memory_limit', '512M');

        $chunkSize = $this->option('chunk');
        $totalProcessed = 0;
        $totalUpdated = 0;

        $this->info('Starting to process leads with lead_metadata...');
        Log::info('Starting to process leads with lead_metadata...');

        // Get total count for progress tracking
        $totalCount = DB::table('leads')
            ->whereNotNull('lead_metadata')
            ->where(function ($query) {
                $query->whereNull('utm_source')
                    ->orWhereNull('utm_campaign')
                    ->orWhereNull('utm_medium')
                    ->orWhereNull('utm_content');
            })
            ->count();

        $this->info("Found {$totalCount} leads to process");
        Log::inf("Found {$totalCount} leads to process");

        // Process in chunks to avoid memory issues
        DB::table('leads')
            ->whereNotNull('lead_metadata')
            ->where(function ($query) {
                $query->whereNull('utm_source')
                    ->orWhereNull('utm_campaign')
                    ->orWhereNull('utm_medium')
                    ->orWhereNull('utm_content');
            })
            ->orderBy('id')
            ->chunkById($chunkSize, function ($leads) use (&$totalProcessed, &$totalUpdated) {
                foreach ($leads as $lead) {
                    $totalProcessed++;
                    
                    // Skip if lead_metadata is not valid JSON
                    if (empty($lead->lead_metadata)) {
                        continue;
                    }
                    
                    // Decode the lead_metadata
                    $metadata = json_decode($lead->lead_metadata, true);
                    
                    // Skip if metadata is not an array
                    if (!is_array($metadata)) {
                        continue;
                    }
                    
                    // Extract UTM parameters
                    $utmSource = $metadata['utm_source'] ?? null;
                    $utmMedium = $metadata['utm_medium'] ?? null;
                    $utmCampaign = $metadata['utm_campaign'] ?? null;
                    $utmContent = $metadata['utm_content'] ?? null;
                    
                    // Skip if no UTM parameters found
                    if (!$utmSource && !$utmMedium && !$utmCampaign && !$utmContent) {
                        continue;
                    }
                    
                    // Prepare update data
                    $updateData = [];
                    
                    if ($utmSource && empty($lead->utm_source)) {
                        $updateData['utm_source'] = $utmSource;
                    }
                    
                    if ($utmMedium && empty($lead->utm_medium)) {
                        $updateData['utm_medium'] = $utmMedium;
                    }
                    
                    if ($utmCampaign && empty($lead->utm_campaign)) {
                        $updateData['utm_campaign'] = $utmCampaign;
                    }
                    
                    if ($utmContent && empty($lead->utm_content)) {
                        $updateData['utm_content'] = $utmContent;
                    }
                    
                    // Skip if no updates needed
                    if (empty($updateData)) {
                        continue;
                    }
                    
                    // Update the lead
                    DB::table('leads')
                        ->where('id', $lead->id)
                        ->update($updateData);
                    
                    $totalUpdated++;
                    
                    // Show progress every 100 records
                    if ($totalProcessed % 100 === 0) {
                        $this->info("Processed {$totalProcessed} leads, updated {$totalUpdated}");
                        Log::info("Processed {$totalProcessed} leads, updated {$totalUpdated}");
                    }
                }
            });
        
        $this->info("Completed! Processed {$totalProcessed} leads, updated {$totalUpdated}");
        Log::info("Completed! Processed {$totalProcessed} leads, updated {$totalUpdated}");
        
        return 0;
    }
}
