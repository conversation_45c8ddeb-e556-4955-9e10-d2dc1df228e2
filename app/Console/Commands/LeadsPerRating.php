<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\StatsReportService;
use App\Services\EmailService;
use Log;

class LeadsPerRating extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-leads-per-rating-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send number of leads per agent for each rating';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(StatsReportService $statsReportService, EmailService $emailService)
    {
        $leadStats = $statsReportService->getAgentLeadsStats();
        $emailService->sendRatedLeadsReport($leadStats);
        Log::info('Rated leads report sent.');
    }
}
