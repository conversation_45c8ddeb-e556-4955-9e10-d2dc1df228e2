<?php

namespace App\Console\Commands;

use App\Services\ContactsService;
use Illuminate\Console\Command;

use Mail;

class TenantsExpiringListR<PERSON><PERSON> extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenants:expiring:reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send an email containing the tenants which has the contracts near end';

    protected $contactsService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ContactsService $contactsService)
    {
        parent::__construct();
        $this->contactsService = $contactsService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $tenants = $this->contactsService->getExpiringTenantsWithinPeriod();
        Mail::send(
            'crm.emails.tenants-expiry-reminder',
            ['tenants' => $tenants],
            function ($m) {
                $m->from('<EMAIL>', 'FG Realty CRM');
                $m->to(env('EXPIRING_TENANTS_EMAIL_TO'))->subject('[FG Realty CRM] Reminder: Expiring tenant contracts');
            }
        );
    }
}
