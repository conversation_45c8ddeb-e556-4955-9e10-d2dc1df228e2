<?php

namespace App\Console\Commands;

use App\Services\BrokerLandlordsService;
use Illuminate\Console\Command;

class ImportLandlordsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:landlords';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import the landlords from the old DB to the new one';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $brokerLandlordsService;
    public function __construct(BrokerLandlordsService $brokerLandlordsService)
    {
        parent::__construct();
        $this->brokerLandlordsService = $brokerLandlordsService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->brokerLandlordsService->importFromOldTable();
    }
}
