<?php

namespace App\Console\Commands;

use App\Services\ContactsService;
use Illuminate\Console\Command;

class SyncContactsToNewsletterList extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:fgr-contacts-newsletters';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync all contacts from website (contact forms, agent forms) with mailpoet subscriber list';

    private $contactsService;
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ContactsService $contactsService)
    {
        parent::__construct();
        $this->contactsService = $contactsService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->contactsService->syncContactsWithBlogMailpoetNewsletterLists();
        return 0;
    }
}
