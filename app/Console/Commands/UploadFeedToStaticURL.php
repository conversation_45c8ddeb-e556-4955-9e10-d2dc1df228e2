<?php

namespace App\Console\Commands;

use App\Services\ExportService;
use App\Services\PropertiesService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use View;
use Log;

class UploadFeedToStaticURL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fgrealty:upload-feed-to-static-url';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload propertyoryx feed to static url';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $propertiesService;
    private $exportService;

    public function __construct(PropertiesService $propertiesService, ExportService $exportService)
    {
        parent::__construct();
        $this->propertiesService = $propertiesService;
        $this->exportService = $exportService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $tag = 'propertyoryx';
        $platform = $this->exportService->getExportPlatformByTag($tag);
        if(is_null($platform)) {
            abort(404);
        }
        $items = $this->propertiesService->getExportQueueItems($platform)->each(function($item)  {
            if(!empty($item->location_gparent)) {
                $item->city = $item->location_gparent;
                $item->region = $item->location_parent;
                $item->area = $item->location;
            } else if(!empty($item->location_parent)) {
                $item->city = $item->location_parent;
                $item->region = $item->location;
            } else if(!empty($item->location)) {
                $item->city = $item->location;
            }
            $item->title =  str_replace('&', 'and', $item->title);
            $item->images = is_array($item->images) ? $item->images : json_decode($item->images);
            if(isset($item->primary_image)) {
                $item->images = $this->propertiesService->sortSnapshotImages($item->images, $item->primary_image);
            }
            $item->frequency = $item->ad_type == 'rent' ? 'MONTHLY' : '';
        });

        $template = $platform->xml_template_name;
        $csvContent = View::make($template, compact('items'))->render();
        // $fileNameStr = "feed.xml";
        // $date = new \DateTime();

        $disk = Storage::disk('bs_ftp');
        $disk->getDriver()->getAdapter()->getConnection()->setOption(FTP_OPT_DEBUG, true);

        Storage::disk('bs_ftp')->put('feed1.xml', $csvContent);
        Log::info("All done?");
        
        // $ftp = ftp_connect(env('BS_FTP_HOST'));
        // $login_result = ftp_login($ftp, env('BS_FTP_USERNAME'), env('BS_FTP_PASSWORD'));
        // ftp_set_option($ftp, FTP_USEPASVADDRESS, false);
        // ftp_pasv($ftp, true);
        // ftp_chdir($ftp, "feed");
        // ftp_chdir($ftp, "propertyoryx");
        // Log::info("Login result " . (!!$login_result));
        // if (!!$login_result) {
        //     $fSetup = tmpfile();
        //     fwrite($fSetup, $csvContent);
        //     fseek($fSetup, 0);
        //     if (!ftp_fput($ftp, $fileNameStr, $fSetup, FTP_ASCII)) {
        //         Log::info("Cannot write to FTP");
        //         throw ("Cannot write to ftp: " . $date->format("Y-m-d H:i:s"));
        //     } else {
        //         Log::info("Sent to FTP server: " . $fileNameStr . " with " . count($items) . " lines.");
        //     }
        //     fclose($fSetup);
        // } else {
        //     Log::info("Cannot login to Charisma FTP ");
        //     throw ("Cannot login to Charisma ftp");
        // }
        // dd($csvContent);
        // $queue = $platform->queue;

        // if(is_null($platform->queue)) {
        //     $queue = new ExportQueue();
        //     $queue->export_platform_id = $platform->id;
        // }
        // $this->exportService->flushQueue($queue, $contents);
    }
}
