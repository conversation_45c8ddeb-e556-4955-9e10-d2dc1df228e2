<?php

namespace App\Console\Commands;

use App\Imports\PearlOwnersColImport;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Contact;
use App\Services\ContactsService;
use App\Services\OperationHistoryService;
use Log;

class ImportPearlOwners extends Command
{
    protected $signature = 'import:pearl-owners {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import owners from an Excel file';

    public function handle(ContactsService $contactsService, OperationHistoryService $operationHistoryService)
    {
        // Performing a hard delete on contacts with id greater than 75411
        $deletedCount = Contact::where('id', '>', 76413)->forceDelete();
        // 73822 users
        
        Log::info("Hard deleted $deletedCount contacts where id > 76413.");

        // Confirming file existence
        $file = $this->argument('file');
        if (!file_exists($file)) {
            $this->error('The file does not exist.');
            return 1;
        }

        // Attempting the import
        try {
            // Excel::import(new PearlOwnersImport($contactsService, $operationHistoryService), $file);
            Excel::import(new PearlOwnersColImport($contactsService, $operationHistoryService), $file);
            $this->info('Pearl owners imported successfully!');
        } catch (\Exception $e) {
            $this->error('Error during import: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
