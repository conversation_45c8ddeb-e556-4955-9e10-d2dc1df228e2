<?php

namespace App\Console\Commands;

use App\Imports\Landlords2Import;
use App\Models\Contact;
use App\Models\ContactLandlordData;
use App\Services\BrokerLandlordsService;
use App\Services\OperationHistoryService;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;
use DB;
use Illuminate\Support\Facades\Log;

class ImportLandlords2Command extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:landlords2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import the landlords from The Pearl Landlords.xls file';

    // after space was replaced
    private $phoneFieldWords = [
        "son",
        "(Owner)",
        "(Owner'sStaff)",
        "<PERSON>(Owner'srepresentative)",
        "wife",
        "Wife",
        "(Mr.<PERSON><PERSON>)",
        "(<PERSON><PERSON>)",
        "(sir)",
        "(Mr.<PERSON>)",
        "(<PERSON>)",
        "(driver)",
        "driver",
        "Home1:",
        "Mobile1:",
        "Mobile2:",
        "Work1:",
        "(<PERSON><PERSON><PERSON>)",
        "(wife)",
        "Fax1:",
        "Fax:",
        "Work:",
        "cook",
        "sister",
        "WorkU<PERSON><PERSON>",
        "(maam)",
        "(<PERSON>Manager)",
        "Home:",
        "<PERSON>:",
        "(secretaryonly)",
        "Secretary'scontactno",
        "(Faisal)",
        "(Darwish)",
        "-<PERSON>salam",
        "<PERSON>usalam<PERSON>ep",
        "Abdurahman(brother)",
        "represintative",
        "(<PERSON>ra-Sis)",
        "Undercapstone",
        "Khalid-son",
        "Aiman",
        "moustafa",
        "Bilal",
        "C/oMr.Abdulla Abdulgani",
        "FernandoDriver",
        "Ning",
        "(Tahar)",
        "c/oFuadJebera",
        "raquel",
        "-Mohamed-",
        "new",
        "SaajidAhmed",
        "c/oMr.Hamad",
        "(Maryam",
        "Faxno.",
        "Mubarak",
        "shama/Sheriff",
        "-shama",
        "Mubarak",
        "<EMAIL>",
        "(owner'srep)",
        "(son)/Nazzar",
        "Heidi",
        "Beju)Natalie",
        "oman",
        "Khalid",
        "-Khalid-son",
        "AbdusalamRep/Abdurahman(brother)",
        "Wafa",
        "Undercapstone",
        "moustafa-",
        "(faxno)",
        "(Bilal)",
        "C/oMr.AbdullaAbdulgani",
        "FernandoDriver/Ning",
        "(Owner'sStaff)",
        "Maureen(Owner's representative)-",
        "(Mr.Hamad)",
        "(Mr.Younes)",
        "(Carol)",
        "(Shamim)",
        "(maam)",
        "(EliasManager)",
        "(secretaryonly)",
        "Secretary'scontactno:",
        "(Faisal)",
        "(Darwish)",
        "(Tahar)",
        "raquel",
        "Faxno."
    ];

    private $phoneEraseableStrings = [
        "C/o Mr. Abdulla Abdulgani ",
        "new",
        "-Saajid Ahmed",
        "c/o Mr. Hamad ",
        "(Maryam",
        "-Mubarak",
        "/<EMAIL>",
        "-Brother",
        "kaber",
        "(owner's rep)",
        "Heidi",
        "-oman",
        "-A/Fe/Evelyn",
        "-rep(mohammed)",
        "ramjet)secretary",
        "(personal#)",
        "(husband)",
        "-Shaji-Driver",
        "owners representative ",
        "Yuksel/",
        "Yuksel",
        "(updated contact)",
        "abdulla  owner",
        " C/O MUHAMMED KHALIL",
        "(Chinese)",
        "(Mr. Ramzi)",
        "MR.RASHIS",
        "Owner's Rep.-",
        "(Ms. Celine)",
        "aboud (",
        " ext:29",
        "Secretary's contact no: ",
        "Abdullah Sultan "
    ];

    private $phoneToSeparatorString = [
        "Abdusalam Rep/Abdurahman(brother)",
        "/moustafa-",
        "-Fernando Driver/Ning-",
        "-Mohamed-",
        "-shama/Sheriff",
        "Beju)Natalie(",
        "-noor-",
        "(fax #)/Jaflah-",
        "-D/",
        "-Mary / ",
        "-Carmen/",
        "/Rihab/",
        "&",
        ";"
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $brokerLandlordsService;
    private $operationHistoryService;
    public function __construct(BrokerLandlordsService $brokerLandlordsService, OperationHistoryService $operationHistoryService)
    {
        parent::__construct();
        $this->brokerLandlordsService = $brokerLandlordsService;
        $this->operationHistoryService = $operationHistoryService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $sheetColumns = [
            'unitno', 'category', 'name', 'contact_number', 'unit_type'
        ];

        $sheetsMap = [
            0 => "PA 3",
            1 => "PA 4",
            2 => "PA 5",
            3 => "PA 6",
            4 => "PA 7",
            5 => "PA 9",
            6 => "PA 10",
            7 => "PA 11",
            8 => "PA 13",
            9 => "PA 14",
            10 => "PA 16",
            11 => "PA 19",
            12 => "PA 21",
            13 => "PA 24",
            14 => "VB 27",
            15 => "VB 28",
            16 => "PA 29"
        ];

        $towersMap = [
            $sheetsMap[0] => 502,
            $sheetsMap[1] => 503,
            $sheetsMap[2] => 504,
            $sheetsMap[3] => 505,
            $sheetsMap[4] => 506,
            $sheetsMap[5] => 507,
            $sheetsMap[6] => 508,
            $sheetsMap[7] => 509,
            $sheetsMap[8] => 510,
            $sheetsMap[9] => 511,
            $sheetsMap[10] => 512,
            $sheetsMap[11] => 513,
            $sheetsMap[12] => 514,
            $sheetsMap[13] => 515,
            $sheetsMap[14] => 516,
            $sheetsMap[15] => 517,
            $sheetsMap[16] => 518,
        ];

        // dd($towersMap);
        $sheetData = $this->readExcel();

        $allContactData = [];

        foreach ($sheetsMap as $sheetIndex => $towerName) {
            $towerId = $towersMap[$sheetsMap[$sheetIndex]];
            foreach ($sheetData[$sheetIndex] as $dataRow) {
                if (in_array(strtolower(trim($dataRow['category'])), ['owner', 'corporate manager', 'representative', 'merchant', 'assistant'])) {
                    $names = $this->getContactFullNames($dataRow);
                    $phoneData = $this->getPhoneData($dataRow);
                    $contactData = [
                        'name' => $names[0],
                        'prefix_mobile_1' => $phoneData["prefix"],
                        'mobile_1' => $phoneData["phone"],
                        'prefix_mobile_2' => '',
                        'mobile_2' => '',
                        'towerData' => [],
                        'all_names' => [],
                        'other_phones' => [],
                    ];
                    if (count($names) > 1) {
                        for ($i = 1; $i < count($names); $i++) {
                            $contactData['all_names'][] = $names[$i];
                        }
                    }
                    if (count($phoneData['otherPhones'])) {
                        foreach ($phoneData['otherPhones'] as $phoneEntryIndex => $phoneEntryData) {
                            if ($phoneEntryIndex == 0) {
                                $contactData['prefix_mobile_2'] = $phoneEntryData['prefix'];
                                $contactData['mobile_2'] = $phoneEntryData['phone'];
                            } else {
                                $contactData['other_phones'][] = $phoneEntryData;
                            }
                        }
                    }
                    $unitNumbers = $this->getUnitNumbers($dataRow);
                    $unitType = $this->getUnitType($dataRow);
                    foreach ($unitNumbers as $index => $unitNumber) {
                        $towerArr = [
                            'tower_id' => $towerId,
                            'unit_type' => $unitType,
                            'unit_no' => $unitNumber
                        ];
                        $contactData['towerData'][] = $towerArr;
                    }
                    // $contactData['allData'] = json_encode($dataRow);
                    $contactData['rowData'] = $dataRow;

                    $allContactData[] = $contactData;
                    // if($sheetIndex == 16 && in_array("1315", $unitNumbers)) {
                    //     print_r($contactData);
                    // }
                }
            }
            // die;

            // if ($sheetIndex == 11) {
            //     print_r($sheetData[$sheetIndex]);
            //     die;
            // }
        }

        $existingContactsCounter = 0;
        $newContactsCounter = 0;
        foreach ($allContactData as $contactData) {
            $phone = $contactData['mobile_1'];
            $dbContact = DB::table('contacts')->select([
                "id",
            ])->where(function ($qb) use ($phone) {
                $qb->whereRaw("REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(phone_1, '-', ''), ')', ''), '(', ''), '+', ''), ' ', '') LIKE ?", $phone)
                    ->orWhereRaw("REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(phone_2, '-', ''), ')', ''), '(', ''), '+', ''), ' ', '') LIKE ?", $phone)
                    ->orWhereRaw("REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(mobile_1, '-', ''), ')', ''), '(', ''), '+', ''), ' ', '') LIKE ?", $phone)
                    ->orWhereRaw("REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(mobile_2, '-', ''), ')', ''), '(', ''), '+', ''), ' ', '') LIKE ?", $phone);
            })->first();

            if (is_null($dbContact)) {
                $newContactsCounter++;
                Log::info($newContactsCounter . ". -> Add contact to db [" . $contactData['name'] . "]");
                // create contact
                $contactObject = new Contact();
                $contactObject->record_no = $this->brokerLandlordsService->getNextLandlordNoForUser();
                $contactObject->contact_type = Contact::LANDLORD;
                $contactObject->name = $contactData['name'];
                $contactObject->prefix_mobile_1 = $contactData['prefix_mobile_1'];
                $contactObject->mobile_1 = $contactData['mobile_1'];
                $contactObject->prefix_mobile_2 = $contactData['prefix_mobile_2'];
                $contactObject->mobile_2 = $contactData['mobile_2'];
                $contactObject->save();
                $landlordData = new ContactLandlordData();
                $landlordData->contact_id = $contactObject->id;
                $landlordData->save();
                foreach ($contactData['towerData'] as $towerRecord) {
                    $contactObject->landlordData->towers()->attach($towerRecord['tower_id'], [
                        'unit_no' => $towerRecord['unit_no'],
                        'unit_type' => $towerRecord['unit_type'],
                    ]);
                }
                // Database tag
                $contactObject->tags()->sync([17]);
                $this->operationHistoryService->addOperationHistory($contactObject, $this->rowToString($contactData['rowData']));
            } else {
                Log::info("Getting data for contact " . $dbContact->id);
                $contactObject = Contact::with(['landlordData', 'landlordData.towers'])->where('id', $dbContact->id)->first();

                if (is_null($contactObject->landlordData)) {
                    $landlordData = new ContactLandlordData();
                    $landlordData->contact_id = $contactObject->id;
                    $contactObject->landlordData()->save($landlordData);
                    $landlordData->save();
                    $contactObject->load(['landlordData', 'landlordData.towers']);
                    Log::info("Created LL Data");
                    
                }

                foreach ($contactData['towerData'] as $towerRecord) {
                    $existingRecord = null;
                    foreach ($contactObject->landlordData->towers as $t) {
                        if ($t->pivot->unit_no == $towerRecord['unit_no']) {
                            $existingRecord = $t;
                            break;
                        }
                    }
                    if (is_null($existingRecord)) {
                        $contactObject->landlordData->towers()->attach($towerRecord['tower_id'], [
                            'unit_no' => $towerRecord['unit_no'],
                            'unit_type' => $towerRecord['unit_type'],
                        ]);
                    }
                }

                // add a history line
                $this->operationHistoryService->addOperationHistory($contactObject, $this->rowToString($contactData['rowData']));
                $existingContactsCounter++;
                Log::info($existingContactsCounter . ". -> This contact already exists [" . $contactData['name'] . "]");
            }
        }
        Log::info("Finished the import from The Pearl Landlords file");
    }

    private function readExcel()
    {
        $path = app_path('../data/tpll.xlsx');
        $array = FacadesExcel::toArray(new Landlords2Import, $path);
        return $array;
    }

    private function rowToString($rowData)
    {
        $str = "Data synced from external source. ";
        foreach ($rowData as $key => $val) {
            $str .= $key . ": " . (empty($val) ? '-' : $val) . "\r\n";
        }
        return $str;
    }

    private function getPhoneData($dataRow)
    {
        $phones = ["prefix" => "+974", "phone" => "", "otherPhones" => []];
        $phoneParts = [];
        $contactNumber = str_replace($this->phoneEraseableStrings, "", $dataRow['contact_number']);
        $contactNumber = str_replace($this->phoneToSeparatorString, "/", $contactNumber);
        $phone1Parts = explode("/", $contactNumber);
        foreach ($phone1Parts as $phonePart) {
            $part = trim($phonePart);
            $subparts = explode(",", $part);
            foreach ($subparts as $subpart) {
                $validChunk = trim($subpart);
                $phoneParts[] = $validChunk;
            }
        }

        foreach ($phoneParts as $index => $phoneStr) {
            $s1Phone = str_replace(" ", "", $phoneStr);
            if (str_contains($s1Phone, "Fax:")) {
                continue;
            }
            $s1Phone = str_replace($this->phoneFieldWords, "", $s1Phone);
            $s2Phone = str_replace([" ", "(", ")", '-'], "", $s1Phone);

            $phonePrefix = "+974";
            if (\Str::startsWith($s2Phone, "+1514") && strlen($s2Phone) > 9) {
                $phonePrefix = "+1";
                $s2Phone = substr($s2Phone, 2, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "+")) {
                $phonePrefix = substr($s2Phone, 0, 4);
                $s2Phone = substr($s2Phone, 4, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "00")) {
                $phonePrefix = substr($s2Phone, 0, 5);
                $phonePrefix = str_replace("00", "+", $phonePrefix);
                $s2Phone = substr($s2Phone, 5, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "33") && strlen($s2Phone) > 10) {
                $phonePrefix = "+33";
                $s2Phone = substr($s2Phone, 2, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "852") && strlen($s2Phone) > 10) {
                $phonePrefix = "+852";
                $s2Phone = substr($s2Phone, 3, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "44") && strlen($s2Phone) > 10) {
                $phonePrefix = "+44";
                $s2Phone = substr($s2Phone, 2, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "966") && strlen($s2Phone) > 9) {
                $phonePrefix = "+966";
                $s2Phone = substr($s2Phone, 3, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "974") && strlen($s2Phone) > 9) {
                $phonePrefix = "+974";
                $s2Phone = substr($s2Phone, 3, strlen($s2Phone));
            }
            if (\Str::startsWith($s2Phone, "971") && strlen($s2Phone) > 9) {
                $phonePrefix = "+971";
                $s2Phone = substr($s2Phone, 3, strlen($s2Phone));
            }

            if (empty($phones["phone"])) {
                $phones["phone"] = $s2Phone;
                $phones["prefix"] = $phonePrefix;
            } else {
                $phoneData = [
                    "phone" => $s2Phone,
                    "prefix" => $phonePrefix
                ];
                $phones["otherPhones"][] = $phoneData;
            }
        }

        return $phones;
    }

    private function getContactFullNames($dataRow)
    {
        $names = [];
        $dataRowContent = str_replace(";", "/", $dataRow['name']);
        $name1Parts = explode("/", $dataRowContent);
        foreach ($name1Parts as $namePart) {
            $part = trim($namePart);
            $subparts = explode(",", $part);
            foreach ($subparts as $subpart) {
                $validChunk = trim($subpart);
                $names[] = $validChunk;
            }
        }

        return $names;
    }

    public function getUnitNumbers($dataRow): array
    {
        if (strtolower($dataRow['unitno']) == "n/a") {
            return ["N/A"];
        }
        $unitNos = explode(",", str_replace("/", ",", $dataRow['unitno']));
        $sanitizedUnitNos = [];
        foreach ($unitNos as $unitNo) {
            $sanitizedUnitNos[] = trim($unitNo);
        }
        return $sanitizedUnitNos;
    }

    public function getUnitType($dataRow)
    {
        return $dataRow['unit_type'] ?? '';
    }

    private function sanitizePhoneNumber($number)
    {
        // Remove any non-numeric characters from the phone number
        $number = preg_replace('/\D/', '', $number);

        // If you know the length of your numbers, uncomment and adjust the following line:
        // $number = substr($number, -10);

        return $number;
    }
}
