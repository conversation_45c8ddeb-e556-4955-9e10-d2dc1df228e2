<?php

namespace App\Console\Commands;

use App\Services\PropertyFinderSyncService;
use Illuminate\Console\Command;
use Log;

set_time_limit(0);

class SyncSnapshotsToPFPortal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:pf-snapshots';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync snapshots to PropertyFinder portal';

    private $pfSyncService;
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(PropertyFinderSyncService $propertyFinderSyncService)
    {
        parent::__construct();
        $this->pfSyncService = $propertyFinderSyncService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('[PF Snapshot Sync Job] Started');
        $this->pfSyncService->syncNextBatchOfSnapshots();
        Log::info('[PF Snapshot Sync Job] Ended');
        return 0;
    }
}
