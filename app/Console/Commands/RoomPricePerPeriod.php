<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;

class RoomPricePerPeriod extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leisure:room-price-per-period {startDate} {endDate} {hotelId} {roomTypeId} {ratePlanId} {pricePer1P} {pricePer2P}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add prices for room anad rate plan for a specific period';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $startDate = $this->argument('startDate');
        $endDate = $this->argument('endDate');
        $hotelId = $this->argument('hotelId');
        $roomTypeId = $this->argument('roomTypeId');
        $ratePlanId = $this->argument('ratePlanId');
        $pricePer1P = $this->argument('pricePer1P');
        $pricePer2P = $this->argument('pricePer2P');

        $period = new \DatePeriod(
            new \DateTime($startDate),
            new \DateInterval('P1D'),
            (new \DateTime($endDate))->modify('+1 day') // Inclusive end date
        );

        foreach ($period as $date) {

            $formattedDate = $date->format('Y-m-d');
            // Convert the DateTime object to a string if needed
            // $exists = DB::table('hotel_room_type_prices')  // Replace 'your_table_name' with your actual table name
            //     ->where('date', $formattedDate)
            //     ->where('hotelId', $hotelId)
            //     ->where('roomTypeId', $roomTypeId)
            //     ->where('ratePlanId', $ratePlanId)
            //     ->exists();

            // if (!$exists) {
            //     // Insert a new record if it doesn't exist
            //     DB::table('hotel_room_type_prices')->insert([
            //         'date' => $formattedDate,
            //         'hotelId' => $hotelId,
            //         'roomTypeId' => $roomTypeId,
            //         'ratePlanId' => $ratePlanId,
            //         'onePersonPrice' => $pricePer1P,
            //         'twoPersonPrice' => $pricePer2P,
            //     ]);

            //     echo "Inserted for date: $formattedDate \n";
            // } else {
            //     echo "Record already exists for date: $formattedDate \n";
            // }

            DB::table('hotel_room_type_prices')->updateOrInsert(
                [
                    'date' => $formattedDate,
                    'hotelId' => $hotelId,
                    'roomTypeId' => $roomTypeId,
                    'ratePlanId' => $ratePlanId,
                ],
                [
                    'onePersonPrice' => $pricePer1P,
                    'twoPersonPrice' => $pricePer2P,
                ]
            );

            echo "Updated or inserted for date: $formattedDate \n";
        }

        // var_dump($period);

        return 0;
    }
}
