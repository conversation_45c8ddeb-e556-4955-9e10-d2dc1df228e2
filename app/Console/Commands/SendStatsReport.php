<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use App\Services\MenuHelperService;
use App\Services\StatsReportService;
use App\Services\PropertiesService;
use Illuminate\Console\Command;
use App\Models\Lead;

class SendStatsReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:send-stats-report {reportType=monthly}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send weekly and monthly stats report to <PERSON>an';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    private $reportService;
    private $propertiesService;
    public function __construct(EmailService $emailService, StatsReportService $reportService, PropertiesService $propertiesService)
    {
        parent::__construct();
        $this->emailService = $emailService;
        $this->reportService = $reportService;
        $this->propertiesService = $propertiesService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $destinationEmail = env('EMAIL_ADDRESS_SERBAN');

        $reportType = $this->argument('reportType');
        $reportType = $reportType == 'monthly' ? 'monthly' : 'weekly';

        $allAgents = getAllFGRAgents(false, false, true);
        $agentInfo = [];
        $propertiesCreated = $this->reportService->getPropertiesCreated($reportType);
        $propertiesUpdated = $this->reportService->getPropertiesUpdated($reportType);
        $createdLeads = $this->reportService->getLeadsPerAuthor($reportType);
        $leadsFromWebsite = $this->reportService->getLeadsFromWebsite($reportType);
        $assignedLeads = $this->reportService->getAssignedLeadsPerUser($reportType);
        $reassignedLeads = $this->reportService->getReassignedLeadsPerUser($reportType);
        $hotLeads = $this->reportService->getLeadsPerUserByStatus($reportType, null, null, Lead::LEAD_STATUS_HOT);
        $warmLeads = $this->reportService->getLeadsPerUserByStatus($reportType, null, null, Lead::LEAD_STATUS_WARM);
        $coldLeads = $this->reportService->getLeadsPerUserByStatus($reportType, null, null, Lead::LEAD_STATUS_COLD);
        $convertedLeads = $this->reportService->getConvertedLeadsPerUser($reportType);
        $deals = $this->reportService->getDealsPerAgent($reportType);
        $createdLandlordsInfo = $this->reportService->getCreatedLandlordsPerUser($reportType);
        $agentCommissionForClient = $this->reportService->getAgentCommissionForClient($reportType);
        $agentCommissionForLandlord = $this->reportService->getAgentCommissionForLandlord($reportType);
        $agentApprovedDeals = $this->reportService->getAgentApprovedDeals($reportType);

        $topViewedSnapshotsData = $this->reportService->getTopSnapshotViews($reportType);
        $topViewedSnapshotIds = array_map(function ($currentItem) {
            return $currentItem->item_id;
        }, $topViewedSnapshotsData);
        $topViewedSnapshots = $this->propertiesService->searchLite(['ids' => $topViewedSnapshotIds]);
        foreach ($topViewedSnapshotsData as $data) {
            $snapshotObject = $topViewedSnapshots->getCollection()->firstWhere('listing_id', '=', $data->item_id);
            $data->snapshotObject = null;
            if (!is_null($snapshotObject)) {
                $data->snapshotObject = $snapshotObject;
                $data->publicURL = MenuHelperService::createURLForSearchLiteReturn($snapshotObject, $this->propertiesService);
            }
        }

        if ($reportType == 'monthly') {
            $periodTime = now()->format('F Y');
        } else if ($reportType == 'quarterly') {
            $periodTime = date('M', strtotime('-2 months')) . " to " . date('M') . " " . date("Y");
        } else {
            $saturday = strtotime('last saturday');
            $thursday = strtotime('+6 days', $saturday);
            $periodTime =  date('d M Y', $saturday) . " to " . date('d M Y', $thursday);
        }

        foreach ($allAgents as $agentRow) {
            if (!isset($agentInfo[$agentRow->id])) {
                $agentInfo[$agentRow->id] = [
                    'name' => $agentRow->name,
                    'propertiesCreated' => 0,
                    'propertiesUpdated' => 0,
                    'createdLeads' => 0,
                    'assignedLeads' => 0,
                    'websiteLeads' => 0,
                    'reassignedLeads' => 0,
                    'hotLeads' => 0,
                    'warmLeads' => 0,
                    'coldLeads' => 0,
                    'createdDeals' => 0,

                    // new
                    'convertedDeals' => 0,
                    'convertedLeads' => 0,
                    'createdLandlords' => 0,
                    'agentCommissionForClient' => 0,
                    'agentCommissionForLandlord' => 0,
                    'agentApprovedDeals' => 0
                ];
            }
        }

        foreach ($propertiesCreated as $listingsCreatedRow) {
            if (isset($listingsCreatedRow->id)) {
                if (isset($agentInfo[$listingsCreatedRow->id])) {
                    $agentInfo[$listingsCreatedRow->id]['propertiesCreated'] += $listingsCreatedRow->created_listings;
                }
            }
        }

        foreach ($propertiesUpdated as $listingsUpdatedRow) {
            if (isset($listingsUpdatedRow->id)) {
                if (isset($agentInfo[$listingsUpdatedRow->id])) {
                    $agentInfo[$listingsUpdatedRow->id]['propertiesUpdated'] += $listingsUpdatedRow->updated_listings;
                }
            }
        }

        foreach ($createdLeads as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['createdLeads'] += $leadInfo->created_leads;
                }
            }
        }

        foreach ($leadsFromWebsite as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['websiteLeads'] += $leadInfo->website_leads;
                }
            }
        }

        foreach ($reassignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['reassignedLeads'] += $leadInfo->reassigned_leads;
                }
            }
        }

        foreach ($hotLeads as $hotLead) {
            if (isset($hotLead->id)) {
                if (isset($agentInfo[$hotLead->id])) {
                    $agentInfo[$hotLead->id]['hotLeads'] += $hotLead->leads_by_status_no;
                }
            }
        }

        foreach ($warmLeads as $warmLead) {
            if (isset($warmLead->id)) {
                if (isset($agentInfo[$warmLead->id])) {
                    $agentInfo[$warmLead->id]['warmLeads'] += $warmLead->leads_by_status_no;
                }
            }
        }

        foreach ($coldLeads as $coldLead) {
            if (isset($coldLead->id)) {
                if (isset($agentInfo[$coldLead->id])) {
                    $agentInfo[$coldLead->id]['coldLeads'] += $coldLead->leads_by_status_no;
                }
            }
        }

        foreach ($assignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['assignedLeads'] += $leadInfo->assigned_leads;
                }
            }
        }

        foreach ($deals as $dealInfo) {
            if (isset($dealInfo->id)) {
                if (isset($agentInfo[$dealInfo->id])) {
                    $agentInfo[$dealInfo->id]['createdDeals'] += $dealInfo->created_deals_no;
                }
            }
        }

        foreach ($createdLandlordsInfo as $landlordInfo) {
            if (isset($landlordInfo->user_id)) {
                if (isset($agentInfo[$landlordInfo->user_id])) {
                    $agentInfo[$landlordInfo->user_id]['createdLandlords'] += $landlordInfo->landlords_no;
                }
            }
        }

        foreach ($agentCommissionForClient as $agentClient) {
            if (isset($agentClient->id)) {
                if (isset($agentInfo[$agentClient->id])) {
                    $agentInfo[$agentClient->id]['agentCommissionForClient'] += $agentClient->cashed_commission_client;
                }
            }
        }

        foreach ($agentCommissionForLandlord as $agentLandlord) {
            if (isset($agentLandlord->id)) {
                if (isset($agentInfo[$agentLandlord->id])) {
                    $agentInfo[$agentLandlord->id]['agentCommissionForLandlord'] += $agentLandlord->cashed_commission_landlord;
                }
            }
        }

        foreach ($agentApprovedDeals as $agentApprove) {
            if (isset($agentApprove->id)) {
                if (isset($agentInfo[$agentApprove->id])) {
                    $agentInfo[$agentApprove->id]['agentApprovedDeals'] += $agentApprove->approved_deals_no;
                }
            }
        }

        foreach ($convertedLeads as $agentData) {
            if (isset($agentData->id)) {
                if (isset($agentInfo[$agentData->id])) {
                    $agentInfo[$agentData->id]['convertedLeads'] += $agentData->converted_leads_no;
                }
            }
        }

        $allData = [
            'periodTime' => $periodTime,
            'reportType' => $reportType,
            'agentInfo' => $agentInfo,
            'topViewedSnapshotData' => $topViewedSnapshotsData,
        ];

        $this->emailService->sendStatsReportEmail($destinationEmail, $allData);
    }
}
