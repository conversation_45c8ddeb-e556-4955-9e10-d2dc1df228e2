<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class MoveImagesToNewDirectory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:intervention';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy the images to be loaded with intervention lib';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $imagesCacheDir = public_path('snapshots');
        $targetDir = public_path('snapshot-images');
        $imagesCounter = 0;
        if(File::isDirectory($imagesCacheDir)) {
            $directories = File::directories($imagesCacheDir);
            foreach ($directories as $directoryPath) {
                $dirName = File::name($directoryPath);
                if(is_numeric($dirName)) {
                    $listingId = $dirName;
                    $allFilesInDir = File::allFiles($directoryPath);
                    foreach ($allFilesInDir as $fileInDir) {
                        $filename = $fileInDir->getFilename();
                        if(!str_contains($filename, '-image(')) {
                            $nextFileName = 'snapshot-'.$listingId.'-'.$filename;
                            File::copy($directoryPath.'/'.$filename, $targetDir.'/'.$nextFileName);
                            $imagesCounter++;
                        }
                    }
                }
            }
        }

        echo "Finished moving $imagesCounter images\r\n";
        return 0;
    }
}
