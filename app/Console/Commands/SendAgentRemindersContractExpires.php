<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\Property;

class SendAgentRemindersContractExpires extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:send-reminders-contract-expires';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder to agents two months before the contract expires';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $newDateTime = Carbon::now()->addMonths(2);
        $dateWithTwoMonths = $newDateTime->format('Y-m-d');

        $properties = Property::with(['author'])
            ->where('rent_end_date', $dateWithTwoMonths)
            ->get();

        foreach($properties as $property) {
            $this->emailService->sendAgentRemindersWhenContractExpires($property);
        }
    }
}
