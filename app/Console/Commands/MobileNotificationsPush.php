<?php

namespace App\Console\Commands;

use App\Services\NotificationsService;
use Illuminate\Console\Command;

class MobileNotificationsPush extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:push';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command tries to push the notifications / send the notification needed emails';

    private $notificationsService;
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(NotificationsService $notificationsService)
    {
        parent::__construct();
        $this->notificationsService = $notificationsService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $response = $this->notificationsService->sendNotifications();
        return $response;
    }
}
