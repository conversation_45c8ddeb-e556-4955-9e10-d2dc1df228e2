<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use App\Services\UserService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UserBirthdayReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:birthday-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send emails containing the user that celebrates their birthday in the current day';

    private $userService;
    private $emailService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(UserService $userService, EmailService $emailService)
    {
        parent::__construct();
        $this->userService = $userService;
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $users = $this->userService->getTodaysUserBirthday();
        if($users->count()) {
            $this->emailService->sendBirthdayReminder($users);
            Log::info('Birthday reminder sent for '.$users->count().' user(s)');
        } else {
            Log::info("Birthday reminder email - not sent");
        }
    }
}
