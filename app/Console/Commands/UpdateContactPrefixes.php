<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Contact;
use App\Models\Country;
use Illuminate\Support\Facades\Cache;
use App\Services\OperationHistoryService;
use Illuminate\Support\Facades\Log;

class UpdateContactPrefixes extends Command
{
    protected $signature = 'contacts:update-prefixes {--just_watch}';

    protected $description = 'Update contact phone prefixes based on the mobile number and country phone prefixes';

    protected $operationHistoryService;

    public function __construct(OperationHistoryService $operationHistoryService)
    {
        parent::__construct();
        $this->operationHistoryService = $operationHistoryService; // Inject the service
    }

    public function handle()
    {
        // Set memory limit to avoid memory exhaustion
        ini_set('memory_limit', '512M');

        // Determine if just_watch flag is set
        $justWatch = $this->option('just_watch');
        
        // Counter for changes
        $counter = 0;

        // Get or cache the country phone prefixes for 24 hours
        $prefixes = Cache::remember('country_phone_prefixes', 60 * 60 * 24, function () {
            return Country::pluck('phone_prefix')->toArray();
        });

        // Use chunking to reduce memory usage by processing contacts in batches
        Contact::whereNull('prefix_mobile_1')->whereNotNull('mobile_1')->chunk(100, function ($contacts) use ($prefixes, $justWatch, &$counter) {
            foreach ($contacts as $contact) {
                $this->processContactForNullPrefix($contact, $prefixes, $justWatch, $counter);
            }
        });

        Contact::whereNotNull('prefix_mobile_1')->whereNotNull('mobile_1')->chunk(100, function ($contactsWithPrefix) use ($prefixes, $justWatch, &$counter) {
            foreach ($contactsWithPrefix as $contact) {
                $this->processContactForNonNullPrefix($contact, $prefixes, $justWatch, $counter);
            }
        });

        $this->info('Contact prefixes updated successfully.');
    }

    private function processContactForNullPrefix($contact, $prefixes, $justWatch, &$counter)
    {
        $mobile = $contact->mobile_1;

        // Process only if the mobile number length is greater than 11 characters
        if (strlen($mobile) <= 11) {
            return; // Skip processing if the mobile number is too short
        }

        $cleanMobile = $this->cleanMobileNumber($mobile); // Ensure '00' is converted to '+'

        foreach ($prefixes as $prefix) {
            // Check if the mobile number starts with the prefix
            if (str_starts_with($cleanMobile, $prefix)) {
                $counter++;

                // Correctly remove the exact prefix using str_replace() and remove only the first occurrence of the prefix
                $strippedMobile = preg_replace('/^' . preg_quote($prefix, '/') . '/', '', $cleanMobile);

                // If the prefix is correct, update the database and log the change
                $message = $this->outputChange($contact, $prefix, $strippedMobile, $justWatch, $counter);

                // If just_watch is false, save the changes and log the history
                if (!$justWatch) {
                    $contact->prefix_mobile_1 = $prefix;
                    $contact->mobile_1 = $strippedMobile; // Store the number without the prefix
                    $contact->save();

                    // Log the operation history and output
                    $this->operationHistoryService->addOperationHistory($contact, $message);
                    Log::info($message);
                }

                // Output the message to the terminal
                $this->info($message);

                return; // Exit once the prefix is found and processed
            }
        }

        // No valid prefix found, do nothing
        return;
    }

    private function processContactForNonNullPrefix($contact, $prefixes, $justWatch, &$counter)
    {
        $mobile = $contact->mobile_1;

        // Process only if the mobile number length is greater than 11 characters
        if (strlen($mobile) <= 11) {
            return; // Skip processing if the mobile number is too short
        }

        $cleanMobile = $this->cleanMobileNumber($mobile);

        foreach ($prefixes as $prefix) {
            // Check if the mobile number starts with the prefix
            if (str_starts_with($cleanMobile, $prefix)) {
                if ($contact->prefix_mobile_1 !== $prefix) {
                    $counter++;

                    // Correctly remove the exact prefix using str_replace() and remove only the first occurrence of the prefix
                    $strippedMobile = preg_replace('/^' . preg_quote($prefix, '/') . '/', '', $cleanMobile);

                    // Log the change
                    $message = $this->outputChange($contact, $prefix, $strippedMobile, $justWatch, $counter);

                    if (!$justWatch) {
                        $contact->prefix_mobile_1 = $prefix;
                        $contact->mobile_1 = $strippedMobile;
                        $contact->save();

                        // Log the operation history and output
                        $this->operationHistoryService->addOperationHistory($contact, $message);
                        Log::info($message);
                    }

                    $this->info($message);
                }

                return; // Exit the loop once the prefix is found and processed
            }
        }

        // No valid prefix found, do nothing
        return;
    }

    private function cleanMobileNumber($mobile)
    {
        // Replace leading '00' with '+'
        if (str_starts_with($mobile, '00')) {
            $mobile = preg_replace('/^00/', '+', $mobile);
        }

        return $mobile;
    }

    private function outputChange($contact, $prefix, $cleanMobile, $justWatch, $counter)
    {
        $oldMobile = $contact->mobile_1;
        $newMobile = preg_replace('/^' . preg_quote($prefix, '/') . '/', '', $cleanMobile); // Correctly remove only the first occurrence of the exact prefix
        $message = "$counter: Contact ID: {$contact->id}, Old Prefix: {$contact->prefix_mobile_1}, New Prefix: $prefix, Old Mobile: $oldMobile, New Mobile: $newMobile";

        return $message; // Return the message so it can be passed to OperationHistoryService
    }
}
