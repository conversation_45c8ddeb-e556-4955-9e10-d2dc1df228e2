<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use App\Services\StatsReportService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use DB;

class LeadsMonthlyStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:leads-monthly-stats';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send stats of leads per current month';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(StatsReportService $statsReportService)
    {
        $endDate = Carbon::now();

        if($endDate->format('d') > 26) {
            $startDate = Carbon::now()->setDay(26)->setHour('0')->setMinute(0);
        } else {
            $startDate = Carbon::now()->startOfMonth()->subMonth()->setDay(26);
        }

        $stats = $statsReportService->getLeadsPerCategoriesPerPeriod($startDate, $endDate);

        $this->emailService->sendLeadStatsPerMonth($stats, $startDate, $endDate);
    }
}
