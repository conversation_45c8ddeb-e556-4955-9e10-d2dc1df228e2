<?php

namespace App\Console\Commands;

use App\Models\Lead;
use App\Models\LeadAssignment;
use App\Models\LeadInteractionTracking;
use App\Models\LeadStatus;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;
use App\Services\AuthorizationService;

class LeadsNotUpdated7Days extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leads:handle-not-updated-in-7days';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Moves leads not updated in 7 days to masterlist with NOT_UPDATED status';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    private $authoServ;
    public function __construct(EmailService $emailService, AuthorizationService $authoServ)
    {
        parent::__construct();
        $this->emailService = $emailService;
        $this->authoServ = $authoServ;

    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $this->notifyAgentsBeforeMove();
            $this->moveLeadsToMasterlist();
            Log::info("LeadsNotUpdated7Days command completed successfully");
        } catch (\Exception $e) {
            Log::error("LeadsNotUpdated7Days command failed: " . $e->getMessage());
        }
    }

    private function notifyAgentsBeforeMove()
    {
        $fiveDaysAgo = now()->subDays(5);
        $sevenDaysAgo = now()->subDays(7);

        $leads = Lead::whereHas('latestAssignment', function($query) {
                $query->whereNull('deleted_at');
            })
            ->whereBetween('updated_at', [$sevenDaysAgo, $fiveDaysAgo])
            ->with(['latestAssignment.user', 'contact'])
            ->get();

        $leadsByAgent = [];
        foreach ($leads as $lead) {
            if (!$lead->latestAssignment || !$lead->latestAssignment->user) {
                continue;
            }
            $agentId = $lead->latestAssignment->user->id;
            if (!isset($leadsByAgent[$agentId])) {
                $leadsByAgent[$agentId] = [
                    'agent' => $lead->latestAssignment->user,
                    'leads' => []
                ];
            }
            $leadsByAgent[$agentId]['leads'][] = $lead;
        }

        if (!empty($leadsByAgent)) {
            foreach ($leadsByAgent as $agentData) {
                $agent = $agentData['agent'];
                $agentLeads = $agentData['leads'];
                try {
                    $this->emailService->emailToAgentNotify5daysBeforeMove($agentLeads, $agent);

                    Log::info("Notification email sent for lead ID {$lead->id} (Agent: {$agent->name}) - 48h before move");

                } catch (\Exception $e) {
                    Log::error("Error sending email for lead ID {$lead->id}: " . $e->getMessage());
                }
            }
        }
    }

    private function moveLeadsToMasterlist()
    {
        $notUpdatedStatusId = LeadStatus::where('name', LeadStatus::STATUS_NOT_UPDATED)->first()->id;
        $sevenDaysAgo = now()->subDays(7);

        $leads = Lead::whereHas('latestAssignment', function($query) {
                $query->whereNull('deleted_at');
            })
            ->where(function($query) use ($sevenDaysAgo) {
                $query->where('updated_at', '<=', $sevenDaysAgo)
                    ->orWhereNull('updated_at');
            })
            ->with(['latestAssignment.user', 'contact'])
            ->get();

        foreach ($leads as $lead) {
            if (!$lead->latestAssignment || !$lead->latestAssignment->user) {
                continue;
            }
            $agent = $lead->latestAssignment->user;
            try {
                $lead->lead_status_id = $notUpdatedStatusId;
                $lead->save();

                $lead->latestAssignment->delete();

                $action = LeadInteractionTracking::MOVED_TO_MASTERLIST_7DAYS;
                $this->authoServ->leadInteractionsTracking($lead->id, $action, null);

                Log::info("Lead ID {$lead->id} moved to masterlist with NOT_UPDATED status due to 7 days of inactivity (Agent: {$agent->name})");

            } catch (\Exception $e) {
                Log::error("Error moving lead ID {$lead->id} to masterlist: " . $e->getMessage());
            }
        }
    }
}
