<?php

namespace App\Console\Commands;

use App\Services\AttachmentsService;
use Illuminate\Console\Command;

class MoveImagesToNewStructure extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:move-structure';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Move images to new structure';

    private $attachmentsService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(AttachmentsService $attachmentsService)
    {
        $this->attachmentsService = $attachmentsService;
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        return $this->attachmentsService->moveImagesToNewStructure();
    }
}
