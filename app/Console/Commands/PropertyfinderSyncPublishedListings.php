<?php

namespace App\Console\Commands;

use App\Services\AttachmentsService;
use App\Services\PropertyFinderService;
use Illuminate\Console\Command;

class PropertyfinderSyncPublishedListings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'propertyfinder:sync-published-listings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync published listings';

    private $propertyFinderService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(PropertyFinderService $propertyFinderService)
    {
        $this->propertyFinderService = $propertyFinderService;
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        return $this->propertyFinderService->syncPublishedListings();
    }
}
