<?php

namespace App\Console\Commands;

use App\Models\Language;
use App\Models\PropertySnapshotTranslation;
use App\Models\PropertyTranslation;
use Illuminate\Console\Command;
use Log;

class ImportTranslations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:translations {translationPart=title}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import translations for listings';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $tranlsationFile = app_path('../data/listing-title-translation-14.11.txt');

        $handle = fopen($tranlsationFile, "r");
        $count = 0;
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                // if($count < 40) {
                    $lineData = explode("\t", $line);
                    // print_r($lineData);
                    $listingId = $lineData[0];
                    $snapshotId = $lineData[1];
                    $titleTranslation = $lineData[2];

                    // //listing translation
                    $lt = PropertyTranslation::where('property_id', $listingId)->where('language', Language::AR)->first();
                    if(!is_null($lt)) {
                        if(empty(trim($lt->title))) {
                            $lt->title = $titleTranslation;
                            $lt->save();
                        } else {
                            Log::info("Skip adding translation for ".$listingId);
                        }
                    } else {
                        $lt = new PropertyTranslation();
                        $lt->property_id = $listingId;
                        $lt->title = $titleTranslation;
                        $lt->language = Language::AR;
                        $lt->save();
                        Log::info("ADDED translation for ".$listingId);
                    }

                    // snapshot translations
                    $st = PropertySnapshotTranslation::where('snapshot_id', $snapshotId)->where('language', Language::AR)->first();
                    if(!is_null($st)) {
                        if(empty(trim($st->title))) {
                            $st->title = $titleTranslation;
                            $st->save();
                        } else {
                            Log::info("Skip adding translation for ".$snapshotId);
                        }
                    } else {
                        $st = new PropertySnapshotTranslation();
                        $st->snapshot_id = $snapshotId;
                        $st->title = $titleTranslation;
                        $st->language = Language::AR;
                        $st->save();
                        Log::info("ADDED translation for ".$snapshotId);
                    }
                // }

                $count++;
            }

            fclose($handle);
        }

    }
}
