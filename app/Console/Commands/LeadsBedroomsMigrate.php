<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;

class LeadsBedroomsMigrate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lead:bedrooms-migrate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Move all the data about bedrooms from the lead request to a new field';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $leads = DB::table('leads')->get();
        foreach ($leads as $lead) {
            if(!is_null($lead->leads_request)) {
                $leadsRequest = json_decode($lead->leads_request, true);
                if (isset($leadsRequest['be'])) {
                    DB::table('leads')
                        ->where('id', $lead->id)
                        ->update(['lead_bedrooms_filter' => json_encode($leadsRequest['be'])]);
                    }
            }
        }
    }
}
