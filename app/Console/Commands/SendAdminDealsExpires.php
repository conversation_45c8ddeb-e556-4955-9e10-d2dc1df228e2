<?php

namespace App\Console\Commands;

use App\Models\Crm\Deal;
use App\Services\EmailService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SendAdminDealsExpires extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:send-reminders-deals-expires';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send <NAME_EMAIL> two months before the deals expires';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    private $emailService;
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $newDateTime = Carbon::now()->addMonths(2);
        $dateWithTwoMonths = $newDateTime->format('Y-m-d');

        $deals = Deal::with('author')->where('end_date', $dateWithTwoMonths)->get();

        if(!$deals->isEmpty()) {
            $destinationEmail = env('EMAIL_CONTRACTS');
            $authorName = '';
            $this->emailService->sendAdminRemindersWhenDealsExpires($deals, $destinationEmail, $authorName);
    
            $authorIds = $deals->pluck('author.id')->unique();
            foreach($authorIds as $authorId) {
                $userDeals = $deals->where('created_by', $authorId);
                $destinationEmail = $userDeals->first()->author->email;
                $authorName = $userDeals->first()->author->name;
                $this->emailService->sendAdminRemindersWhenDealsExpires($userDeals, $destinationEmail, $authorName);
            }
        }
    }
}
