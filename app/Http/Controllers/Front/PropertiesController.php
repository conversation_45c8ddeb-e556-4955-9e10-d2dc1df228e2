<?php

namespace App\Http\Controllers\Front;

use App\Models\Attribute;
use App\Models\CacheKeys;
use App\Models\ItemView;
use App\Models\Language;
use App\Models\Property;
use App\Models\PropertySnapshot;
use App\Models\PropertySnapshotTranslation;
use App\Models\PropertyTranslation;
use App\Models\QueryParamsDef;
use App\Models\RouteListingItem;
use App\Models\Timings;
use App\Models\User;
use App\Models\Contact;
use App\Models\Development;
use App\Models\Geography;
use App\Services\AttributesService;
use App\Services\DOMParserService;
use App\Services\GeographyService;
use App\Services\JsonLdService;
use App\Services\MenuHelperService;
use App\Services\PropertiesSearchParserService;
use App\Services\PropertiesService;
use App\Services\SEOContentMetaBuilderService;
use App\Services\SEOContentService;
use App\Services\StatsReportService;
use App\Services\SyncRateService;
use App\Services\CountriesService;
use Illuminate\Support\Str;
use Storage;
use Share;
use Cache;
use DB;
use Log;

class PropertiesController extends FrontController
{
    protected $geographyService;
    protected $propertiesSearchParserService;
    protected $propertiesService;
    private $attributesService;
    private $lastRates;
    private $reportService;
    private $countriesService;
    private $menuHelperService;

    public function __construct(
        CountriesService              $countriesService,
        AttributesService             $attributesService,
        PropertiesService             $propertiesService,
        GeographyService              $geographyService,
        SyncRateService               $syncRateService,
        PropertiesSearchParserService $propertiesSearchParserService,
        SEOContentService $SEOContentService,
        SEOContentMetaBuilderService $SEOContentMetaBuilderService,
        JsonLdService $jsonLdService,
        StatsReportService $reportService,
        MenuHelperService $menuHelperService
    ) {
        parent::__construct($SEOContentService, $geographyService, $SEOContentMetaBuilderService, $jsonLdService);
        $this->propertiesService = $propertiesService;
        $this->countriesService = $countriesService;
        $this->propertiesSearchParserService = $propertiesSearchParserService;
        $this->attributesService = $attributesService;
        $this->geographyService = $geographyService;
        $this->lastRates = $syncRateService->getRates();
        $this->reportService = $reportService;
        $this->menuHelperService = $menuHelperService;
    }

    public function singleOld($locale, $propertyWord, $id, $refNo)
    {
        $snapshot = $this->propertiesService->getSnapshot($id, $refNo, $locale);
        if (!is_null($snapshot)) {
            $routeListingItem = new RouteListingItem();
            $routeListingItem->id = $snapshot->listing_id;
            $routeListingItem->ref_no = $snapshot->ref_no;
            $routeListingItem->ad_type = $snapshot->ad_type;
            $routeListingItem->geography_slug = '';
            $routeListingItem->bedrooms = intval($snapshot->bedrooms) ?? 0;

            $cachedGeographies = $this->propertiesService->cachedGeographiesMap($locale);
            $cachedPropertyTypesHashmap = $this->propertiesService->cachedPropertyTypesHashmap();

            foreach ($cachedGeographies as $geoSlug => $geoData) {
                if ($geoData['id'] == $snapshot->location_id) {
                    $usedSlug = $geoSlug;
                    if ($locale == Language::AR) {
                        $slugParts = [];
                        if (!is_null($geoData['parent'])) {
                            if (!is_null($geoData['parent']['parent'])) {
                                $slugParts[] = $geoData['parent']['parent']['translation'][$locale]['slug'];
                            }
                            $slugParts[] = $geoData['parent']['translation'][$locale]['slug'];
                        }
                        $slugParts[] = $geoData['translation'][$locale]['slug'];
                        $usedSlug = implode("--", $slugParts);
                    }

                    $routeListingItem->geography_slug = $usedSlug;
                }
            }

            foreach ($cachedPropertyTypesHashmap as $_ => $propertyTypeData) {
                if ($propertyTypeData['id'] == $snapshot->property_type_id) {
                    $routeListingItem->property_type_url = $propertyTypeData['filter_value'];
                }
            };
            $urlToRedirect = MenuHelperService::createListingURL($routeListingItem, $locale);
            return redirect($urlToRedirect, 301);
        } else {
            Log::info("Redirect to homepage - ".request()->url());
            return redirect(route('home.localized'));
            // abort(404);
        }
    }

    public function single($locale, $propertyWord, $operationType, $propertyURL, $agentId = null)
    {
        $propertyURLParts = explode("-", $propertyURL);
        $listingId = array_pop($propertyURLParts);
        $userCurrency = request()->cookie('userCurrency', 'qar');
        Cache::forget(CacheKeys::SNAPSHOT_PART . $listingId . "_" . $locale . "_" . $userCurrency);
        $cachedData = Cache::remember(CacheKeys::SNAPSHOT_PART . $listingId . "_" . $locale . "_" . $userCurrency, Timings::DAY, function () use ($listingId, $userCurrency, $locale) {
            $snapshot = $this->propertiesService->getSnapshot($listingId, null, $locale);
            $snapshotAttributes = null;
            $websiteContent = null;
            $amenities = null;
            $baseAmenities = null;
            $hotelId = null;
            $hotelName = null;
            $roomTypeId = null;
            $isShortStayFlow = false;
            $feesAttributes = null;
            $youtubeId = "";
            $breadcrumbs = [];

            if (!is_null($snapshot)) {
                $owner = User::where('id', $snapshot->created_by)->first();
                $snapshot->owner = $owner;

                $itemPrice = $userCurrency === 'qar' ? $snapshot->price : $this->lastRates->{$userCurrency} * $snapshot->price;
                $snapshot->price = number_format($itemPrice, 2);
                $snapshotAttributes = $this->attributesService->getSnapshotAttributes($snapshot);
                $websiteContent = $snapshotAttributes
                    ->filter(function ($item) {
                        return in_array($item->attribute_group, ['Website-content', 'Basic-Info']);
                    })
                    ->mapWithKeys(function ($item) {
                        return [$item->attribute_definition => $item->value_large];
                    })->toArray();

                $amenities =
                    $snapshotAttributes
                    ->filter(function ($item) {
                        return $item->attribute_group == 'Amenities';
                    })
                    ->map(function ($item) {
                        return $item->attribute_definition;
                    })
                    ->toArray();

                if (!count($amenities)) {
                    if (isset($this->propertiesService->defaultAmenities[$snapshot->location_id])) {
                        $amenities = $this->propertiesService->defaultAmenities[$snapshot->location_id];
                    } else if (isset($this->propertiesService->defaultAmenities[$snapshot->location_parent_id])) {
                        $amenities = $this->propertiesService->defaultAmenities[$snapshot->location_parent_id];
                    }
                }
                
                $feesAttributes = $snapshotAttributes->filter(function($attr) {
                    return in_array($attr->attribute_definition_name, ['property-features-transfer_fee', 'property-features-service_charge']);
                });

                $snapshot->description = '';
                if (isset($websiteContent['Description'])) {
                    $snapshot->description = $websiteContent['Description'];
                }
                if ($locale !== Language::EN && !empty($snapshot->translated_description)) {
                    $snapshot->description = $snapshot->translated_description;
                }
                if ($locale !== Language::EN && !empty($snapshot->translated_title)) {
                    $snapshot->title = $snapshot->translated_title;
                }

                $baseAmenities = $snapshotAttributes
                    ->filter(function ($item) {
                        return in_array($item->attribute_definition_name, ['property-features-bedrooms', 'property-features-bathrooms', 'property-features-build-up-area']);
                    })
                    ->map(function ($item) {
                        return (object)[
                            'attr_value' => $item->value,
                            'definition_name' => str_replace("property-features-", "", $item->attribute_definition_name)
                        ];
                    })
                    ->mapWithKeys(function ($item) {
                        return [$item->definition_name => $item->attr_value];
                    })
                    ->toArray();


                if (!empty($snapshot->embeed_youtube)) {
                    $urlData = parse_url($snapshot->embeed_youtube);
                    if (str_contains($urlData['host'], 'youtube.com') && isset($urlData['query'])) {
                        $query = "";
                        $queryParams = [];
                        if(isset($urlData['query']) && !empty($urlData['query'])) {
                            $query = $urlData['query'];
                            $queryParams = explode('&', $query);
                            foreach ($queryParams as $qp) {
                                if (Str::startsWith($qp, "v=")) {
                                    $youtubeId = str_replace("v=", "", $qp);
                                }
                            }
                        }
                    } else if(!isset($urlData['query']) && str_contains($urlData['path'], 'shorts')) {
                        $youtubeId = trim(str_replace(["/shorts/", "shorts/"], "", $urlData['path']), "/");
                    } else if (str_contains($urlData['host'], 'youtu.be')) {
                        $youtubeId = trim($urlData['path'], "/");
                    }
                }

                $breadcrumbs = $this->SEOContentService->getBreadcrumbsForSnapshot($snapshot, $locale);
                $isShortStayFlow = $snapshot->is_short_stay && request()->has('shortStay') && request()->has('startDate') && request()->has('endDate');
                if ($isShortStayFlow) {
                    $result = DB::table('hotel_room_type as hrt')
                        ->join('hotels as h', 'h.id', '=', 'hrt.hotel_id')
                        ->where(['hrt.listing_id' => $snapshot->listing_id])
                        ->select([
                            'hrt.hotel_id as hotel_id',
                            'h.name as hotel_name',
                            'hrt.room_type_id as room_type_id',
                        ])
                        ->first();
                    if (!is_null($result)) {
                        $hotelId = $result->hotel_id;
                        $hotelName = $result->hotel_name;
                        $roomTypeId = $result->room_type_id;
                    }
                }
            }

            $cachedData = [
                'snapshot' => $snapshot,
                'snapshotAttributes' => $snapshotAttributes,
                'websiteContent' => $websiteContent,
                'amenities' => $amenities,
                'baseAmenities' => $baseAmenities,
                'youtubeId' => $youtubeId,
                'breadcrumbs' => $breadcrumbs,
                'hotelId' => $hotelId,
                'hotelName' => $hotelName,
                'roomTypeId' => $roomTypeId,
                'feesAttributes' => $feesAttributes
            ];

            return $cachedData;
        });

        $snapshot = $cachedData['snapshot'];

        if (is_null($snapshot)) {
            abort(404);
        }

        $amenities = $cachedData['amenities'];
        $baseAmenities = $cachedData['baseAmenities'];
        $websiteContent = $cachedData['websiteContent'];
        $youtubeId = $cachedData['youtubeId'];

        $continueYourSearchParams = [
            QueryParamsDef::OPERATION_TYPE => $snapshot->ad_type,
            QueryParamsDef::PROPERTY_TYPE => $snapshot->property_type_url_value,
            QueryParamsDef::EXCEPT_ID => $snapshot->id,
            QueryParamsDef::COUNTRY_ID => 187
        ];

        $similarPropertiesItems = $this->propertiesService->searchLite(
            $continueYourSearchParams,
            null,
            ['p.updated_at', 'DESC'],
            8,
            $locale
        )->items();

        $similarProperties = [];
        foreach ($similarPropertiesItems as $item) {
            $similarProperties[] = $this->propertiesService->mapSnapshotModelToListItem($item, 'list-item', $locale);
        }

        // location data
        $geography = $this->geographyService->getWithInfoById($snapshot->location_id, $locale);
        $areaAttachmentAssignment = $geography->attachmentAssignments->first();
        $geographyOverview = $geography->websiteContent->filter(function ($wc) {
            return $wc->content_type === 'overview';
        })->first();

        // create an object which contain the translations
        $geographyData = (object) null;
        $geographyData->id = $geography->id;
        $geographyData->name = $geography->name;
        $geographyData->slug = $geography->slug;
        $geographyData->image = $geography->image;
        $geographyData->overviewContent = "";

        if (!is_null($geographyOverview)) {
            $geographyData->overviewContent = $geographyOverview->content;
        }

        if ($locale !== Language::EN) {
            // try to get the translation for geography
            $geographyTranslation = $geography->translations->first() ?? null;
            if (!is_null($geographyTranslation)) {
                $geographyData->name = $geographyTranslation->name;
                $geographyData->slug = $geographyTranslation->slug;
            }

            $geographyOverviewTranlsation = $geography->websiteContentTranslations->filter(function ($wc) {
                return $wc->content_type === 'overview';
            })->first();
            if (!is_null($geographyOverviewTranlsation)) {
                if (!empty($geographyOverviewTranlsation->content)) {
                    $geographyData->overviewContent = $geographyOverviewTranlsation->content;
                }
            }
        }


        $snapshotPublicUrl = url()->current();
        $socialMediaLinks = Share::page($snapshotPublicUrl, __("Hi, I found this listing. Please have a look and let me khow what you think!"))
            ->facebook()
            ->twitter()
            ->linkedin()
            ->whatsapp()
            ->getRawLinks();

        $this->handleMetas(['snapshot' => $snapshot], $locale);

        $locationCoords = $this->geographyService->defaultLocationCoords;
        
        $shouldDisplayBedrooms = !in_array($snapshot->property_type_id, [8, 11, 26, 31]);
        $shouldDisplayBathrooms = $snapshot->property_type_id != 11; // land
        $isShortStay = $snapshot->ad_type == 'rent' && in_array($snapshot->minimum_contract, [0.01, 0.02, 0.03, 0.1]);
        if (!is_null($agentId)) {
            $agent = User::where('id', $agentId)->firstOrFail();
        } else {
            $agent = User::where('id', $snapshot->created_by)->firstOrFail();
        }
        $countries = $this->countriesService->getCountries();
        $development = Development::with(['location', 'translations' => function ($qb) use ($locale) {
            $qb->where('language', $locale);
        }, 'location.translations' => function ($qb) use ($locale) {
            $qb->where('language', $locale);
        }])
            ->where('location_id', '>', 0)
            ->where('is_published', 1)
            ->where('id', $snapshot->project_id)
            ->first();
        $snapshot->development = $development;

        $imagesMedia = '';
        if(!is_null($snapshot->project_id)) {
            $development = Development::where('id', $snapshot->project_id)->first();
            if ($development) {
                $imagesMedia = $development->getMedia('gallery_image')->filter(function ($media) use ($snapshot) {
                    return $media->model_id == $snapshot->project_id && $media->model_type == \App\Models\Development::class;
                });
            }

        }

        $content = [
            'content' => [
                "snapshot" => $snapshot,
                "imagesMedia" => $imagesMedia,
                "youtubeId" => $youtubeId,
                "selectedCurrency" => strtoupper($userCurrency),
                "amenities" => $amenities,
                "baseAmenities" => $baseAmenities,
                'similarProperties' => $similarProperties,

                "descriptionLength" => strlen(strip_tags($snapshot->description)),
                "ldJSONSchema" => '', //$ldJSONSchema,
                "routeName" => request()->route()->getName(),
                "websiteContent" => $websiteContent,
                'geographyData' => $geographyData,
                'areaAttachmentAssignment' => $areaAttachmentAssignment,
                'socialMediaLinks' => $socialMediaLinks,
                'locationsCoords' => $locationCoords,
                'shouldDisplayBedrooms' => $shouldDisplayBedrooms,
                'shouldDisplayBathrooms' => $shouldDisplayBathrooms,
                'isShortStay' => $isShortStay,
                'breadcrumbs' => $cachedData['breadcrumbs'],
                'hotelId' => $cachedData['hotelId'],
                'hotelName' => $cachedData['hotelName'],
                'roomTypeId' => $cachedData['roomTypeId'],
                'snapshotPublicUrl' => $snapshotPublicUrl,
                'agent' => $agent,
                'countries' => $countries,
                'feesAttributes' => $cachedData['feesAttributes']
            ],
            'seo' => [], //$seo,
        ];
        // dd($content['content']['snapshot']);

        ItemView::create([
            'operation_type' => 'snapshot_view',
            'remote_addr' => getenv('REMOTE_ADDR'),
            'item_id' => $snapshot->listing_id,
            'created_at' => now()
        ]);
        return view('front.properties.single', $content['content']);
    }

    public function downloadBrochure($locale, $id)
    {
        $property = Property::where('id', $id)->firstOrFail();
        if ($property->brochure_path) {
            return Storage::download($property->brochure_path, $property->brochure_title);
        }
        return response();
    }

    public function downloadSnapshotBrochure($locale, $id)
    {
        $snapshot = PropertySnapshot::where('id', $id)->firstOrFail();
        if ($snapshot->brochure_path) {
            return Storage::download($snapshot->brochure_path, $snapshot->brochure_title);
        }
        return response();
    }

    public function downloadLayout($locale, $id)
    {
        $property = Property::where('id', $id)->firstOrFail();
        if ($property->layout_path) {
            return Storage::download($property->layout_path, $property->layout_title);
        }
        return response();
    }

    public function downloadSnapshotLayout($locale, $id)
    {
        $snapshot = PropertySnapshot::where('id', $id)->firstOrFail();
        if ($snapshot->layout_path) {
            return Storage::download($snapshot->layout_path, $snapshot->layout_title);
        }
        return response();
    }

    public function allOnMap()
    {
        $all = PropertySnapshot::whereNotNull('geo_point')->get()->map(function ($item) {
            return [
                'title' => $item->title,
                'price' => $item->price,
                'refNo' => $item->ref_no,
                'geo_lat' => empty($item->geo_point) ? null : $item->geo_point->getLat(),
                'geo_lng' => empty($item->geo_point) ? null : $item->geo_point->getLng()
            ];
        });

        return view('map-with-all', ['listings' => $all]);
    }

    public function allListingDescriptions(DOMParserService $dOMParserService)
    {
        $allDescriptions = Attribute::join('properties as p', 'p.asset_id', '=', 'attributes.asset_id')->where(function ($qb) {
            $qb->where('attribute_definition_id', 23);
        })
            ->select(['p.id as listing_id', 'p.asset_id', 'value_large'])
            ->where('p.publishing_status', 'published')
            ->orderBy('p.id', 'desc')
            ->offset(0)
            ->limit(1)
            ->get();

        $tagsToNumbersMap = ['p' => 10, 'h2' => 20, 'h3' => 30, 'h4' => 40, 'li' => 50, 'div' => 60];

        echo "<table border='1' cellspacing='0'>";
        for ($i = 0; $i < count($allDescriptions); $i++) {
            $descObject = $allDescriptions[$i];
            $contentArr = $dOMParserService->parseContent($descObject->value_large, ['p', 'h2', 'h3', 'h4', 'li', 'div']);
            foreach ($contentArr as $index => $lineData) {
                $tr  = "<tr>";
                $tr .= "<td>" . $descObject['listing_id'] . "</td>";
                $tr .= "<td>" . $index . "</td>";
                $tr .= "<td>" . $tagsToNumbersMap[$lineData['tag_name']] . "</td>";
                $tr .= "<td>" . $lineData['content'] . "</td>";
                $tr .= "<td> </td>";
                $tr .= "</tr>";
                echo $tr;
            }
        }
        echo "</table>";
        die;
        // return response($allDescriptions);
    }

    public function fromTranslationFileToDB()
    {
        $tranlsationFile = app_path('../data/trans/107-26-ar.txt');
        $handle = fopen($tranlsationFile, "r");
        $translations = $this->getContentFromTransaltionArray($handle);

        if (!is_null($translations)) {
            foreach ($translations as $propertyId => $arDescriptionContent) {
                //check the description
                $existingTranslation = PropertyTranslation::where('property_id', $propertyId)
                    ->where('language', '=', 'ar')
                    ->first();
                $shouldSaveListingTranslation = false;
                if (is_null($existingTranslation)) {
                    $existingTranslation = new PropertyTranslation();
                    $existingTranslation->property_id = $propertyId;
                    $existingTranslation->title = "";
                    $existingTranslation->description = $arDescriptionContent;
                    $existingTranslation->language = Language::AR;
                    $shouldSaveListingTranslation = true;
                } else {
                    if (empty(trim($existingTranslation->description))) {
                        $existingTranslation->description = $arDescriptionContent;
                        $existingTranslation->language = Language::AR;
                        $shouldSaveListingTranslation = true;
                    }
                }
                if ($shouldSaveListingTranslation) {
                    $existingTranslation->save();
                }

                //check the snapshot description
                $snapshotTranslation = PropertySnapshot::with(['translations' => function ($qb) {
                    $qb->where('language', Language::AR);
                }])
                    ->where('listing_id', $propertyId)
                    ->first();

                if (!is_null($snapshotTranslation)) {
                    $translation = $snapshotTranslation->translations->first();
                    $shouldSave = false;
                    if (is_null($translation)) {
                        $translation = new PropertySnapshotTranslation();
                        $translation->language = Language::AR;
                        $translation->snapshot_id = $snapshotTranslation->id;
                        $translation->description = $arDescriptionContent;
                        $shouldSave = true;
                    } else {
                        // dd($translation);
                        if (is_null($translation->description)) {
                            $translation->description = $arDescriptionContent;
                            $shouldSave = true;
                        }
                    }
                    if ($shouldSave) {
                        $translation->save();
                        dd('st', $translation);
                        dd('Saved', $propertyId);
                    }
                }

                dd('s', $snapshotTranslation);
            }
        }
    }

    private function getContentFromTransaltionArray($handle)
    {
        if ($handle) {
            $numbersToTagsMap = [10 => 'p', 20 => 'h2', 30 => 'h3', 40 => 'h4', 50 => 'li', 60 => 'div'];
            $prevTag = "";

            // echo "<table border='1' cellspacing='0'>";
            while (($line = fgets($handle)) !== false) {
                $lineData = explode("\t", $line);
                $lineContent = "";
                $currentTag = $numbersToTagsMap[intval($lineData[2])];
                if ($currentTag != 'li' && $prevTag == 'li') {
                    // dd($currentTag, $prevTag);
                    $lineContent .= "</ul>";
                }
                if ($currentTag == 'li' && $prevTag != 'li') {
                    $lineContent .= "<ul>";
                }

                if (isset($lineData[3])) {
                    $lineContent .= "<" . $currentTag . ">";
                    $lineContent .= trim($lineData[3]);
                    $lineContent .= "</" . $currentTag . ">";
                    $translations[$lineData[0]] = empty($translations[$lineData[0]]) ? $lineContent : $translations[$lineData[0]] . $lineContent;
                }
                $prevTag = $currentTag;
            }

            return $translations;
        }
        return null;
    }

    public function statsEmulation()
    {
        $reportType = request()->get('reportType', 'monthly') == 'monthly' ? 'monthly' : 'weekly';

        $allAgents = getAllFGRAgents();
        $agentInfo = [];
        $propertiesCreated = $this->reportService->getPropertiesCreated($reportType);
        $propertiesUpdated = $this->reportService->getPropertiesUpdated($reportType);
        $createdLeads = $this->reportService->getLeadsPerAuthor($reportType);
        $leadsFromWebsite = $this->reportService->getLeadsFromWebsite($reportType);
        $assignedLeads = $this->reportService->getAssignedLeadsPerUser($reportType);
        $reassignedLeads = $this->reportService->getReassignedLeadsPerUser($reportType);
        $convertedLeads = $this->reportService->getConvertedLeadsPerUser($reportType);
        $deals = $this->reportService->getDealsPerAgent($reportType);
        $createdLandlordsInfo = $this->reportService->getCreatedLandlordsPerUser($reportType);
        $agentCommissionForClient = $this->reportService->getAgentCommissionForClient($reportType);
        $agentCommissionForLandlord = $this->reportService->getAgentCommissionForLandlord($reportType);

        $topViewedSnapshotsData = $this->reportService->getTopSnapshotViews($reportType);
        $topViewedSnapshotIds = array_map(function ($currentItem) {
            return $currentItem->item_id;
        }, $topViewedSnapshotsData);
        $topViewedSnapshots = $this->propertiesService->searchLite(['ids' => $topViewedSnapshotIds]);
        foreach ($topViewedSnapshotsData as $data) {
            $snapshotObject = $topViewedSnapshots->getCollection()->firstWhere('listing_id', '=', $data->item_id);
            $data->snapshotObject = null;
            if (!is_null($snapshotObject)) {
                $data->snapshotObject = $snapshotObject;
                $data->publicURL = MenuHelperService::createURLForSearchLiteReturn($snapshotObject, $this->propertiesService);
            }
        }

        if ($reportType == 'monthly') {
            $periodTime = now()->format('F Y');
        } else {
            $saturday = strtotime('last saturday');
            $thursday = strtotime('+6 days', $saturday);
            $periodTime =  date('d M Y', $saturday) . " to " . date('d M Y', $thursday);
        }

        foreach ($allAgents as $agentRow) {
            if (!isset($agentInfo[$agentRow->id])) {
                $agentInfo[$agentRow->id] = [
                    'name' => $agentRow->name,
                    'propertiesCreated' => 0,
                    'propertiesUpdated' => 0,
                    'createdLeads' => 0,
                    'assignedLeads' => 0,
                    'websiteLeads' => 0,
                    'reassignedLeads' => 0,
                    'createdDeals' => 0,

                    // new
                    'convertedDeals' => 0,
                    'convertedLeads' => 0,
                    'createdLandlords' => 0,
                    'agentCommissionForClient' => 0,
                    'agentCommissionForLandlord' => 0
                ];
            }
        }

        foreach ($propertiesCreated as $listingsCreatedRow) {
            if (isset($listingsCreatedRow->id)) {
                if (isset($agentInfo[$listingsCreatedRow->id])) {
                    $agentInfo[$listingsCreatedRow->id]['propertiesCreated'] += $listingsCreatedRow->created_listings;
                }
            }
        }

        foreach ($propertiesUpdated as $listingsUpdatedRow) {
            if (isset($listingsUpdatedRow->id)) {
                if (isset($agentInfo[$listingsUpdatedRow->id])) {
                    $agentInfo[$listingsUpdatedRow->id]['propertiesUpdated'] += $listingsUpdatedRow->updated_listings;
                }
            }
        }

        foreach ($createdLeads as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['createdLeads'] += $leadInfo->created_leads;
                }
            }
        }

        foreach ($leadsFromWebsite as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['websiteLeads'] += $leadInfo->website_leads;
                }
            }
        }

        foreach ($reassignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['reassignedLeads'] += $leadInfo->reassigned_leads;
                }
            }
        }

        foreach ($assignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['assignedLeads'] += $leadInfo->assigned_leads;
                }
            }
        }

        foreach ($deals as $dealInfo) {
            if (isset($dealInfo->id)) {
                if (isset($agentInfo[$dealInfo->id])) {
                    $agentInfo[$dealInfo->id]['createdDeals'] += $dealInfo->created_deals_no;
                }
            }
        }

        foreach ($createdLandlordsInfo as $landlordInfo) {
            if (isset($landlordInfo->user_id)) {
                if (isset($agentInfo[$landlordInfo->user_id])) {
                    $agentInfo[$landlordInfo->user_id]['createdLandlords'] += $landlordInfo->landlords_no;
                }
            }
        }

        foreach ($agentCommissionForClient as $agentClient) {
            if (isset($agentClient->id)) {
                if (isset($agentInfo[$agentClient->id])) {
                    $agentInfo[$agentClient->id]['agentCommissionForClient'] += $agentClient->agent_client;
                }
            }
        }

        foreach ($agentCommissionForLandlord as $agentLandlord) {
            if (isset($agentLandlord->id)) {
                if (isset($agentInfo[$agentLandlord->id])) {
                    $agentInfo[$agentLandlord->id]['agentCommissionForLandlord'] += $agentLandlord->agent_landlord;
                }
            }
        }

        foreach ($convertedLeads as $agentData) {
            if (isset($agentData->id)) {
                if (isset($agentInfo[$agentData->id])) {
                    $agentInfo[$agentData->id]['convertedLeads'] += $agentData->converted_leads_no;
                }
            }
        }

        $allData = [
            'periodTime' => $periodTime,
            'reportType' => $reportType,
            'agentInfo' => $agentInfo,
            'topViewedSnapshotData' => $topViewedSnapshotsData,
        ];
        return view('components.mail.stats-report-email', $allData);
    }

    public function checkNextRefNo()
    {
        if (request()->has('ot') && request()->has('t') && request()->has('cId')) {
            $ot = request()->get('ot');
            $t = request()->get('t');
            $cId = request()->get('cId');

            $propertiesService = app()->make(PropertiesService::class);
            $contact = Contact::where('id', $cId)->first();

            if (!is_null($contact)) {
                $refNo = $propertiesService->getNextRefNo($t, $ot);
                return response($refNo . '-' . $contact->record_no);
            }
        }
        return response('Not enough params');
    }

    public function regenerateStats()
    {
        $this->propertiesService->generateSnapshotsStats();
        return "Snapshot stats have been regenerated";
    }

    public function singleShare($locale, $agentSlug, $listingId)
    {
        $snapshot = $this->propertiesService->getSnapshot($listingId);
        $user = User::where('slug', $agentSlug)->first();
        if (!is_null($snapshot) && !is_null($user)) {
            $routeListingItem = new RouteListingItem();
            $routeListingItem->id = $snapshot->listing_id;
            $routeListingItem->ref_no = $snapshot->ref_no;
            $routeListingItem->ad_type = $snapshot->ad_type;
            $routeListingItem->geography_slug = '';
            $routeListingItem->bedrooms = intval($snapshot->bedrooms) ?? 0;

            $cachedGeographies = $this->propertiesService->cachedGeographiesMap();
            foreach ($cachedGeographies as $geoSlug => $geoData) {
                if ($geoData['id'] == $snapshot->location_id) {
                    $usedSlug = $geoSlug;
                    $routeListingItem->geography_slug = $usedSlug;
                }
            }
            $cachedPropertyTypesHashmap = $this->propertiesService->cachedPropertyTypesHashmap();
            foreach ($cachedPropertyTypesHashmap as $_ => $propertyTypeData) {
                if ($propertyTypeData['id'] == $snapshot->property_type_id) {
                    $routeListingItem->property_type_url = $propertyTypeData['filter_value'];
                }
            }

            $listingURL = $this->menuHelperService->createListingURL($routeListingItem, $locale);
            $urlParts = explode("/", $listingURL);
            $listingURLPart = array_pop($urlParts);
            return $this->single($locale, null, null, $listingURLPart, $user->id);
        } else {
            return abort(404);
        }
    }
}
