<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\CrmBaseController;
use App\Models\Lead;
use App\Models\User;
use App\Services\MenuHelperService;
use App\Services\PropertiesService;
use App\Services\StatsReportService;
use Illuminate\Support\Facades\Cache;
use DB;

class ReportsController extends CrmBaseController
{
    public $reportTypes = ['pie' => 'Pie', 'doughnut' => 'Doughnut'];
    public $allColumns = ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"];
    public $columns = [
        /* Jan */
        ["Created Listings", "Updated Listings", "Website Leads", "Created Leads", "Created Deals"],
        /* Feb */
        ["Created Listings", "Updated Listings", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Created Deals", "Converted Leads", "Created Landlord", "Cashed in target"],
        /* March */
        ["Created Listings", "Updated Listings", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Created Deals", "Converted Leads", "Created Landlord", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Created Deals", "Converted Leads", "Created Landlord", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
        ["Created Listings", "Updated Listings", "Created Landlords", "Website Leads", "Created Leads", "Assigned Leads", "Reassigned Leads", "Converted Leads", "Hot Leads", "Warm Leads", "Cold Leads", "Created Deals", "Approved Deals", "Cashed commission client", "Cashed commission landlord"],
    ];
    public $data = [
        /* Jan */
        [
            "Abraj Bay" => [0, 0, 0, 0, 0],
            "Ahmed Selim" => [0, 0, 0, 0, 0],
            "Alja Ulaga Fabcia" => [0, 0, 0, 0, 0],
            "Amir Zahraoui" => [13, 0, 3, 0, 0],
            "Atef Mahmoud" => [0, 0, 0, 0, 0],
            "Beverly Tate" => [32, 0, 24, 19, 0],
            "Cristine Canimo" => [0, 0, 0, 0, 0],
            "David Fayez" => [7, 0, 3, 5, 0],
            "Davronbek Dzhalalov" => [1, 0, 0, 0, 0],
            "Farrukh Khakimov" => [1, 0, 3, 0, 0],
            "FGREALTY Office" => [18, 0, 8, 13, 0],
            "Mahmoud Al Ghali" => [21, 0, 2, 0, 0],
            "Michelle Addo" => [10, 0, 11, 0, 0],
            "Moez Arfaoui" => [1, 0, 0, 0, 0],
            "Mostafa Karout" => [12, 0, 6, 27, 0],
            "Mouen Elfegih" => [0, 0, 0, 0, 0],
            "Neeru Bajwa" => [17, 0, 9, 1, 0],
            "Oussema Mhadhbi" => [0, 0, 0, 0, 0],
            "Serban Gabriel Spirea" => [3, 0, 3, 6, 0],
            "Sofia Alsouri" => [0, 0, 0, 0, 0],
            "Steed Taylor" => [24, 0, 7, 0, 0],
            "Tino Matamba" => [38, 0, 41, 0, 0],
            "Usama Alfazly" => [29, 0, 3, 5, 0]
        ],
        [
            "Achraf Hmercha" => [19, 5, 14, 0, 14, 14, 0, "N/A", 7, "N/A"],
            "Amir Zahraoui" => [5, 1, 2, 0, 2, 2, 0, "N/A", 3, "N/A"],
            "Angela Dlamini" => [12, 2, 3, 0, 4, 4, 0, "N/A", 0, "N/A"],
            "Beverly Tate" => [5, 26, 44, 1, 47, 46, 0, "N/A", 0, "N/A"],
            "David Fayez" => [6, 7, 8, 6, 13, 8, 0, "N/A", 0, "N/A"],
            "Davronbek Dzhalalov" => [0, 0, 0, 0, 0, 0, 0, "N/A", 0, "N/A"],
            "Farrukh Khakimov" => [0, 0, 4, 0, 8, 8, 0, "N/A", 0, "N/A"],
            "FGREALTY Office" => [29, 103, 16, 0, 14, 14, 0, "N/A", 2, "N/A"],
            "Francesco Farina" => [15, 2, 0, 19, 1, 1, 0, "N/A", 0, "N/A"],
            "Gabriela Nicolau" => [6, 0, 0, 7, 7, 0, 0, "N/A", 0, "N/A"],
            "Ghena Salem" => [5, 2, 0, 6, 10, 4, 0, "N/A", 0, "N/A"],
            "Hend Ben Messaoud" => [21, 14, 1, 30, 30, 2, 0, "N/A", 2, "N/A"],
            "Mahmoud Al Ghali" => [17, 21, 1, 2, 5, 3, 0, "N/A", 8, "N/A"],
            'Marta Mazzeo' => [12, 1, 0, 0, 0, 0, 0, "N/A", 0, "N/A"],
            "Michelle Addo" => [3, 41, 3, 5, 14, 9, 0, "N/A", 0, "N/A"],
            "Mostafa Karout" => [21, 59, 21, 38, 56, 23, 0, "N/A", 5, "N/A"],
            "Mouen Elfegih" => [0, 0, 0, 0, 0, 0, 0, "N/A", 0, "N/A"],
            "Neeru Bajwa" => [5, 3, 1, 1, 5, 4, 0, "N/A", 0, "N/A"],
            "Sabri Al Kharraz" => [1, 1, 0, 0, 0, 0, 0, "N/A", 0, "N/A"],
            "Serban Gabriel Spirea" => [0, 0, 7, 3, 6, 6, 0, "N/A", 0, "N/A"],
            "Steed Taylor" => [4, 12, 2, 3, 8, 6, 0, "N/A", 0, "N/A"],
            "Tino Matamba" => [12, 4, 29, 0, 29, 29, 0, "N/A", 0, "N/A"],
            "Usama Alfazly" => [15, 10, 2, 18, 21, 6, 0, "N/A", 7, "N/A"]
        ],
        [
            "Achraf Hmercha" =>    [14, 19, 1, 0, 4, 4, 0, "N/A", 4, 1, 1],
            "Amir Zahraoui" =>    [14, 8, 4,    35,    41,    9,    0,    "N/A",    7,    4,    2],
            "Amira Nader" => [24,    50,  5,    15,    7,    7,    27,    "N/A",    5,    0,    0],
            "Angela Dlamini" => [6,    5,    0,    2,    2,    0,    0,    "N/A",    0,    0, 0],
            "Auji Dasbee" => [0,    0, 0,    0,    0,    0,    0,    "N/A",    0,    0,    0],
            "Beverly Tate" => [32,    26,    8,    21,    25,    10,    0,    "N/A",    1,    2,    1],
            'Chayma Dkhil' => [2,    2,    0,    0,    0,    0,    0,    "N/A",    0,    0,    0],
            "Cooper Zak" => [34,    20,    0,    0,    1,    1,    0,    "N/A",    6,    0,    0],
            "Daniel Hani" => [42,    21,    1,    5,    8,    3,    0,    "N/A",    14,    0,    0],
            "David Fayez" => [19,    9,    3,    4,    12,    9,    0,    "N/A",    0,    1,    1],
            "Davronbek Dzhalalov" =>    [0,    0,    0,    0,    0,    0,    0,    "N/A",    0,    0,    0],
            "Farrukh Khakimov" =>    [0,    0,    0,    0,    9,    9,    0,    "N/A",    0,    9, 1],
            "FGREALTY Office" =>    [37,    328,    3,    0,    3,    3,    0,    "N/A",    4,    1, 15],
            "Gabriela Nicolau" =>    [4,    10,    2,    10,    13,    2,    0,    "N/A",    1,    0,    0],
            "Ghena Salem" =>    [20,    29,    4,    31,    59,    29,    0,    "N/A",    8,    0,    0],
            "Hassan Al Saadi" =>    [3,    3,    0,    0,    0,    0,    0,    "N/A",    0,    0,    0],
            "Hend Ben Messaoud" =>    [7,    25,    7,    13,    29,    17,    0,    "N/A",    1,    0,    2],
            "Louay Soufi" =>    [25,    14,    10,    0,    15,    15,    0,    "N/A",    5,    0,    0],
            "Mahmoud Al Ghali" =>    [16,    35,    3,    0,    4,    4,    0,    "N/A",    12,    1,    3],
            "Marta Mazzeo" =>    [40,    31,    2,    45,    36,    4,    0,    "N/A",    2,    0, 0],
            "Michelle Addo"    => [7,    28,    5,    4,    11,    7,    0,    "N/A",    0,    1,    0],
            "Mohamad Usama" =>     [8,    38,    30,    0,    34,    34,    0,    "N/A",    8,    2,    0],
            "Mostafa Karout" =>    [8,    46,    4,    24,    47,    24,    0,    "N/A",    5,    2,    3],
            "Mouen Elfegih" =>    [0,    0,    0,    0,    0,    0,    0,    "N/A",    0,    0,    0],
            "Ni Bajwa" =>    [7,    3,    4,    7,    15,    8,    0,    "N/A",    3,    1,    1],
            "Nizar Ayachi" =>    [13,    1,    0,    0,    0,    0,    0,    "N/A",    9,    0,    0],
            "Raven Ravanes" =>    [0,    37,    24,    5,    52,    30,    0,    "N/A",    2,    0,    0],
            "Serban Gabriel Spirea" =>    [0,    10,    2,    0,    8,    8,    0,    "N/A",    0,    0,    0],
            "Shiheb Kahlouzi" =>    [11,    2,    1,    0,    2,    2,    0,    "N/A",    9,    0,    0],
            "Steed Taylor" =>    [31,    14,    10,    5,    22,    18,    0,    "N/A",    1,    2,    3],
            "Tino Matamba" =>    [11,    49,    10,    0,    12,    12,    0,    "N/A",    1,    2,    2]
        ],
        [
            "Achraf Hmercha" => [4, 1, 2, 7, 11, 31, 22, 31, 2, 3, 0, 0, 0, 0, 0],
            "Ala Aburayya" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Alexandra Iovu" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Amir Zahraoui" => [12, 2, 6, 3, 12, 31, 20, 31, 8, 9, 0, 0, 1, 0, 0],
            "Angela Dlamini" => [3, 1, 0, 2, 12, 8, 3, 8, 1, 0, 0, 0, 3, 0, 0],
            "Areej Hashem" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Customer Service Representative" => [224, 8, 2, 68, 316, 64, 44, 64, 5, 0, 0, 0, 0, 0, 0],
            "Daniel Hani" => [31, 1, 5, 1, 6, 26, 20, 26, 1, 0, 0, 0, 7, 0, 0],
            "Faheem Miswer" => [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Farrukh Khakimov" => [3, 0, 0, 2, 0, 18, 18, 18, 6, 1, 0, 0, 7, 0, 0],
            "Gabriela Nicolau" => [246, 48, 3, 9, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Ghena Salem" => [1, 0, 0, 4, 15, 25, 14, 25, 6, 10, 0, 0, 0, 0, 0],
            "Ivan Christian" => [39, 219, 12, 12, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0],
            "Jennifer Jane Pascua" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Kareem" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Kevin Campbell" => [28, 2, 1, 0, 18, 20, 2, 20, 0, 3, 0, 0, 1, 0, 0],
            "Marwa Alzadjali" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Marwen Ben Ali" => [33, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 5, 0, 0],
            "Michelle Addo" => [8, 1, 1, 15, 11, 36, 25, 36, 13, 8, 0, 0, 1, 0, 0],
            "Mohamad Usama" => [32, 24, 6, 60, 26, 92, 76, 92, 15, 8, 0, 0, 1, 0, 0],
            "Mostafa Karout" => [40, 4, 4, 48, 88, 568, 488, 568, 6, 17, 0, 0, 1, 0, 0],
            "Ni Bajwa" => [3, 1, 0, 9, 15, 34, 20, 34, 15, 3, 0, 0, 1, 0, 0],
            "Olga Paraschiv" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0],
            "Ruwini Iresha Sandamali" => [4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Sabri El-Kharraz" => [2, 3, 1, 0, 11, 19, 8, 19, 10, 3, 0, 0, 0, 0, 0],
            "Serban Gabriel Spirea" => [36, 15, 0, 15, 225, 18, 12, 18, 0, 0, 0, 0, 1, 0, 0],
            "Steed Taylor" => [4, 1, 1, 2, 16, 23, 16, 23, 2, 4, 0, 0, 3, 0, 0],
            "Tino Matamba" => [4, 4, 0, 17, 2, 33, 31, 33, 1, 2, 0, 0, 0, 0, 0],
            "Zaman Farhan" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
            "Zennyla Bhutia" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        ],
        [
            "Achraf Hmercha" => [4, 3, 2, 3, 8, 26, 18, 26, 3, 4, 0, 0, 0, 0, 0],
            "Ala Aburayya" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Alexandra Iovu" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Amir Zahraoui" => [7, 2, 2, 3, 12, 21, 10, 21, 9, 4, 0, 0, 1, 0, 0],
            "Angela Dlamini" => [3, 1, 0, 0, 0, 6, 6, 6, 1, 4, 0, 0, 3, 0, 0],
            "Areej Hashem" => [32, 6, 8, 0, 2, 10, 8, 10, 0, 0, 0, 0, 0, 0, 0],
            "Customer Service Representative" => [86, 10, 0, 90, 292, 338, 244, 338, 25, 29, 0, 0, 0, 0, 0],
            "Daniel Hani" => [20, 2, 4, 4, 5, 31, 26, 31, 3, 1, 0, 0, 7, 0, 0],
            "Faheem Miswer" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Farrukh Khakimov" => [0, 0, 0, 5, 0, 19, 19, 19, 6, 4, 0, 0, 7, 0, 0],
            "Gabriela Nicolau" => [12, 63, 18, 6, 9, 6, 6, 6, 0, 1, 0, 0, 0, 0, 0],
            "Ghena Salem" => [1, 4, 1, 0, 6, 35, 29, 35, 10, 20, 0, 0, 0, 0, 0],
            "Ivan Christian" => [105, 93, 0, 57, 54, 63, 63, 63, 0, 0, 0, 0, 0, 0, 0],
            "Jennifer Jane Pascua" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Kareem" => [4, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Kevin Campbell" => [13, 6, 0, 1, 6, 28, 22, 28, 0, 3, 0, 0, 1, 0, 0],
            "Marwa Alzadjali" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Marwen Ben Ali" => [9, 0, 0, 1, 21, 39, 20, 39, 3, 0, 0, 0, 5, 0, 0],
            "Michelle Addo" => [0, 1, 0, 12, 3, 36, 33, 36, 16, 6, 0, 0, 1, 0, 0],
            "Mohamad Usama" => [60, 14, 2, 50, 8, 116, 106, 116, 25, 6, 0, 0, 1, 0, 0],
            "Mostafa Karout" => [64, 16, 4, 48, 168, 936, 772, 936, 12, 46, 0, 0, 1, 0, 0],
            "Ni Bajwa" => [0, 0, 0, 6, 7, 28, 21, 28, 12, 2, 0, 0, 1, 0, 0],
            "Olga Paraschiv" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0],
            "Ruwini Iresha Sandamali" => [38, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Sabri El-Kharraz" => [2, 1, 0, 2, 10, 24, 14, 24, 16, 5, 0, 0, 0, 0, 0],
            "Serban Gabriel Spirea" => [24, 9, 0, 30, 249, 42, 27, 42, 0, 0, 0, 0, 1, 0, 0],
            "Steed Taylor" => [16, 0, 0, 2, 11, 26, 19, 26, 0, 5, 0, 0, 3, 0, 0],
            "Tino Matamba" => [2, 17, 0, 20, 2, 48, 46, 48, 16, 3, 0, 0, 0, 0, 0],
            "Zaman Farhan" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
            "Zennyla Bhutia" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        ],
        [],
        [],
        [],
        [
            "Achraf Hmercha" => [1, 13, 6, 2, 43, 41, 1, 43, 0, 0, 0],
            "Amir Zahraoui" => [5, 0, 1, 3, 27, 24, 0, 27, 2, 0, 0],
            "Angela Dlamini" => [1, 5, 0, 0, 26, 26, 0, 26, 0, 0, 0],
            "Areej Hashem" => [5, 1, 1, 8, 59, 51, 6, 59, 0, 0, 0],
            "Beverly Tate" => [8, 11, 3, 2, 28, 26, 2, 28, 0, 0, 0],
            "Daniel Hani" => [27, 7, 7, 12, 62, 50, 23, 62, 2, 0, 0],
            "Farrukh Khakimov" => [4, 2, 0, 0, 25, 25, 5, 25, 0, 0, 0],
            "Gabriela Nicolau" => [0, 23, 0, 1, 1, 1, 0, 1, 3, 0, 0],
            "Ghena Salem" => [3, 33, 0, 6, 23, 17, 0, 23, 0, 0, 0],
            "Hend Ben Messaoud" => [2, 4, 0, 5, 10, 5, 1, 10, 0, 0, 0],
            "Ivan Christian" => [63, 188, 3, 65, 4, 3, 0, 4, 3, 0, 0],
            "Jennifer Jane Pascua" => [6, 11, 2, 0, 0, 0, 0, 0, 26, 0, 0],
            "Kareem" => [0, 3, 0, 34, 1, 1, 0, 1, 0, 0, 0],
            "Kathryn" => [54, 148, 9, 121, 16, 13, 0, 16, 1, 0, 0],
            "Kevin Campbell" => [1, 0, 0, 2, 32, 30, 1, 32, 0, 0, 0],
            "Louay Soufi" => [3, 12, 7, 0, 37, 37, 0, 37, 0, 0, 0],
            "Mahmoud Al Ghali" => [12, 22, 5, 0, 5, 5, 0, 5, 2, 0, 0],
            "Marwa Alzadjali" => [0, 0, 0, 0, 8, 8, 0, 8, 0, 0, 0],
            "Marwen Ben Ali" => [10, 3, 1, 5, 42, 37, 10, 42, 0, 0, 0],
            "Melaina Alderson" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Michelle Addo" => [3, 16, 3, 3, 22, 20, 1, 22, 2, 0, 0],
            "Mohamad Usama" => [6, 2, 0, 3, 19, 16, 5, 19, 3, 0, 0],
            "Mostafa Karout" => [11, 70, 5, 17, 72, 55, 1, 72, 1, 0, 0],
            "Ni Bajwa" => [8, 13, 2, 3, 27, 24, 7, 27, 0, 0, 0],
            "Olga Paraschiv" => [17, 18, 1, 18, 23, 4, 0, 23, 3, 0, 0],
            "Ruwini Iresha Sandamali" => [86, 334, 11, 192, 13, 13, 0, 13, 8, 0, 0],
            "Sabri El-Kharraz" => [3, 1, 0, 3, 25, 22, 0, 25, 1, 0, 0],
            "Serban Gabriel Spirea" => [0, 6, 0, 0, 0, 0, 0, 0, 1, 0, 0],
            "Steed Taylor" => [12, 37, 3, 0, 11, 11, 0, 11, 0, 0, 0],
            "Tino Matamba" => [11, 57, 3, 2, 15, 13, 3, 15, 0, 0, 0]
        ],
        [
            "Achraf Hmercha" => [8, 8, 0, 3, 2, 29, 27, 29, 5, 4, 0, 1, 1, 0, "4500"],
            "Amir Zahraoui" => [2, 2, 0, 0, 0, 22, 22, 22, 6, 6, 0, 1, 1, 0, "7500"],
            "Angela Dlamini" => [5, 12, 0, 0, 0, 20, 20, 20, 2, 12, 0, 2, 2, "10250", "14250"],
            "Beverly Tate" => [4, 11, 0, 3, 1, 27, 26, 27, 4, 2, 0, 0, 1, "3000", "3000"],
            "Daniel Hani" => [4, 2, 0, 0, 6, 49, 43, 49, 17, 7, 0, 4, 8, "37875", "68425"],
            "Faheem Miswer" => [1, 0, 0, 0, 39, 42, 1, 42, 12, 8, 0, 0, 0, 0, 0],
            "Farrukh Khakimov" => [4, 1, 0, 0, 3, 16, 14, 16, 5, 0, 0, 6, 7, "34000", "24625"],
            "Gabriela Nicolau" => [1, 0, 4, 0, 4, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0],
            "Ghena Salem" => [3, 29, 1, 0, 21, 26, 6, 26, 5, 16, 0, 0, 0, 0, 0],
            "Hend Ben Messaoud" => [1, 1, 1, 0, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0],
            "Ivan Christian" => [27, 144, 2, 5, 30, 30, 28, 30, 0, 12, 0, 0, 0, 0, 0],
            "Jennifer Jane Pascua" => [29, 24, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Kareem" => [0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0],
            "Kathryn" => [39, 136, 2, 8, 94, 13, 10, 13, 0, 0, 0, 0, 0, 0, 0],
            "Kevin Campbell" => [4, 1, 0, 0, 0, 15, 15, 15, 5, 4, 0, 3, 3, "10750", "8750"],
            "Marji Aahmed Farook" => [0, 0, 1, 0, 0, 5, 5, 5, 1, 1, 0, 0, 0, 0, 0],
            "Marwa Alzadjali" => [2, 0, 1, 0, 1, 3, 2, 3, 1, 0, 0, 1, 1, 0, "4500"],
            "Marwen Ben Ali" => [5, 9, 1, 1, 0, 47, 47, 47, 1, 14, 0, 8, 7, "18100", "37500"],
            "Michelle Addo" => [4, 19, 2, 3, 8, 48, 41, 48, 17, 14, 0, 5, 2, "27500", "1961"],
            "Mohamad Usama" => [7, 18, 2, 0, 7, 26, 19, 26, 7, 12, 0, 11, 7, "25000", "59725"],
            "Mostafa Karout" => [3, 16, 0, 3, 31, 51, 20, 51, 12, 21, 0, 1, 1, "22780", "11389"],
            "Ni Bajwa" => [8, 3, 0, 3, 0, 15, 15, 15, 9, 1, 0, 2, 4, "19250", "7330"],
            "Olga Paraschiv" => [22, 34, 0, 1, 20, 39, 19, 39, 11, 17, 0, 1, 1, "5538", "6000"],
            "Ruwini Iresha Sandamali" => [64, 344, 2, 14, 151, 18, 18, 18, 0, 0, 0, 0, 1, "5500", 0],
            "Sabri El-Kharraz" => [9, 6, 0, 0, 0, 5, 5, 5, 2, 0, 0, 0, 0, 0, 0],
            "Serban Gabriel Spirea" => [6, 4, 0, 2, 7, 7, 3, 7, 3, 1, 0, 1, 1, "95000", "95000"],
            "Steed Taylor" => [21, 78, 0, 3, 0, 8, 8, 8, 4, 0, 0, 0, 0, 0, 0],
            "Tino Matamba" => [21, 38, 2, 3, 0, 6, 6, 6, 1, 2, 0, 0, 0, 0, 0],
            "Zaman Farhan" => [3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Zennyla Bhutia" => [0, 1, 1, 0, 53, 54, 0, 54, 14, 17, 0, 2, 1, "54902", "21960"],
        ],
        [
            "Achraf Hmercha" => [1, 0, 0, 3, 0, 16, 15, 16, 3, 7, 0, 0, 1, 0, "4500"],
            "Alexandra Iovu" => [4, 2, 0, 0, 4, 5, 2, 5, 2, 2, 0, 0, 0, 0, 0],
            "Amir Zahraoui" => [2, 3, 0, 0, 1, 15, 14, 15, 6, 3, 0, 1, 2, 0, "13025"],
            "Angela Dlamini" => [1, 5, 0, 2, 0, 14, 14, 14, 0, 12, 0, 1, 1, "12750", "6250"],
            "Areej Hashem" => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, "6350", "8050"],
            "Customer Service Representative" => [4, 64, 2, 13, 75, 16, 14, 16, 0, 2, 0, 0, 0, 0, 0],
            "Daniel Hani" => [6, 4, 4, 0, 6, 69, 63, 69, 23, 23, 0, 9, 28, "132975", "150710"],
            "Faheem Miswer" => [0, 0, 0, 0, 28, 35, 5, 35, 4, 14, 0, 0, 0, 0, 0],
            "Farrukh Khakimov" => [4, 0, 0, 0, 1, 26, 25, 26, 12, 5, 0, 6, 11, "34840", "39250"],
            "Gabriela Nicolau" => [0, 3, 2, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0],
            "Ghena Salem" => [2, 32, 0, 0, 7, 12, 5, 12, 3, 7, 0, 0, 0, 0, 0],
            "Hend Ben Messaoud" => [0, 6, 0, 0, 0, 2, 2, 2, 1, 0, 0, 0, 1, "2500", "2500"],
            "Ivan Christian" => [25, 132, 2, 14, 18, 26, 25, 26, 1, 4, 0, 0, 0, 0, 0],
            "Jennifer Jane Pascua" => [4, 7, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Kareem" => [0, 1, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Kevin Campbell" => [6, 5, 0, 0, 0, 26, 26, 26, 11, 2, 0, 0, 4, 13250, "8750"],
            "Marwa Alzadjali" => [0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 1, 0, 4500],
            "Marwen Ben Ali" => [2, 16, 0, 1, 0, 48, 48, 48, 3, 19, 0, 3, 16, 33475, "88730"],
            "Michelle Addo" => [8, 28, 0, 2, 1, 27, 26, 27, 6, 8, 0, 3, 6, "37500", "21461"],
            "Mohamad Usama" => [1, 14, 1, 4, 7, 31, 24, 31, 7, 11, 0, 10, 17, "8000", "89000"],
            "Mostafa Karout" => [6, 77, 0, 2, 34, 52, 21, 52, 19, 15, 0, 2, 4, "55596", "314734"],
            "Ni Bajwa" => [3, 10, 0, 1, 0, 15, 15, 15, 6, 5, 0, 0, 5, 21, 500, "12900"],
            "Olga Paraschiv" => [12, 37, 1, 10, 7, 32, 25, 32, 1, 16, 0, 3, 2, "20538", "21354"],
            "Ruwini Iresha Sandamali" => [51, 349, 7, 25, 146, 59, 56, 59, 0, 2, 0, 0, 1, "5500", 0],
            "Sabri El-Kharraz" => [6, 8, 3, 1, 3, 12, 9, 12, 9, 1, 0, 3, 1, "750000", 0],
            "Serban Gabriel Spirea" => [4, 13, 0, 5, 1, 11, 11, 11, 1, 1, 0, 1, 0, "37867", "18933"],
            "Steed Taylor" => [21, 100, 0, 7, 3, 16, 13, 16, 5, 2, 0, 3, 2, "50470", "45970"],
            "Tino Matamba" => [3, 19, 0, 3, 0, 4, 4, 4, 0, 1, 0, 0, 3, "3250", "15500"],
            "Zaman Farhan" => [9, 6, 3, 1, 0, 26, 26, 26, 1, 15, 0, 0, 0, 0, 0],
            "Zennyla Bhutia" => [0, 0, 2, 0, 43, 47, 3, 47, 13, 12, 0, 0, 0, "27451", "10980"]
        ],
        []
    ];

    private StatsReportService $reportService;
    private PropertiesService $propertiesService;

    public function __construct(
        StatsReportService $reportService,
        PropertiesService $propertiesService
    ) {
        $this->reportService = $reportService;
        $this->propertiesService = $propertiesService;
    }

    public function index()
    {
        $months = [
            0 => "January",
            1 => "February",
            2 => "March",
            3 => "April",
            4 => "May",
            5 => "June",
            6 => "July",
            7 => "Aug",
            8 => "September",
            9 => "October",
            10 => "November",
            11 => "December",
            'all' => 'All'
        ];
        $selectedMonth = request()->query->get('month', 0);
        $selectedMetric = request()->query->get('metric', 0);
        $chartType = request()->query->get('chartType', 'line');
        $metrics = $this->allColumns;

        if (strcmp($selectedMonth, 'all') == 0) {
            // $metrics = $this->allColumns;
            // $metrics = ["Created Listings", "Updated Listings", "Website Leads", "Created Leads"];
            if ($selectedMetric > count($metrics) - 1) {
                $selectedMetric = 0;
            }

            if (!in_array($chartType, ['line', 'bar'])) {
                $chartType = 'line';
            }

            $selectedMetricLabel = $metrics[$selectedMetric];
            $agents = [];
            $totalActivities = [];

            foreach ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11] as $monthIndex) {
                // foreach ([4, 5, 6, 7, 11] as $monthIndex) {
                $usedDate = new \DateTime("2023-" . ($monthIndex + 1) . "-01");
                $this->data[$monthIndex] = Cache::remember('fullReportForMonth' . $monthIndex, 60 * 60, function () use ($usedDate) {
                    return $this->getMonthlyReportData($usedDate);
                });
            }

            foreach ($this->data as $rowIndex => $dataArr) {
                $metricIndex = -1;
                foreach ($this->columns[$rowIndex] as $columnIndex => $columnsLabel) {
                    if ($selectedMetricLabel == $columnsLabel) {
                        $metricIndex = $columnIndex;
                    }
                }
                if ($metricIndex > -1) {
                    foreach ($dataArr as $agentName => $agentDataArr) {
                        if (!in_array($agentName, $agents)) {
                            $agents[] = $agentName;
                            if (!isset($totalActivities[$agentName])) {
                                $totalActivities[$agentName] = [];
                            }
                        }
                    }
                }
            }

            foreach ($this->data as $rowIndex => $dataArr) {
                $metricIndex = -1;
                foreach ($this->columns[$rowIndex] as $columnIndex => $columnsLabel) {
                    if ($selectedMetricLabel == $columnsLabel) {
                        $metricIndex = $columnIndex;
                    }
                }
                // if($metricIndex > -1) {
                foreach ($agents as $agentName) {
                    if (!isset($dataArr[$agentName])) {
                        $totalActivities[$agentName][] = 0;
                    } else {
                        if ($metricIndex > -1) {
                            $totalActivities[$agentName][] = doubleval($dataArr[$agentName][$metricIndex]);
                        } else {
                            $totalActivities[$agentName][] = 0;
                        }
                    }
                }
                // }
            }

            $data = $this->data;
            $chartTypes = [
                'line' => 'Line',
                'bar' => 'Bar'
            ];
        } else {
            if ($selectedMonth > 11) {
                $selectedMonth = 0;
            }
            if ($selectedMetric > count($metrics) - 1) {
                $selectedMetric = 0;
            }
            $selectedMetricLabel = $metrics[$selectedMetric];
            // $metrics = $this->columns[$selectedMonth];
            // $metrics = $this->allColumns;
            $data = $this->data;

            if (in_array($selectedMonth, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11])) {
                // if (in_array($selectedMonth, [4, 5, 6, 7, 11])) {
                $usedDate = new \DateTime("2023-" . ($selectedMonth + 1) . "-01");
                $this->data[$selectedMonth] = Cache::remember('fullReportForMonth' . $selectedMonth, 60 * 60, function () use ($usedDate) {
                    return $this->getMonthlyReportData($usedDate);
                });
            }

            $agents = array_keys($this->data[$selectedMonth]);
            $totalActivities = [];
            foreach ($agents as $agent) {
                $totalActivities[] = doubleval($this->data[$selectedMonth][$agent][$selectedMetric]);
            }
            $chartTypes = [
                'doughnut' => 'Doughnut',
                'pie' => 'Pie',
                'line' => 'Line',
                'bar' => 'Bar'
            ];
        }

        return view('admin.reports.index', compact([
            'months',
            'selectedMonth',
            'selectedMetric',
            'selectedMetricLabel',
            'metrics',
            'data',
            'agents',
            'totalActivities',
            'chartTypes',
            'chartType'
        ]));
    }

    private function getMonthlyReportData($startDate)
    {
        $reportType = 'monthly';

        $allAgents = getAllFGRAgents(false, false, true);
        $agentInfo = [];
        $propertiesCreated = $this->reportService->getPropertiesCreated($reportType, $startDate);
        $propertiesUpdated = $this->reportService->getPropertiesUpdated($reportType, $startDate);
        $createdLeads = $this->reportService->getLeadsPerAuthor($reportType, $startDate);
        $leadsFromWebsite = $this->reportService->getLeadsFromWebsite($reportType, $startDate);
        $assignedLeads = $this->reportService->getAssignedLeadsPerUser($reportType, $startDate);
        $reassignedLeads = $this->reportService->getAgentThatReassignLeads($reportType, $startDate);
        $hotLeads = $this->reportService->getLeadsPerUserByStatus($reportType, $startDate, null, Lead::LEAD_STATUS_HOT);
        $warmLeads = $this->reportService->getLeadsPerUserByStatus($reportType, $startDate, null, Lead::LEAD_STATUS_WARM);
        $coldLeads = $this->reportService->getLeadsPerUserByStatus($reportType, $startDate, null, Lead::LEAD_STATUS_COLD);
        $convertedLeads = $this->reportService->getConvertedLeadsPerUser($reportType, $startDate);
        $deals = $this->reportService->getDealsPerAgent($reportType, $startDate);
        $createdLandlordsInfo = $this->reportService->getCreatedLandlordsPerUser($reportType, $startDate);
        $agentCommissionListingAgent = $this->reportService->getAgentCommissionListingAgent($reportType, $startDate);
        $agentCommissionReferredListingAgent = $this->reportService->getAgentCommissionReferredListingAgent($reportType, $startDate);
        $agentCommissionClosingAgent = $this->reportService->getAgentCommissionClosingAgent($reportType, $startDate);
        $agentCommissionReferredClosingAgent = $this->reportService->getAgentCommissionReferredClosingAgent($reportType, $startDate);
        $agentApprovedDeals = $this->reportService->getAgentApprovedDeals($reportType, $startDate);

        $topViewedSnapshotsData = $this->reportService->getTopSnapshotViews($reportType, $startDate);
        $topViewedSnapshotIds = array_map(function ($currentItem) {
            return $currentItem->item_id;
        }, $topViewedSnapshotsData);
        $topViewedSnapshots = $this->propertiesService->searchLite(['ids' => $topViewedSnapshotIds]);
        foreach ($topViewedSnapshotsData as $data) {
            $snapshotObject = $topViewedSnapshots->getCollection()->firstWhere('listing_id', '=', $data->item_id);
            $data->snapshotObject = null;
            if (!is_null($snapshotObject)) {
                $data->snapshotObject = $snapshotObject;
                $data->publicURL = MenuHelperService::createURLForSearchLiteReturn($snapshotObject, $this->propertiesService);
            }
        }

        if ($reportType == 'monthly') {
            $periodTime = now()->format('F Y');
        } else if ($reportType == 'quarterly') {
            $periodTime = date('M', strtotime('-2 months')) . " to " . date('M') . " " . date("Y");
        } else {
            $saturday = strtotime('last saturday');
            $thursday = strtotime('+6 days', $saturday);
            $periodTime =  date('d M Y', $saturday) . " to " . date('d M Y', $thursday);
        }

        foreach ($allAgents as $agentRow) {
            if (!isset($agentInfo[$agentRow->id])) {
                $agentInfo[$agentRow->id] = [
                    'name' => $agentRow->name,
                    'propertiesCreated' => 0,
                    'propertiesUpdated' => 0,
                    'createdLeads' => 0,
                    'assignedLeads' => 0,
                    'websiteLeads' => 0,
                    'reassignedLeads' => 0,
                    'hotLeads' => 0,
                    'warmLeads' => 0,
                    'coldLeads' => 0,
                    'createdDeals' => 0,

                    // new
                    'convertedDeals' => 0,
                    'convertedLeads' => 0,
                    'createdLandlords' => 0,
                    'agentCommissionListingAgent' => 0,
                    'agentCommissionReferredListingAgent' => 0,
                    'agentCommissionClosingAgent' => 0,
                    'agentCommissionReferredClosingAgent' => 0,
                    'agentApprovedDeals' => 0
                ];
            }
        }

        foreach ($propertiesCreated as $listingsCreatedRow) {
            if (isset($listingsCreatedRow->id)) {
                if (isset($agentInfo[$listingsCreatedRow->id])) {
                    $agentInfo[$listingsCreatedRow->id]['propertiesCreated'] += $listingsCreatedRow->created_listings;
                }
            }
        }

        foreach ($propertiesUpdated as $listingsUpdatedRow) {
            if (isset($listingsUpdatedRow->id)) {
                if (isset($agentInfo[$listingsUpdatedRow->id])) {
                    $agentInfo[$listingsUpdatedRow->id]['propertiesUpdated'] += $listingsUpdatedRow->updated_listings;
                }
            }
        }

        foreach ($createdLeads as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['createdLeads'] += $leadInfo->created_leads;
                }
            }
        }

        foreach ($leadsFromWebsite as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['websiteLeads'] += $leadInfo->website_leads;
                }
            }
        }

        foreach ($reassignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['reassignedLeads'] += $leadInfo->agent_reassigned_leads;
                }
            }
        }

        foreach ($hotLeads as $hotLead) {
            if (isset($hotLead->id)) {
                if (isset($agentInfo[$hotLead->id])) {
                    $agentInfo[$hotLead->id]['hotLeads'] += $hotLead->leads_by_status_no;
                }
            }
        }

        foreach ($warmLeads as $warmLead) {
            if (isset($warmLead->id)) {
                if (isset($agentInfo[$warmLead->id])) {
                    $agentInfo[$warmLead->id]['warmLeads'] += $warmLead->leads_by_status_no;
                }
            }
        }

        foreach ($coldLeads as $coldLead) {
            if (isset($coldLead->id)) {
                if (isset($agentInfo[$coldLead->id])) {
                    $agentInfo[$coldLead->id]['coldLeads'] += $coldLead->leads_by_status_no;
                }
            }
        }

        foreach ($assignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['assignedLeads'] += $leadInfo->assigned_leads;
                }
            }
        }

        foreach ($deals as $dealInfo) {
            if (isset($dealInfo->id)) {
                if (isset($agentInfo[$dealInfo->id])) {
                    $agentInfo[$dealInfo->id]['createdDeals'] += $dealInfo->created_deals_no;
                }
            }
        }

        foreach ($createdLandlordsInfo as $landlordInfo) {
            if (isset($landlordInfo->user_id)) {
                if (isset($agentInfo[$landlordInfo->user_id])) {
                    $agentInfo[$landlordInfo->user_id]['createdLandlords'] += $landlordInfo->landlords_no;
                }
            }
        }

        foreach ($agentCommissionListingAgent as $listingAgent) {
            if (isset($listingAgent->id)) {
                if (isset($agentInfo[$listingAgent->id])) {
                    $agentInfo[$listingAgent->id]['agentCommissionListingAgent'] += $listingAgent->listing_agent_shared_commission;
                }
            }
        }

        foreach ($agentCommissionReferredListingAgent as $referredListingAgent) {
            if (isset($referredListingAgent->id)) {
                if (isset($agentInfo[$referredListingAgent->id])) {
                    $agentInfo[$referredListingAgent->id]['agentCommissionReferredListingAgent'] += $referredListingAgent->referred_listing_agent_shared_commission;
                }
            }
        }

        foreach ($agentCommissionClosingAgent as $closingAgent) {
            if (isset($closingAgent->id)) {
                if (isset($agentInfo[$closingAgent->id])) {
                    $agentInfo[$closingAgent->id]['agentCommissionClosingAgent'] += $closingAgent->closing_agent_shared_commission;
                }
            }
        }

        foreach ($agentCommissionReferredClosingAgent as $referredClosingAgent) {
            if (isset($referredClosingAgent->id)) {
                if (isset($agentInfo[$referredClosingAgent->id])) {
                    $agentInfo[$referredClosingAgent->id]['agentCommissionReferredClosingAgent'] += $referredClosingAgent->referred_closing_agent_shared_commission;
                }
            }
        }

        foreach ($agentApprovedDeals as $agentApprove) {
            if (isset($agentApprove->id)) {
                if (isset($agentInfo[$agentApprove->id])) {
                    $agentInfo[$agentApprove->id]['agentApprovedDeals'] += $agentApprove->approved_deals_no;
                }
            }
        }

        foreach ($convertedLeads as $agentData) {
            if (isset($agentData->id)) {
                if (isset($agentInfo[$agentData->id])) {
                    $agentInfo[$agentData->id]['convertedLeads'] += $agentData->converted_leads_no;
                }
            }
        }

        $allData = [
            'periodTime' => $periodTime,
            'reportType' => $reportType,
            'agentInfo' => $agentInfo,
            'topViewedSnapshotData' => $topViewedSnapshotsData,
        ];

        $mappedAgentInfo = [];
        foreach ($agentInfo as $agentData) {
            $agentName = $agentData['name'];
            if (!isset($mappedAgentInfo[$agentName])) {
                $mappedAgentInfo[$agentName] = [
                    $agentData['propertiesCreated'],
                    $agentData['propertiesUpdated'],
                    $agentData['createdLandlords'],
                    $agentData['websiteLeads'],
                    $agentData['createdLeads'],
                    $agentData['assignedLeads'],
                    $agentData['reassignedLeads'],
                    $agentData['convertedLeads'],
                    $agentData['hotLeads'],
                    $agentData['warmLeads'],
                    $agentData['coldLeads'],
                    $agentData['createdDeals'],
                    $agentData['agentApprovedDeals'],
                    $agentData['agentCommissionListingAgent'],
                    $agentData['agentCommissionReferredListingAgent'],
                    $agentData['agentCommissionClosingAgent'],
                    $agentData['agentCommissionReferredClosingAgent']
                ];
            }
        }

        return $mappedAgentInfo;
    }

    public function reportsIndexNew()
    {


        $months = [
            ['value' => '0', 'label' => 'January'],
            ['value' => '1', 'label' => 'February'],
            ['value' => '2', 'label' => 'March'],
            ['value' => '3', 'label' => 'April'],
            ['value' => '4', 'label' => 'May'],
            ['value' => '5', 'label' => 'June'],
            ['value' => '6', 'label' => 'July'],
            ['value' => '7', 'label' => 'Aug'],
            ['value' => '8', 'label' => 'September'],
            ['value' => '9', 'label' => 'October'],
            ['value' => '10', 'label' => 'November'],
            ['value' => '11', 'label' => 'December'],
            ['value' => 'all', 'label' => 'All'],
        ];

        $agents = getAllAgents();
        return view('crm.dashboard.reports', compact([
            'months',
            'agents',
        ]));
    }

    public function getData()
    {
        $data = request()->validate([
            'year' => '',
            'month' => '',
            'groupType' => '',
            'reportType' => '',
            'chartType' => '',
        ]);

        $selectedYear = $data['year'];
        $selectedMonth = $data['month'];
        if (strcmp($selectedMonth, 'all') == 0) {
            $totalSums = [];
            foreach ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11] as $monthIndex) {
                $usedDate = new \DateTime("$selectedYear-" . ($monthIndex + 1) . "-01");
                $selectedMonthData = Cache::remember('fullReportForMonth' . $selectedYear . $monthIndex, 60 * 60 * 2, function () use ($usedDate) {
                    return $this->getMonthlyReportData($usedDate);
                });
                foreach ($selectedMonthData as $key => $data) {
                    if (!isset($totalSums[$key])) {
                        $totalSums[$key] = array_fill(0, 15, 0);
                    }
                    foreach ($data as $index => $value) {
                        $totalSums[$key][$index] += $value;
                    }
                }
            }
            return $totalSums;
        } else {
            $cacheKey = 'fullReportForMonth' . $selectedYear . $selectedMonth;
            $usedDate = new \DateTime("$selectedYear-" . ($selectedMonth + 1) . "-01");
            $selectedMonthData = Cache::remember($cacheKey, 60 * 60 * 2, function () use ($usedDate) {
                return $this->getMonthlyReportData($usedDate);
            });
            return $selectedMonthData;
        }
    }

    public function getMonthlyCompetitionData($startDate, $agentIds)
    {
        $reportType = 'monthly';
        $allCompetitionAgents = getAllAgentsForContest(false, false, true);

        $contactsCreated = $this->reportService->getContactsCreated($reportType, $startDate);
        $propertiesCreated = $this->reportService->getPropertiesCreated($reportType, $startDate);
        $deals = $this->reportService->getDealsPerAgent($reportType, $startDate);
        $reassignedLeads = $this->reportService->getAgentThatReassignLeads($reportType, $startDate);
        // $websiteLeads = $this->reportService->getLeadsFromWebsitePerUser($reportType, $startDate);
        $selfGeneratedLeads = $this->reportService->getSelfGeneratedLeadsPerUser($reportType, $startDate);
        $visitScheduledLeads = $this->reportService->getVisitScheduledPerUser($reportType, $startDate);
        $cashIn = $this->reportService->getAgentCashIn($reportType, $startDate);
        $verifiedListings = $this->reportService->getVerifiedListings($reportType, $startDate);
        $agentsRating = $this->reportService->getRatingByAgent($reportType, $startDate);


        foreach ($allCompetitionAgents as $agentRow) {
            if (!isset($agentInfo[$agentRow->id])) {
                $agentInfo[$agentRow->id] = [
                    'id' => $agentRow->id,
                    'name' => $agentRow->name,
                    'contactsCreated' => 0,
                    'propertiesCreated' => 0,
                    'reassignedLeads' => 0,
                    'selfGeneratedLeads' => 0,
                    'createdDeals' => 0,
                    'visitScheduledLeads' => 0,
                    'cashIn' => 0,
                    'verifiedListings' => 0,
                    'agentsRating' => 0,
                ];
            }
        }
        foreach ($contactsCreated as $contactsCreatedRow) {
            if (isset($contactsCreatedRow->id)) {
                if (isset($agentInfo[$contactsCreatedRow->id])) {
                    $agentInfo[$contactsCreatedRow->id]['contactsCreated'] += $contactsCreatedRow->created_landlords;
                }
            }
        }
        foreach ($propertiesCreated as $listingsCreatedRow) {
            if (isset($listingsCreatedRow->id)) {
                if (isset($agentInfo[$listingsCreatedRow->id])) {
                    $agentInfo[$listingsCreatedRow->id]['propertiesCreated'] += $listingsCreatedRow->created_listings;
                }
            }
        }
        foreach ($reassignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['reassignedLeads'] += $leadInfo->agent_reassigned_leads;
                }
            }
        }
        foreach ($selfGeneratedLeads as $selfGeneratedLeadRow) {
            if (isset($selfGeneratedLeadRow->id)) {
                if (isset($agentInfo[$selfGeneratedLeadRow->id])) {
                    $agentInfo[$selfGeneratedLeadRow->id]['selfGeneratedLeads'] += $selfGeneratedLeadRow->generated_leads_per_user;
                }
            }
        }
        foreach ($deals as $dealInfo) {
            if (isset($dealInfo->id)) {
                if (isset($agentInfo[$dealInfo->id])) {
                    $agentInfo[$dealInfo->id]['createdDeals'] += $dealInfo->created_deals_no;
                }
            }
        }
        foreach ($visitScheduledLeads as $visitScheduledLeadsRow) {
            if (isset($visitScheduledLeadsRow->id)) {
                if (isset($agentInfo[$visitScheduledLeadsRow->id])) {
                    $agentInfo[$visitScheduledLeadsRow->id]['visitScheduledLeads'] += $visitScheduledLeadsRow->leads_visit_scheduled;
                }
            }
        }
        foreach ($cashIn as $cashInRow) {
            if (isset($cashInRow->id)) {
                if (isset($agentInfo[$cashInRow->id])) {
                    $agentInfo[$cashInRow->id]['cashIn'] += $cashInRow->cashed_commission;
                }
            }
        }
        foreach ($verifiedListings as $verifiedListingsRow) {
            if (isset($verifiedListingsRow->id)) {
                if (isset($agentInfo[$verifiedListingsRow->id])) {
                    $agentInfo[$verifiedListingsRow->id]['verifiedListings'] += $verifiedListingsRow->verified_listings;
                }
            }
        }
        foreach ($agentsRating as $agentsRatingRow) {
            if (isset($agentsRatingRow->id)) {
                if (isset($agentInfo[$agentsRatingRow->id])) {
                    $agentInfo[$agentsRatingRow->id]['agentsRating'] += $agentsRatingRow->agent_rating;
                }
            }
        }

        $mappedAgentInfo = [];
        if(isset($agentInfo) && is_array($agentInfo)) {
            foreach ($agentInfo as $agentData) {
                $agentName = $agentData['name'];
                $agentProfileImage = User::where('id', $agentData['id'])->first();
                $profileImageUrl = !empty($agentProfileImage['profile_image']) ? imageRoute('square-150', $agentProfileImage['profile_image']) : "";
                if (!isset($mappedAgentInfo[$agentName])) {
                    $mappedAgentInfo[$agentName] = [
                        'agentId' => $agentData['id'],
                        'agentName' => $agentName,
                        'newContacts' => $agentData['contactsCreated'],
                        'newListings' => $agentData['propertiesCreated'],
                        'reassignedLeads' => $agentData['reassignedLeads'],
                        'selfGeneratedLeads' => $agentData['selfGeneratedLeads'],
                        'newDeals' => $agentData['createdDeals'],
                        'visitScheduled' => $agentData['visitScheduledLeads'],
                        'cashIn' => $agentData['cashIn'],
                        'verifiedListings' => $agentData['verifiedListings'],
                        'ratings' => $agentData['agentsRating'],
                        'profileImage' => $profileImageUrl,
                        'points' => 0
                    ];
                }
            }
        }
        foreach ($mappedAgentInfo as &$agent) {
            $agent['points'] = $this->calculatePoints($agent);
        }

        usort($mappedAgentInfo, [$this, 'agentCompetitionSorter']);
        return $mappedAgentInfo;
    }

    private function calculatePoints($agent)
    {
        $targets = [
            'newContacts' => 60,
            'newListings' => 30,
            'newDeals' => 10,
            'reassignedLeads' => 30,
            'selfGeneratedLeads' => 40,
            'visitScheduled' => 40,
            'cashIn' => 40000,
            'verifiedListings' => 5,
            'ratings' => 15,
        ];

        $pointsPerTarget = [
            'newContacts' => 10,
            'newListings' => 15,
            'newDeals' => 15,
            'reassignedLeads' => 10,
            'selfGeneratedLeads' => 10,
            'visitScheduled' => 10,
            'cashIn' => 20,
            'verifiedListings' => 10,
            'ratings' => 10,
        ];

        $totalPoints = 0;

        foreach ($targets as $key => $target) {
            if ($agent[$key] >= $target) {
                $totalPoints += $pointsPerTarget[$key];
            }
        }

        return $totalPoints;
    }

    function agentCompetitionSorter($item1, $item2)
    {
        if ($item1['points'] > $item2['points']) {
            return -1;
        } elseif ($item1['points'] < $item2['points']) {
            return 1;
        } else {
            return strcmp($item1['agentName'], $item2['agentName']);
        }
    }


    public function competitionIndex()
    {
        $data = request()->validate([
            'month' => '',
        ]);

        $usedDate = new \DateTime($data['month'] . "-01");
        $usedDate->modify('-1 month');
        $usedDate->setDate($usedDate->format('Y'), $usedDate->format('m'), 26);

        $agentIds = User::where('is_active_for_competition', 1)->pluck('id')->toArray();
        return $this->getMonthlyCompetitionData($usedDate, $agentIds);
    }
}
