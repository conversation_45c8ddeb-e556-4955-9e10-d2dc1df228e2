<?php

namespace App\Http\Controllers\Crm;

use App\Exports\AgentReportExport;
use App\Exports\ClosedDealsExport;
use App\Exports\DealsDec2024Export;
use App\Exports\DealsExport;
use App\Exports\DubaiInvestorsExport;
use App\Exports\JMJLeadsExport;
use App\Exports\FGRealtyDatabaseExport;
use App\Exports\NohasContactsApril06;
use App\Http\Controllers\CrmBaseController;
use App\Imports\ContactsDatabaseSaleAccountsImport;
use App\Imports\SaleThePearlLeads2025;
use App\Models\Contact;
use App\Models\OperationHistory;
use App\Models\ContactWebsiteEntry;
use App\Models\Crm\ContactsListTag;
use App\Services\DashboardService;
use App\Models\Crm\ListingPublishingStatus;
use App\Models\ItemView;
use App\Models\Lead;
use App\Models\Property;
use App\Models\PropertySnapshot;
use App\Models\User;
use App\Services\EmailService;
use App\Services\MenuHelperService;
use App\Services\PerformanceService;
use App\Services\PropertiesService;
use App\Services\StatsReportService;
use App\Services\LeadsService;
use App\Services\OperationHistoryService;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Request;

class DashboardControllerCrm extends CrmBaseController
{
    private DashboardService $dashboardService;
    private StatsReportService $reportService;
    private PropertiesService $propertiesService;
    private PerformanceService $performanceService;
    private LeadsService $leadsService;
    private OperationHistoryService $operationHistoryService;

    public function __construct(
        DashboardService $dashboardService,
        StatsReportService $reportService,
        PropertiesService $propertiesService,
        PerformanceService $performanceService,
        LeadsService $leadsService,
        OperationHistoryService $operationHistoryService
    ) {
        $this->dashboardService = $dashboardService;
        $this->reportService = $reportService;
        $this->propertiesService = $propertiesService;
        $this->performanceService = $performanceService;
        $this->leadsService = $leadsService;
        $this->operationHistoryService = $operationHistoryService;
    }

    protected $views = [
        'index' => 'crm.dashboard.index'
    ];

    public function contactsCleanup()
    {
        // email cleanup
        DB::table('contacts')
            ->whereRaw("email_1 LIKE '%@test.com' OR email_1 LIKE 'aaa@%'")
            ->update([
                'email_1' => null
            ]);

        DB::table('contacts')
            ->whereRaw("email_2 LIKE '%@test.com' OR email_2 LIKE 'aaa@%'")
            ->update([
                'email_2' => null
            ]);

        $phoneColumns = ['phone_1', 'phone_2', 'mobile_1', 'mobile_2'];

        foreach ($phoneColumns as $column) {
            DB::table('contacts')
                ->whereNotNull($column)
                ->update([
                    $column => DB::raw("REGEXP_REPLACE($column, '[^0-9+]', '')"),
                ]);

            DB::table('contacts')
                ->whereRaw($column . " LIKE '0000'")
                ->update([
                    $column => null
                ]);
        }

        return 'Ok';

        // DB::table('contacts')
        //     ->update([
        //         'master_contact_id' => null
        //     ]);

        $allContacts = Contact::whereRaw('(phone_1 IS NOT NULL AND phone_1 != "") OR (phone_2 IS NOT NULL AND phone_2 != "") OR (mobile_1 IS NOT NULL AND mobile_1 != "") OR (mobile_2 IS NOT NULL AND mobile_2 != "")')->where('id', '35048')->orderBy('id', 'DESC')->paginate(1000);
        dd($allContacts->items());

        if (!$allContacts->count()) {
            die('No more contacts');
        }

        foreach ($allContacts as $c) {
            if (is_null($c->master_contact_id) && is_null($c->is_master_contact)) {
                Contact::select([
                    'id',
                    'name',
                    'phone_1',
                    'phone_2',
                    'mobile_1',
                    'mobile_2',
                    'email_1',
                    'email_2'
                ])->where(function ($qb) use ($c) {
                    if (!empty($c->phone_1)) {
                        $phoneNo = $c->phone_1;
                        $qb->orWhere(function ($qqb) use ($phoneNo) {
                            $qqb->where('phone_1', $phoneNo)
                                ->orWhere('phone_2', $phoneNo)
                                ->orWhere('mobile_1', $phoneNo)
                                ->orWhere('mobile_2', $phoneNo);
                        });
                    }
                    if (!empty($c->phone_2)) {
                        $phoneNo = $c->phone_2;
                        $qb->orWhere(function ($qqb) use ($phoneNo) {
                            $qqb->where('phone_1', $phoneNo)
                                ->orWhere('phone_2', $phoneNo)
                                ->orWhere('mobile_1', $phoneNo)
                                ->orWhere('mobile_2', $phoneNo);
                        });
                    }
                    if (!empty($c->mobile_1)) {
                        $phoneNo = $c->mobile_1;
                        $qb->orWhere(function ($qqb) use ($phoneNo) {
                            $qqb->where('phone_1', $phoneNo)
                                ->orWhere('phone_2', $phoneNo)
                                ->orWhere('mobile_1', $phoneNo)
                                ->orWhere('mobile_2', $phoneNo);
                        });
                    }
                    if (!empty($c->mobile_2)) {
                        $phoneNo = $c->mobile_2;
                        $qb->orWhere(function ($qqb) use ($phoneNo) {
                            $qqb->where('phone_1', $phoneNo)
                                ->orWhere('phone_2', $phoneNo)
                                ->orWhere('mobile_1', $phoneNo)
                                ->orWhere('mobile_2', $phoneNo);
                        });
                    }
                    if (!empty($c->email_1)) {
                        $emailAddr = $c->email_1;
                        $qb->orWhere(function ($qqb) use ($emailAddr) {
                            $qqb->where('email_1', $emailAddr)
                                ->orWhere('email_2', $emailAddr);
                        });
                    }
                    if (!empty($c->email_2)) {
                        $emailAddr = $c->email_2;
                        $qb->orWhere(function ($qqb) use ($emailAddr) {
                            $qqb->where('email_1', $emailAddr)
                                ->orWhere('email_2', $emailAddr);
                        });
                    }
                })->where('id', '!=', $c->id)->whereNull('is_master_contact')->update([
                    'master_contact_id' => $c->id
                ]);
                $c->is_master_contact = true;
                $c->save();
            }
        }

        echo "Done";
        die;

        return view($this->views['index']);
    }

    public function exportClosedDeals()
    {
        // $sql = "
        // SELECT
        //     c.name,
        //     c.mobile_1,
        //     c.mobile_2,
        //     n.name as nationality,
        //     ls.name as lead_source,
        //     d.price,
        //     d.created_at
        // FROM
        //     deals d
        //     JOIN leads l ON l.id = d.lead_id
        //     JOIN contacts c ON c.id = l.contact_id
        //     LEFT JOIN lead_sources ls ON  ls.id = l.platform_from
        //     LEFT JOIN nationalities n ON n.id = c.nationality_id
        // WHERE
        //     d.type LIKE 'SALE'
        // AND (d.deal_status IS NULL OR d.deal_status LIKE 'approved')
        // ORDER BY d.created_at DESC;
        // ";

        return Excel::download(new ClosedDealsExport, 'closedDealsQ12025.xlsx');
    }

    public function exportJMJSituation()
    {
        //retrieve all data for JMJ leads + 4 listings
        $leads = $this->leadsService->getJMJLeadsCollection();
        $mappedLeads = $leads->map(function ($lead) {
            $leadRequest = null;
            $network = '';
            $utm_campaign = '';
            $beStr = 'n/a';
            try {
                $leadRequest = json_decode($lead->leads_request);
                if (!is_null($leadRequest)) {
                    if (isset($leadRequest->be)) {
                        if (is_array($leadRequest->be)) {
                            if (count($leadRequest->be)) {
                                $beStr = $leadRequest->be[0] . "BR";
                            }
                        } else {
                            $beStr = $leadRequest->be . "BR";
                        }
                    }
                }
                $leadMetadata = $lead->lead_metadata;
                if (!is_null($leadMetadata)) {
                    $network = $leadMetadata['network'];
                    $utm_campaign = $leadMetadata['utm_campaign'];
                }
            } catch (\Exception $Ex) {
            }

            $metadata = null;
            $campaign = $lead->socialMediaPlatform();

            $arr = [
                'id' => $lead->id,
                'contactName' => $lead->contact->contactName(),
                'email' => $lead->contact->email_1,
                'phone_prefix' => $lead->contact->prefix_mobile_1,
                'country' => $lead->contact->country,
                'phone' => $lead->contact->mobile_1,
                'listing' => $lead->inquired_ref_no,
                'created_at' => $lead->created_at->format('d.m.Y H:i'),
                'looking_for' => $beStr,
                'campaign' => $campaign,
                'network' => $campaign,
                'utm_campaign' => $utm_campaign,
            ];

            return (object)$arr;
        });

        if (request()->has('download')) {
            return Excel::download(new JMJLeadsExport($mappedLeads), 'jmj-leads_' . date('d-m-Y-H-i') . '.xlsx');
        }

        return view('crm.dashboard.jmj-situation', ['leads' => $mappedLeads]);
    }

    public function events()
    {
        $currentDate = (new \DateTime())->format("Y-m-d");
        $startDate = date("Y-m-01");
        $endDate = date("Y-m-t", strtotime($currentDate));

        if (request()->has('start')) {
            $startDate = Carbon::parse(request()->get('start'))->format("Y-m-d");
        }
        if (request()->has('end')) {
            $endDate = Carbon::parse(request()->get('end'))->format("Y-m-d");
        }

        $userEvents = $this->dashboardService->getDashboardEvents(auth()->user(), $startDate, $endDate);

        return $userEvents;
    }

    public function stats()
    {
        $listingsNo = PropertySnapshot::whereIn('publishing_status', [ListingPublishingStatus::STATUS_PUBLISHED])->count();
        $personalListingsNo = PropertySnapshot::whereIn('publishing_status', [ListingPublishingStatus::STATUS_PUBLISHED])->where('created_by', auth()->user()->id)->count();
        $allImpressions = ItemView::where('operation_type', 'snapshot_view')->count();
        $personalImpressionsRes = DB::select(
            DB::raw("SELECT COUNT(*) as impressions_no from item_views iv JOIN property_snapshots ps ON ps.id = iv.item_id WHERE iv.operation_type LIKE 'snapshot_view' AND ps.publishing_status IN ('published') AND ps.created_by = '" . auth()->user()->id . "'")
        );
        return [
            "listings" => [
                "total" => $listingsNo,
                "personal" => $personalListingsNo
            ],
            "impressions" => [
                "total" => $allImpressions,
                "personal" => $personalImpressionsRes[0]->impressions_no
            ],
            "clicks" => [
                "total" => 0,
                "personal" => 0
            ]
        ];
    }

    public function kpi()
    {
        $month = request()->query('month');
        $year = request()->query('year');
        if (!request()->has('agentIds')) {
            $userIds = [auth()->user()->id];
        } else {
            $userIds = [request()->get('agentIds')];
            $userIds = explode(",", $userIds[0]);
        }

        $kpiData = $this->performanceService->getDataForAgents($userIds, $month, $year);

        return compact(
            'kpiData',
        );
    }

    public function export2024Deals()
    {
        return Excel::download(new DealsExport, 'deals_2024.xlsx');
    }

    public function exportAll2024Deals()
    {
        return Excel::download(new DealsDec2024Export, 'all_deals_2024.xlsx');
    }

    public function exportDubaiInvestors()
    {
        return Excel::download(new DubaiInvestorsExport, 'dubai_investors_2024.xlsx');
    }

    protected function viewVars(string $viewName)
    {
        $agents = getAllFGRAgentsForContest(true, false, true);
        return compact(
            'agents',
        );
    }

    public function reportsEmailTest()
    {
        $destinationEmail = '<EMAIL>';

        $reportType = 'monthly';

        $allAgents = getAllFGRAgents(false, false, true);
        $agentInfo = [];
        $startDate = new \DateTime("2023-05-01");
        $propertiesCreated = $this->reportService->getPropertiesCreated($reportType, $startDate);
        $propertiesUpdated = $this->reportService->getPropertiesUpdated($reportType, $startDate);
        $createdLeads = $this->reportService->getLeadsPerAuthor($reportType, $startDate);
        $leadsFromWebsite = $this->reportService->getLeadsFromWebsite($reportType, $startDate);
        $assignedLeads = $this->reportService->getAssignedLeadsPerUser($reportType, $startDate);
        $reassignedLeads = $this->reportService->getReassignedLeadsPerUser($reportType, $startDate);
        $hotLeads = $this->reportService->getLeadsPerUserByStatus($reportType, $startDate, null, Lead::LEAD_STATUS_HOT);
        $warmLeads = $this->reportService->getLeadsPerUserByStatus($reportType, $startDate, null, Lead::LEAD_STATUS_WARM);
        $coldLeads = $this->reportService->getLeadsPerUserByStatus($reportType, $startDate, null, Lead::LEAD_STATUS_COLD);
        $convertedLeads = $this->reportService->getConvertedLeadsPerUser($reportType, $startDate);
        $deals = $this->reportService->getDealsPerAgent($reportType, $startDate);
        $createdLandlordsInfo = $this->reportService->getCreatedLandlordsPerUser($reportType, $startDate);
        $agentCommissionForClient = $this->reportService->getAgentCommissionForClient($reportType, $startDate);
        $agentCommissionForLandlord = $this->reportService->getAgentCommissionForLandlord($reportType, $startDate);
        $agentApprovedDeals = $this->reportService->getAgentApprovedDeals($reportType, $startDate);

        $topViewedSnapshotsData = $this->reportService->getTopSnapshotViews($reportType, $startDate);
        $topViewedSnapshotIds = array_map(function ($currentItem) {
            return $currentItem->item_id;
        }, $topViewedSnapshotsData);
        $topViewedSnapshots = $this->propertiesService->searchLite(['ids' => $topViewedSnapshotIds]);
        foreach ($topViewedSnapshotsData as $data) {
            $snapshotObject = $topViewedSnapshots->getCollection()->firstWhere('listing_id', '=', $data->item_id);
            $data->snapshotObject = null;
            if (!is_null($snapshotObject)) {
                $data->snapshotObject = $snapshotObject;
                $data->publicURL = MenuHelperService::createURLForSearchLiteReturn($snapshotObject, $this->propertiesService);
            }
        }

        if ($reportType == 'monthly') {
            $periodTime = now()->format('F Y');
        } else if ($reportType == 'quarterly') {
            $periodTime = date('M', strtotime('-2 months')) . " to " . date('M') . " " . date("Y");
        } else {
            $saturday = strtotime('last saturday');
            $thursday = strtotime('+6 days', $saturday);
            $periodTime =  date('d M Y', $saturday) . " to " . date('d M Y', $thursday);
        }

        foreach ($allAgents as $agentRow) {
            if (!isset($agentInfo[$agentRow->id])) {
                $agentInfo[$agentRow->id] = [
                    'name' => $agentRow->name,
                    'propertiesCreated' => 0,
                    'propertiesUpdated' => 0,
                    'createdLeads' => 0,
                    'assignedLeads' => 0,
                    'websiteLeads' => 0,
                    'reassignedLeads' => 0,
                    'hotLeads' => 0,
                    'warmLeads' => 0,
                    'coldLeads' => 0,
                    'createdDeals' => 0,

                    // new
                    'convertedDeals' => 0,
                    'convertedLeads' => 0,
                    'createdLandlords' => 0,
                    'agentCommissionForClient' => 0,
                    'agentCommissionForLandlord' => 0,
                    'agentApprovedDeals' => 0
                ];
            }
        }

        foreach ($propertiesCreated as $listingsCreatedRow) {
            if (isset($listingsCreatedRow->id)) {
                if (isset($agentInfo[$listingsCreatedRow->id])) {
                    $agentInfo[$listingsCreatedRow->id]['propertiesCreated'] += $listingsCreatedRow->created_listings;
                }
            }
        }

        foreach ($propertiesUpdated as $listingsUpdatedRow) {
            if (isset($listingsUpdatedRow->id)) {
                if (isset($agentInfo[$listingsUpdatedRow->id])) {
                    $agentInfo[$listingsUpdatedRow->id]['propertiesUpdated'] += $listingsUpdatedRow->updated_listings;
                }
            }
        }

        foreach ($createdLeads as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['createdLeads'] += $leadInfo->created_leads;
                }
            }
        }

        foreach ($leadsFromWebsite as $leadInfo) {
            if (isset($leadInfo->id)) {
                if (isset($agentInfo[$leadInfo->id])) {
                    $agentInfo[$leadInfo->id]['websiteLeads'] += $leadInfo->website_leads;
                }
            }
        }

        foreach ($reassignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['reassignedLeads'] += $leadInfo->reassigned_leads;
                }
            }
        }

        foreach ($hotLeads as $hotLead) {
            if (isset($hotLead->id)) {
                if (isset($agentInfo[$hotLead->id])) {
                    $agentInfo[$hotLead->id]['hotLeads'] += $hotLead->leads_by_status_no;
                }
            }
        }

        foreach ($warmLeads as $warmLead) {
            if (isset($warmLead->id)) {
                if (isset($agentInfo[$warmLead->id])) {
                    $agentInfo[$warmLead->id]['warmLeads'] += $warmLead->leads_by_status_no;
                }
            }
        }

        foreach ($coldLeads as $coldLead) {
            if (isset($coldLead->id)) {
                if (isset($agentInfo[$coldLead->id])) {
                    $agentInfo[$coldLead->id]['coldLeads'] += $coldLead->leads_by_status_no;
                }
            }
        }

        foreach ($assignedLeads as $leadInfo) {
            if (isset($leadInfo->user_id)) {
                if (isset($agentInfo[$leadInfo->user_id])) {
                    $agentInfo[$leadInfo->user_id]['assignedLeads'] += $leadInfo->assigned_leads;
                }
            }
        }

        foreach ($deals as $dealInfo) {
            if (isset($dealInfo->id)) {
                if (isset($agentInfo[$dealInfo->id])) {
                    $agentInfo[$dealInfo->id]['createdDeals'] += $dealInfo->created_deals_no;
                }
            }
        }

        foreach ($createdLandlordsInfo as $landlordInfo) {
            if (isset($landlordInfo->user_id)) {
                if (isset($agentInfo[$landlordInfo->user_id])) {
                    $agentInfo[$landlordInfo->user_id]['createdLandlords'] += $landlordInfo->landlords_no;
                }
            }
        }

        foreach ($agentCommissionForClient as $agentClient) {
            if (isset($agentClient->id)) {
                if (isset($agentInfo[$agentClient->id])) {
                    $agentInfo[$agentClient->id]['agentCommissionForClient'] += $agentClient->cashed_commission_client;
                }
            }
        }

        foreach ($agentCommissionForLandlord as $agentLandlord) {
            if (isset($agentLandlord->id)) {
                if (isset($agentInfo[$agentLandlord->id])) {
                    $agentInfo[$agentLandlord->id]['agentCommissionForLandlord'] += $agentLandlord->cashed_commission_landlord;
                }
            }
        }

        foreach ($agentApprovedDeals as $agentApprove) {
            if (isset($agentApprove->id)) {
                if (isset($agentInfo[$agentApprove->id])) {
                    $agentInfo[$agentApprove->id]['agentApprovedDeals'] += $agentApprove->approved_deals_no;
                }
            }
        }

        foreach ($convertedLeads as $agentData) {
            if (isset($agentData->id)) {
                if (isset($agentInfo[$agentData->id])) {
                    $agentInfo[$agentData->id]['convertedLeads'] += $agentData->converted_leads_no;
                }
            }
        }

        $allData = [
            'periodTime' => $periodTime,
            'reportType' => $reportType,
            'agentInfo' => $agentInfo,
            'topViewedSnapshotData' => $topViewedSnapshotsData,
        ];

        $mappedAgentInfo = [];
        foreach ($agentInfo as $agentData) {
            $agentName = $agentData['name'];
            if (!isset($mappedAgentInfo[$agentName])) {
                $mappedAgentInfo[$agentName] = [
                    $agentData['propertiesCreated'],
                    $agentData['propertiesUpdated'],
                    $agentData['createdLandlords'],
                    $agentData['websiteLeads'],
                    $agentData['createdLeads'],
                    $agentData['assignedLeads'],
                    $agentData['reassignedLeads'],
                    $agentData['convertedLeads'],
                    $agentData['hotLeads'],
                    $agentData['warmLeads'],
                    $agentData['coldLeads'],
                    $agentData['createdDeals'],
                    $agentData['agentApprovedDeals'],
                    $agentData['agentCommissionForClient'],
                    $agentData['agentCommissionForLandlord']
                ];
            }
        }

        echo json_encode($mappedAgentInfo);
        die;
    }

    public function correctContactsFromWebsiteEntries()
    {
        die('Already executed');
        $websiteEntries = ContactWebsiteEntry::with(['contacts' => function ($qb) {
            $qb->whereNull('prefix_mobile_1');
        }])->whereNotNull('prefix')->get();

        foreach ($websiteEntries as $we) {
            foreach ($we->contacts as $c) {
                if (is_null($c->prefix_mobile_1)) {
                    if (!Str::startsWith($c->mobile_1, "+")) {
                        $c->prefix_mobile_1 = $we->prefix;
                        $c->save();
                        Log::info("Updated contact " . $c->id . ", " . $c->mobile_1 . " | " . $c->email_1 . " = " . $we->email . ", [" . $we->prefix . " - " . $we->phone . "]");
                    }
                }
            }
        }
        return "Executed";
    }

    public function fgrealtyDatabase()
    {
        $reportType = request()->get('reportType');
        $records = $this->leadsService->getFgrealtyDatabase($reportType)->map(function ($lead) {
            if (is_null($lead->contact)) {
                dd($lead);
            }
            $leadRequirements = $lead->leadRequirements();
            if (is_array($leadRequirements)) {
                $leadRequirementsStr = "";
                if (isset($leadRequirements["be"])) {
                    $leadRequirementsStr .= "Be: " . (is_array($leadRequirements["be"]) ? implode(",", $leadRequirements["be"]) : $leadRequirements["be"]) . "\r\n";
                }
                if (isset($leadRequirements["ba"])) {
                    $leadRequirementsStr .= "Ba: " . $leadRequirements["ba"] . "\r\n";
                }
            } else {
                $leadRequirementsStr = $leadRequirements;
            }

            $leadBudget = ['min' => "", 'max' => ''];

            if (!empty($lead->filter_budget_min)) {
                $leadBudget['min'] = $lead->filter_budget_min;
            }
            if (!empty($lead->filter_budget_max)) {
                $leadBudget['max'] = $lead->filter_budget_max;
            }

            if ($leadBudget['min'] == '') {
                if (!empty($lead->budget_min)) {
                    $leadBudget['min'] = $lead->budget_min;
                }
                if (!empty($lead->budget_max)) {
                    $leadBudget['max'] = $lead->budget_max;
                }
            }

            if ($leadBudget['min'] != '') {
                $leadBudget['min'] = number_format($leadBudget['min']);
            }

            if ($leadBudget['max'] != '') {
                $leadBudget['max'] = number_format($leadBudget['max']);
            }

            $mappedLead = [];
            $mappedLead['id'] = $lead->id;
            $mappedLead['leadOperationType'] = $lead->leadOperationType();
            $mappedLead['contactName'] = $lead->contact->contactName();
            $mappedLead['mobile1Number'] = $lead->contact->getMobile1Number();
            $mappedLead['email'] = $lead->contact->email_1 . ' ' . (!empty($lead->contact->email_2) ? ', ' . $lead->contact->email_2 : '');
            $mappedLead['last_contact_date'] = !empty($lead->last_contact_date) ? (new \DateTime($lead->last_contact_date))->format('d.m.Y') : "";
            $mappedLead['leadRequirementsStr'] = $leadRequirementsStr;
            $mappedLead['leadPlatform'] = $lead->leadPlatform();
            $mappedLead['created_at'] = !empty($lead->created_at) ? $lead->created_at->format('d.m.Y H:i') : "";
            $mappedLead['leadBudget'] = $leadBudget['min'] . " " . ($leadBudget['max'] != '' ? " - " . $leadBudget['max'] : "");
            if (empty(trim($mappedLead['leadBudget']))) {
                $mappedLead['leadBudget'] = number_format($lead->budget);
            }

            return (object) $mappedLead;
        });

        if (request()->has('download')) {
            return Excel::download(new FGRealtyDatabaseExport($records), 'fgrealty-database_' . date('d-m-Y-H-i') . '.xlsx');
        }
        return view('crm.dashboard.fgrealty-database', ['records' => $records]);
    }

    public function leadsReport(StatsReportService $statsReportService, EmailService $emailService)
    {

        $endDate = Carbon::now(); //->toDateString();

        if ($endDate->format('d') >= 26) {
            $startDate = Carbon::now()->setDay(26)->setHour('0')->setMinute(0);
        } else {
            $startDate = Carbon::now()->startOfMonth()->subMonth()->setDay(26);
        }

        $stats = $statsReportService->getLeadsPerCategoriesPerPeriod($startDate, $endDate);

        $emailService->sendLeadStatsPerMonth($stats, $startDate, $endDate);
    }

    public function putTagsOnJmjContacts()
    {
        $leads = Lead::with(['contact'])->where('platform_from', 122)->get();
        $tags = ['Enquiry form JMJ'];

        foreach ($leads as $lead) {
            if (!is_null($lead->contact)) {
                $this->attachTagsToContact($lead->contact, $tags);
            }
        }
    }

    protected function attachTagsToContact($contact, $tags)
    {
        foreach ($tags as $tagLabel) {
            // Find or create the tag in the contacts_list_tags table
            $tag = DB::table('contacts_list_tags')->where('label', $tagLabel)->first();
            if (!$tag) {
                $tagId = DB::table('contacts_list_tags')->insertGetId(['label' => $tagLabel]);
                $tag = (object) ['id' => $tagId, 'label' => $tagLabel];
            }

            // Use DB query builder to check if the contact already has this tag
            $existingTag = DB::table('contacts_list_x_tags')
                ->where('contacts_list_id', $contact->id)
                ->where('contacts_list_tag_id', $tag->id)
                ->exists();

            // If the contact does not already have the tag, insert the relationship
            if (!$existingTag) {
                DB::table('contacts_list_x_tags')->insert([
                    'contacts_list_id' => $contact->id,
                    'contacts_list_tag_id' => $tag->id,
                ]);

                // Use OperationHistoryService to log the operation
                $this->operationHistoryService->addOperationHistory(
                    $contact,
                    "The following tag: {$tag->label} was added"
                );
            }
        }
    }

    public function reimportGoogleLeads()
    {
        // $leadData = DB::table('google_leads')->select(['lead_id', 'user_column_data'])->where('id', '>', 10)->get();
        // $googleLeads = [];
        // foreach($leadData as $item) {
        //     if(!isset($googleLeads[$item->lead_id])) {
        //         $googleLead = new GoogleLead();
        //         $googleLead->lead_id = $item->lead_id;
        //         $googleLead->user_column_data = json_decode($item->user_column_data);
        //         $googleLeads[$item->lead_id] = $googleLead;

        //         $this->leadsService->createLeadFromGoogleLead($googleLead);
        //     }
        // }
    }

    public function exportGmailContactsToCSV()
    {
        // Run the combined query
        $contacts = DB::select(DB::raw("
            SELECT
                CASE
                    WHEN email_2 LIKE '%@gmail.com' THEN email_2
                    ELSE email_1
                END AS Email,
                CONCAT(prefix_mobile_1, mobile_1) AS Phone,
                SUBSTRING_INDEX(name, ' ', 1) AS First_Name,
                SUBSTRING_INDEX(name, ' ', -1) AS Last_Name,
                country AS Country,
                zip_code AS Zip
            FROM
                contacts
            WHERE
                (email_1 LIKE '%@gmail.com' OR email_2 LIKE '%@gmail.com')

            UNION

            SELECT
                email AS Email,
                NULL AS Phone,
                SUBSTRING_INDEX(name, ' ', 1) AS First_Name,
                SUBSTRING_INDEX(name, ' ', -1) AS Last_Name,
                NULL AS Country,
                NULL AS Zip
            FROM
                newsletter_subscribtions
            WHERE
                email LIKE '%@gmail.com'
        "));

        // Remove duplicates by email (in case UNION doesn't cover it perfectly)
        $uniqueContacts = collect($contacts)->unique('Email');

        // Define the CSV file header
        $csvHeader = ['Email', 'Phone', 'First Name', 'Last Name', 'Country', 'Zip'];

        // Create CSV content
        $csvContent = implode(",", $csvHeader) . "\n";

        $existingEmails = [];
        // Add rows
        foreach ($uniqueContacts as $contact) {
            if (!isset($existingEmails[$contact->Email])) {
                $csvContent .= implode(",", [
                    $contact->Email,
                    $contact->Phone ?? '',
                    $contact->First_Name ?? '',
                    $contact->Last_Name ?? '',
                    $contact->Country ?? '',
                    $contact->Zip ?? ''
                ]) . "\n";
                $existingEmails[$contact->Email] = 1;
            }
        }

        // Save CSV file to storage
        Storage::put('gmail_contacts_combined.csv', $csvContent);

        return response()->download(storage_path('app/gmail_contacts_combined.csv'));
    }

    public function exportOpTypeContactsWithLeadsToCSV()
    {
        // Run the SQL query
        $contacts = DB::select(DB::raw("
        SELECT
            CASE
                WHEN c.email_2 LIKE '%@gmail.com' THEN c.email_2
                ELSE c.email_1
            END AS Email,
            CONCAT(c.prefix_mobile_1, c.mobile_1) AS Phone,
            SUBSTRING_INDEX(c.name, ' ', 1) AS First_Name,
            SUBSTRING_INDEX(c.name, ' ', -1) AS Last_Name,
            c.country AS Country,
            c.zip_code AS Zip
        FROM
            contacts c
        JOIN
            leads l ON c.id = l.contact_id
        WHERE
            (c.email_1 LIKE '%@gmail.com' OR c.email_2 LIKE '%@gmail.com')
            AND l.filter_operation_type = 'rent'
    "));

        // Define the CSV file header
        $csvHeader = ['Email', 'Phone', 'First Name', 'Last Name', 'Country', 'Zip'];

        // Create CSV content
        $csvContent = implode(",", $csvHeader) . "\n";

        $existingEmails = [];

        // Add rows
        foreach ($contacts as $contact) {
            if (!isset($existingEmails[$contact->Email])) {
                $csvContent .= implode(",", [
                    $contact->Email,
                    $contact->Phone ?? '',
                    $contact->First_Name ?? '',
                    $contact->Last_Name ?? '',
                    $contact->Country ?? '',
                    $contact->Zip ?? ''
                ]) . "\n";
                $existingEmails[$contact->Email] = 1;
            }
        }

        // Save CSV file to storage
        Storage::put('gmail_contacts_with_leads.csv', $csvContent);

        return response()->download(storage_path('app/gmail_contacts_with_leads.csv'));
    }

    public function exportJMJContactsCustom()
    {
        $contacts = DB::select(DB::raw("SELECT * FROM contacts where id IN (select contact_id FROM `leads` WHERE `platform_from` IN ('116','122','124') AND deleted_at IS NULL);"));
        // $contacts = DB::select(DB::raw("SELECT * FROM contacts where id IN (select contact_id FROM `leads` WHERE `platform_from` IN ('116','122','124') AND deleted_at IS NULL AND lead_status_id NOT IN (13, 19, 21, 29));"));
        $csvHeader = ['phone_number', 'name'];

        // Create CSV content
        $csvContent = implode(",", $csvHeader) . "\n";

        // Add rows
        foreach ($contacts as $contact) {
            $phone = getCompletePhoneNo($contact->prefix_mobile_1, $contact->mobile_1);
            $csvContent .= implode(",", [
                $phone,
                $contact->name ?? '',
            ]) . "\n";
        }

        // Save CSV file to storage
        Storage::put('jmj_contacts.csv', $csvContent);

        return response()->download(storage_path('app/jmj_contacts.csv'));
    }

    public function exportContactsForMarketingCampaign()
    {
        // Sale
        // $fileName = "WhatsappExportSaleContacts.csv";
        // $sql = "SELECT l.id, l.filter_operation_type, c.prefix_mobile_1, c.mobile_1, c.name, c.prefix_mobile_2, c.mobile_2, c.phone_1, c.phone_2 FROM leads l JOIN contacts c ON c.id = l.contact_id WHERE COALESCE(c.prefix_mobile_1, c.mobile_1, 'invalid') NOT LIKE 'invalid' AND c.name IS NOT NULL AND c.master_contact_id IS NULL AND (l.filter_operation_type LIKE 'sale' OR l.leads_request LIKE '%\"ot\":\"sale\"%') GROUP BY c.id;";

        // Rent
        // $fileName = "WhatsappExportOnlyRentContacts.csv";
        // $sql = "SELECT l.id, l.filter_operation_type, c.prefix_mobile_1, c.mobile_1, c.name, c.prefix_mobile_2, c.mobile_2, c.phone_1, c.phone_2 FROM leads l JOIN contacts c ON c.id = l.contact_id WHERE COALESCE(c.mobile_1, c.mobile_2, 'invalid') NOT LIKE 'invalid' AND c.name IS NOT NULL AND c.master_contact_id IS NULL AND (l.filter_operation_type LIKE 'rent' OR l.leads_request LIKE '%\"ot\":\"rent\"%') AND c.id NOT IN (SELECT c.id FROM leads l JOIN contacts c ON c.id = l.contact_id WHERE COALESCE(c.prefix_mobile_1, c.mobile_1, 'invalid') NOT LIKE 'invalid' AND c.name IS NOT NULL AND (l.filter_operation_type LIKE 'sale' OR l.leads_request LIKE '%\"ot\":\"sale\"%')) GROUP BY c.id;";

        // All other contacts
        $fileName = "WhatsappExportAllOtherContacts.csv";
        $sql = "SELECT c.prefix_mobile_1, c.mobile_1, c.name, c.prefix_mobile_2, c.mobile_2, c.phone_1, c.phone_2 FROM contacts c WHERE c.master_contact_id IS NULL AND COALESCE(c.mobile_1, c.mobile_2, 'invalid') NOT LIKE 'invalid' AND c.id NOT IN (SELECT contact_id FROM leads)";

        // $fileName = "WhatsappExportContactsNotSegmented.csv";
        // $sql = "SELECT c.prefix_mobile_1, c.mobile_1, c.name, c.prefix_mobile_2, c.mobile_2, c.phone_1, c.phone_2 FROM contacts c WHERE c.master_contact_id IS NULL AND COALESCE(c.mobile_1, c.mobile_2, 'invalid') NOT LIKE 'invalid'";

        $records = DB::select(DB::raw($sql));

        $csvContent = "phone_number,name\n";
        $usedPhones = [];
        foreach ($records as $record) {
            $usedPrefix = null;
            $usedPhone = null;
            if (!empty($record->mobile_1)) {
                $usedPrefix = $record->prefix_mobile_1;
                $usedPhone = $record->mobile_1;
            } else if (!empty($record->mobile_2)) {
                $usedPrefix = $record->prefix_mobile_2;
                $usedPhone = $record->mobile_2;
            }
            if (!is_null($usedPhone) && !isset($usedPhones[$usedPhone])) {
                $phoneNo = getCompletePhoneNo($usedPrefix ?? '+974', $usedPhone);
                $csvContent .= $phoneNo . ", " . $record->name . "\n";
                $usedPhones[$usedPhone] = 1;
            }
        }

        Storage::put($fileName, $csvContent);
        return "File " . $fileName . " successfully written";
    }

    // public function importCompaniesForm() {
    //     return view('crm.dashboard.companies-form');
    // }

    // public function importCompanies(Request $request)
    // {
    //     Excel::import(new CompaniesImport, $request->file('file'));

    //     return redirect()->back()->with('success', 'Companies imported successfully!');
    // }

    public function importDatabaseSalesAccounts()
    {
        if (request()->isMethod('post')) {
            $file = request()->file('contacts_file');
            Excel::import(new ContactsDatabaseSaleAccountsImport, $file);
            die('Contacts imported');
        }
        return view('crm.dashboard.database-sales-accounts');
    }

    public function deleteContactsAndLeads()
    {
        $contactIds = []; // Expect an array of contact IDs.
        $mobilePattern = "*********";

        try {
            DB::transaction(function () use ($contactIds, $mobilePattern) {
                // Step 1: Get contacts with the specified conditions
                $contacts = Contact::whereIn('id', $contactIds)
                    ->orWhereIn('master_contact_id', $contactIds)
                    ->orWhere('mobile_1', 'LIKE', '%' . $mobilePattern . '%')
                    ->get();

                // dd($contacts);

                $contactIdsToDelete = $contacts->pluck('id')->toArray();
                // dd($contactIdsToDelete);

                // Step 2: Get leads for the contacts
                $leads = Lead::whereIn('contact_id', $contactIdsToDelete)->get();
                $leadIdsToDelete = $leads->pluck('id')->toArray();

                // dd($leadIdsToDelete);

                // Step 3: Remove related records in operation_history
                OperationHistory::where(function ($query) use ($contactIdsToDelete, $leadIdsToDelete) {
                    $query->whereIn('model_id', $contactIdsToDelete)
                        ->where('model_type', 'App\Models\Contact');
                })->orWhere(function ($query) use ($leadIdsToDelete) {
                    $query->whereIn('model_id', $leadIdsToDelete)
                        ->where('model_type', 'App\Models\Lead');
                })->delete();

                // Step 4: Delete contacts and leads
                Lead::whereIn('id', $leadIdsToDelete)->delete();
                Contact::whereIn('id', $contactIdsToDelete)->delete();
            });

            return 'Contacts and leads deleted successfully.';
        } catch (\Exception $e) {
            return 'Failed to delete contacts and leads: ' . $e->getMessage();
        }
    }

    function updateDatabaseSalesAccountsContacts()
    {
        // Fetch the tag ID for "Database Sales Accounts"
        $tag = ContactsListTag::where('label', 'Database Sales Accounts')->first();

        if (!$tag) {
            throw new Exception("Tag 'Database Sales Accounts' not found.");
        }

        // Fetch contacts with the "Database Sales Accounts" tag
        $contacts = Contact::whereHas('tags', function ($query) use ($tag) {
            $query->where('contacts_list_tags.id', $tag->id);
        })->where('created_by', 0)->get();

        // Update `created_by` to 329 for each contact
        foreach ($contacts as $contact) {
            $contact->update(['created_by' => 329]);
            $this->operationHistoryService->addOperationHistory($contact, "Referred by updated to: <EMAIL>");
            // echo $contact->id.": ".$contact->created_by."<br />";
        }

        return "Updated " . $contacts->count() . " contacts.";
    }

    public function showFileUpload()
    {
        return view("file-uploader");
    }

    public function thePearlSaleLeads(Request $request)
    {
        Excel::import(new SaleThePearlLeads2025, $request->file('file'));
        return "After";
    }

    public function firstQuarter2025()
    {
        $startDate = '2024-12-26 00:00:00';
        $endDate   = '2025-03-25 23:59:59';

        $period = CarbonPeriod::create($startDate, $endDate);
        $totalDays = $period->count();

        $agents = User::whereHas('roles', function ($q) {
            $q->whereIn('id', [1, 2, 3]);
        })->get();

        $finalReport = $agents->map(function ($agent) use ($startDate, $endDate, $totalDays) {
            $listings = Property::where('created_by', $agent->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $selfLeads = Lead::where('created_by', $agent->id)
                ->where('platform_from', '<>', 'FGR')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $fgrLeads = Lead::with(['assignment'])
                ->whereNull('created_by')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereHas('assignment', function ($query) use ($agent) {
                    $query->where('user_id', $agent->id);
                })
                ->count();

            $fgrFromLeads = Lead::with(['assignment'])
                ->whereIn('created_by', [54, 217, 334])
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereHas('assignment', function ($query) use ($agent) {
                    $query->where('user_id', $agent->id)
                        ->whereNull('deleted_at');
                })
                ->count();

            $allFgrLeads = $fgrLeads + $fgrFromLeads;

            $checkIns = DB::table('user_check_ins')
                ->select(
                    DB::raw("DATE(check_in_date) as check_in_day"),
                    DB::raw("MIN(check_in_date) as first_check_in")
                )
                ->where('user_id', $agent->id)
                ->whereBetween('check_in_date', [$startDate, $endDate])
                ->groupBy(DB::raw("DATE(check_in_date)"))
                ->get();

            $daysNotOnTime = $checkIns->filter(function ($record) {
                return date('H:i:s', strtotime($record->first_check_in)) > '09:16:00';
            })->count();

            $daysOnTime = $checkIns->filter(function ($record) {
                return date('H:i:s', strtotime($record->first_check_in)) <= '09:16:00';
            })->count();

            $closedDeals = DB::table('deals')
                ->where(function ($q) use ($agent) {
                    $q->where('closing_agent_id', $agent->id);
                })
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            return [
                'agent_id'             => $agent->id,
                'agent_name'           => $agent->name,
                'listings_created'     => $listings,
                'self_generated_leads' => $selfLeads,
                'leads_from_fgr'       => $allFgrLeads,
                'days_not_on_time'     => $daysNotOnTime,
                'days_on_time'         => $daysOnTime,
                'closed_deals_count'   => $closedDeals,
            ];
        });

        $specificUserLeadCounts = User::whereIn('id', [54, 217, 334])
            ->withCount(['leads as lead_count' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->get(['id', 'name']);

        return view('reports.agent', compact('finalReport', 'specificUserLeadCounts'));
    }

    public function firstQuarter2025Export()
    {
        // The same date constraints and working days logic.
        $startDate = '2024-12-26 00:00:00';
        $endDate   = '2025-03-25 23:59:59';

        $period = CarbonPeriod::create($startDate, $endDate);
        $workingDays = collect($period->toArray())->filter(function ($date) {
            return in_array($date->dayOfWeek, [0, 1, 2, 3, 4]);
        });

        $agents = User::whereHas('roles', function ($q) {
            $q->whereIn('id', [1, 2, 3]);
        })->get();

        $finalReport = $agents->map(function ($agent) use ($startDate, $endDate) {
            $listings = Property::where('created_by', $agent->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $selfLeads = Lead::where('created_by', $agent->id)
                ->where('platform_from', '<>', 'FGR')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $fgrLeads = Lead::with(['assignment'])
                ->whereNull('created_by')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereHas('assignment', function ($query) use ($agent) {
                    $query->where('user_id', $agent->id);
                })
                ->count();

            $fgrFromLeads = Lead::with(['assignment'])
                ->whereIn('created_by', [54, 217, 334])
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereHas('assignment', function ($query) use ($agent) {
                    $query->where('user_id', $agent->id)
                        ->whereNull('deleted_at');
                })
                ->count();

            $allFgrLeads = $fgrLeads + $fgrFromLeads;

            $checkIns = DB::table('user_check_ins')
                ->select(
                    DB::raw("DATE(check_in_date) as check_in_day"),
                    DB::raw("MIN(check_in_date) as first_check_in")
                )
                ->where('user_id', $agent->id)
                ->whereBetween('check_in_date', [$startDate, $endDate])
                ->groupBy(DB::raw("DATE(check_in_date)"))
                ->get();

            // $checkIns = $checkIns->filter(function ($record) {
            //     return in_array(Carbon::parse($record->check_in_day)->dayOfWeek, [0, 1, 2, 3, 4]);
            // });

            $daysNotOnTime = $checkIns->filter(function ($record) {
                return date('H:i:s', strtotime($record->first_check_in)) > '09:16:00';
            })->count();

            $daysOnTime = $checkIns->filter(function ($record) {
                return date('H:i:s', strtotime($record->first_check_in)) <= '09:16:00';
            })->count();

            $closedDeals = DB::table('deals')
                ->where(function ($q) use ($agent) {
                    $q->where('closing_agent_id', $agent->id);
                })
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            return [
                'agent_id'             => $agent->id,
                'agent_name'           => $agent->name,
                'listings_created'     => empty($listings) ? '-' : $listings,
                'self_generated_leads' => empty($selfLeads) ? '-' : $selfLeads,
                'leads_from_fgr'       => empty($allFgrLeads) ? '-' : $allFgrLeads,
                'days_not_on_time'     => empty($daysNotOnTime) ? '-' : $daysNotOnTime,
                'days_on_time'         => empty($daysOnTime) ? '-' : $daysOnTime,
                'closed_deals_count'   => empty($closedDeals) ? '-' : $closedDeals
            ];
        })->sortBy('agent_name');

        return Excel::download(new AgentReportExport($finalReport), 'agent_report.xlsx');
    }

    public function nohasLeads()
    {
        // Get unique contacts directly by joining with leads table
        $uniqueContacts = Contact::select('contacts.*')
            ->join('leads', 'contacts.id', '=', 'leads.contact_id')
            ->whereRaw("(leads.filter_operation_type LIKE 'rent' AND leads.filter_budget_min >= 9000) OR ((leads.filter_bedrooms >= 1 AND leads.filter_bedrooms <= 2) AND leads.location_id IN (6, 28, 13, 34, 1066,  2, 15, 16, 17, 18, 19, 20, 21, 22, 84, 165, 185, 186, 1003, 1071, 35) )")
            ->whereRaw("CONCAT(REPLACE(contacts.prefix_mobile_1, '+', ''), contacts.mobile_1) NOT IN (SELECT phone_number FROM failed_messages)")
            ->whereRaw("contacts.mobile_1 NOT IN (SELECT phone_number FROM failed_messages)")
            ->whereRaw("LENGTH(CONCAT(REPLACE(contacts.prefix_mobile_1, '+', ''), contacts.mobile_1)) <= 15")
            ->whereNull('leads.deleted_at') // Ensure we don't include soft-deleted leads
            ->where('name', 'NOT LIKE', '%test%')
            ->where('name', 'NOT LIKE', '%ITDEV%')
            ->groupBy('contacts.id') // This ensures we get unique contacts only
            ->get();

        // Prepare data for export.
        $exportData = $uniqueContacts->map(function ($contact) {
            return [
                'Phone' => $this->getCompletePhoneNo($contact->prefix_mobile_1, $contact->mobile_1),
                'Name'  => $contact->contactName(),
            ];
        })->toArray();

        // Return an Excel download.
        return Excel::download(new NohasContactsApril06($exportData), 'rent-above-9000.xlsx');
    }

    private function getCompletePhoneNo($prefix, $phone)
    {
        $mobilePhone = $phone;
        $phone = str_replace(['(', ')', ' '], '', $phone);
        if (!empty($prefix) && !\Str::startsWith($phone, "+") && !\Str::startsWith($phone, "00")) {
            $mobilePhone = $prefix . $phone;
        } else if (empty($prefix) && !\Str::startsWith($phone, "+") && !\Str::startsWith($phone, "00") && !\Str::startsWith($phone, "974")) {
            $mobilePhone = "+974" . $phone;
        }
        $mobilePhone = str_replace(' ', '', $mobilePhone);
        return $mobilePhone;
    }
}
