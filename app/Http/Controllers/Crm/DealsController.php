<?php

namespace App\Http\Controllers\Crm;

use App\Http\Requests\DealUpdateRequest;
use App\Models\Contact;
use App\Models\Crm\ContactsListTag;
use App\Models\Crm\Deal;
use App\Models\Crm\PermissionsDef;
use App\Models\Crm\Reminder;
use App\Models\Crm\RolesDef;
use App\Models\Invoice;
use App\Models\Lead;
use App\Models\Property;
use App\Models\PropertySnapshot;
use App\Services\DealsService;
use Illuminate\Http\Request;
use App\Services\AttachmentsService;
use App\Services\CountriesService;
use App\Services\EmailService;
use App\Services\LeadsService;
use App\Services\NotesService;
use App\Services\OperationHistoryService;
use Illuminate\Support\Facades\Storage;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Gate as FacadesGate;
use Log;

class DealsController extends FormController
{
    protected $modelClass = 'App\Models\Crm\Deal';
    protected $formView = 'crm.deal.form.form';
    protected $indexView = "crm.deal.index";
    protected $hasEnvelope = true;

    protected $itemFetcher;
    protected $ajaxListMapper;
    protected $listRoute = 'crm.deals.index';
    protected $createPostRoute = null;
    protected $updatePostRoute = 'crm.deals.update';
    protected $deletePostRoute = 'crm.deals.delete.post';
    protected $editRoute = 'crm.deals.edit';

    protected $deleteFunction;

    protected $afterItemUpdate;

    protected $updateValidator;
    protected $dealsService;
    protected $attachmentsService;
    protected $operationHistoryService;
    protected $emailService;
    private $userService;
    private $countriesService;
    private $notesService;
    protected $leadsService;

    public function __construct(
        NotesService $notesService,
        Request $request,
        DealsService $dealsService,
        AttachmentsService $attachmentsService,
        OperationHistoryService $operationHistoryService,
        EmailService $emailService,
        UserService $userService,
        CountriesService $countriesService,
        LeadsService $leadsService
    ) {
        $this->notesService = $notesService;
        $this->dealsService = $dealsService;
        $this->attachmentsService = $attachmentsService;
        $this->operationHistoryService = $operationHistoryService;
        $this->emailService = $emailService;
        $this->userService = $userService;
        $this->countriesService = $countriesService;
        $this->leadsService = $leadsService;
        $extraConfig = $this->dealsService->getExtraConfig($request);
        $this->itemFetcher = function ($offset = 0, $limit = 10) use ($extraConfig) {
            return $this->dealsService->fetchItems($extraConfig, $offset, $limit);
        };
        $this->ajaxListMapper = $this->dealsService->ajaxListMapper;

        $this->updateValidator = function ($controller, $request) {
            $validator = Validator::make(request()->all(), [
                'price' => 'required',
                'agent_id_client' => '',
                'agent_id_landlord' => '',
                'client-id' => 'required',
                'client-name' => 'required',
                'client-prefix_mobile_1' => 'required',
                'client-mobile_1' => 'required',
                'client-email_1' => 'required',
                'client-date_of_birth' => '',
                'client-qatar_id_no' => '',
                'owner-id' => 'required',
                'owner-name' => 'required',
                'owner-prefix_mobile_1' => 'required',
                'owner-mobile_1' => 'required',
                'owner-email_1' => 'required',
                'owner-date_of_birth' => '',
                'owner-qatar_id_no' => '',
            ], $this->validationMessages);

            if ($validator->fails()) {
                return redirect('crm/deals/' . request()->get('id') . '/edit')
                    ->withErrors($validator)
                    ->withInput();
            }
        };

        $this->afterItemUpdate = function ($request, $item) {
            $this->handleFormsFilesUploads($item, $request);
            $this->updateRelatedContacts($item, $request);
            $this->dealsService->handleDealsAgents($item, $request);
            $this->handleOperationHistory($item);
        };
    }

    public function replaceLeadFormsPath($path, $dealId)
    {
        return preg_replace('/lead-forms\/\d+/', 'deal-forms/' . $dealId, $path);
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            if ($this->itemFetcher) {
                $offset = empty($request->query('start')) ? 0 : $request->query('start');
                $limit = empty($request->query('length')) ? 0 : $request->query('length');
                $items = call_user_func($this->itemFetcher, $offset, $limit);
            } else {
                $items = call_user_func($this->modelClass . '::all');
            }

            if (isset($items['items'])) {
                if (is_array($items['items'])) {
                    $data = array_map($this->ajaxListMapper, $items['items']);
                } else {
                    $data = $items['items']->map($this->ajaxListMapper);
                }
            } else {
                if (is_array($items)) {
                    $data = array_map($this->ajaxListMapper, $items);
                } else {
                    $data = $items->map($this->ajaxListMapper);
                }
            }

            if (isset($this->hasEnvelope)) {
                $payload = ['data' => $data, 'recordsTotal' => $items['count'] ?? 0, 'recordsFiltered' => $items['count'] ?? 0];
                return response()->json($payload);
            }

            return response()->json(["data" => $data]);
        }

        $vt = request()->get('vt', 'default');
        if (!in_array($vt, ['default', 'team_list', 'pending', 'approved', 'finalized'])) {
            $vt = 'default';
        }
        $agents = $this->userService->getPossibleListingOwners();
        $teamLeaderAgents = getTeamLeaderAgents();
        $user = auth()->user();
        $userIsAgent = $user->hasRole(RolesDef::AGENT);
        $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
        $userIsManager = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $sources = $this->leadsService->getCachedSources();

        return view($this->indexView, compact('vt', 'userIsAgent', 'userIsTeamLeader', 'userIsManager', 'agents', 'sources', 'teamLeaderAgents'));
    }

    protected function applyModelValues(Request $request, $item)
    {
        foreach ($request->all() as $column => $value) {
            if (
                (
                    isFillableColumn($column) ||
                    in_array($column, ['listing_agent_shared_commission', 'referred_listing_agent_shared_commission', 'closing_agent_shared_commission', 'referred_closing_agent_shared_commission'])
                ) &&
                !in_array($column, ['agent_id_landlord', 'agent_id_client', 'deal-forms', 'deal-forms-remove', 'documents_options', 'operation-history', 'listingId', 'sendEmails', 'listing_agent_month', 'referred_listing_agent_month', 'closing_agent_month', 'referred_closing_agent_month'])
                && !Str::startsWith($column, 'client-') && !Str::startsWith($column, 'owner-')
            ) {
                $value = $request->input($column);
                $item->$column = $value;
            }
        }
    }

    private function handleOperationHistory($item)
    {
        $validFields = request()->validate([
            'operation-history' => '',
        ]);

        if (!empty($validFields['operation-history'])) {
            $this->operationHistoryService->addOperationHistory($item, $validFields['operation-history'], auth()->user());

            $deal = $item->with('author')->where('id', $item->id)->first();
            $this->emailService->sendEmailOnAddingOperationHistoryForDeals($deal, auth()->user(), $validFields['operation-history']);
        }
    }

    private function handleFormsFilesUploads($deal, $request)
    {
        $validFields = request()->validate([
            'deal-forms' => 'array',
            'documents_options' => 'array',
            'deal-forms-remove' => 'array',
            'sendEmails' => '',
        ]);

        $formIndexesToRemove = $validFields['deal-forms-remove'] ?? [];
        $itemForms = $deal->forms ?? [];
        $cleanupForms = [];
        if (count($formIndexesToRemove)) {
            foreach ($itemForms as $index => $itemForm) {
                $deleted = false;
                if (in_array($index, $formIndexesToRemove)) {
                    $deleted = $this->attachmentsService->removeDealForm($deal->id, $itemForm->path);
                }
                if (!$deleted) {
                    $cleanupForms[] = $itemForm;
                }
            }
        } else {
            $cleanupForms = $itemForms;
        }
        $forms = $validFields['deal-forms'] ?? null;
        $formsOptions = $validFields['documents_options'] ?? [];
        $uploadedForms = [];
        if (!is_null($forms)) {
            $uploadedForms = $this->attachmentsService->uploadDealFormFiles($deal, $forms, $formsOptions);
        }
        $allForms = array_merge($cleanupForms, $uploadedForms);
        $deal->forms = $allForms;

        if ($deal->deal_status == Deal::STATUS_APPROVED && isset($validFields['sendEmails'])) {
            $notSentForms = [];
            foreach ($deal->forms as $formObject) {
                if (property_exists($formObject, 'sentAt') && is_null($formObject->sentAt)) {
                    $notSentForms[] = $formObject;
                    $formObject->sentAt = (new \DateTime())->format('d/m/Y');
                }
            }

            if (count($notSentForms) > 0) {
                $this->emailService->sendDocumentsToClientOrOwner($deal, $notSentForms, 'client');
                $this->emailService->sendDocumentsToClientOrOwner($deal, $notSentForms, 'owner');
            }
        }
        $deal->save();
    }

    public function previewForm($dealId)
    {
        if (request()->has('filePath')) {
            $filePath = request()->get('filePath');
            $deal = Deal::find($dealId);
            $forms = $deal->forms ?? [];

            $filteredFiles = array_filter($forms, function ($formFile) use ($filePath) {
                return $formFile->path == $filePath;
            });

            if (count($filteredFiles) > 0) {
                $firstArrayPos = array_values($filteredFiles)[0];
                $replacePath = $this->replaceLeadFormsPath($firstArrayPos->path, $dealId);
                $fullPath = Storage::path($replacePath);

                if (!file_exists($fullPath)) {
                    return abort(404, 'File not found');
                }

                $extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
                $viewableExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];

                if (in_array($extension, $viewableExtensions)) {
                    return response()->file($fullPath, [
                        'Content-Type' => mime_content_type($fullPath),
                        'Content-Disposition' => 'inline; filename="' . $firstArrayPos->title . '"'
                    ]);
                }

                return Storage::download($firstArrayPos->path, $firstArrayPos->title);
            }
        }

        return response('', 400);
    }

    public function downloadForm($dealId)
    {
        if (request()->has('filePath')) {
            $filePath = request()->get('filePath');
            $deal = Deal::find($dealId);
            $forms = $deal->forms ?? [];
            $filteredFiles = array_filter($forms, function ($formFile) use ($filePath) {
                return $formFile->path == $filePath;
            });
            if (count($filteredFiles) > 0) {
                $firstArrayPos = array_values($filteredFiles)[0];
                $replacePath = $this->replaceLeadFormsPath($firstArrayPos->path, $dealId);
                return Storage::download($replacePath, $firstArrayPos->title);
                // return Storage::download($firstArrayPos->path, $firstArrayPos->title);
            }
        }

        return response();
    }

    public function updateStatus($dealId)
    {
        $authUser = auth()->user();
        $deal = Deal::with(['owner', 'client', 'author'])->where('id', '=', $dealId)->firstOrFail();
        $dealAuthor = $deal->author;
        $dealAuthorTeamLeader = !is_null($dealAuthor) ? $dealAuthor->teamLeader : null;
        $dealAuthorTeamLeaderId = !is_null($dealAuthorTeamLeader) ? $dealAuthorTeamLeader->id : null;

        // if (!$authUser->hasRole(RolesDef::ACCOUNTANT) && auth()->user()->id != $dealAuthorTeamLeaderId) {
        //     return response(null, 403);
        // }

        $lead = Lead::with(['contact'])->where('id', '=', $deal->lead_id)->first();

        $validData = request()->validate([
            'status' => 'required',
            'operationHistory' => '',
            'sendEmails' => ''
        ]);

        if ($validData['status'] == Deal::STATUS_FINALIZED) {
            $hasReceipt = false;
            foreach ($deal->forms as $document) {
                if ($document->option == 'receipt') {
                    $hasReceipt = true;
                }
            }
            if (!$hasReceipt) {
                return response()->json(['error' => 'In order to finalize the deal, you must add a receipt'], 422);
            }
        }
        $property = Property::with('theTower', 'location')->where('id', '=', $deal->property_id)->first();
        $propertySnapshot = PropertySnapshot::with('theTower', 'location')->where('listing_id', $property->id)->first();

        if (is_null($deal) || $deal->deal_status == $validData['status']) {
            return response("Nothing to do");
        }

        $condition1Constraints = $validData['status'] != Deal::STATUS_FINALIZED && $authUser->hasRole(RolesDef::TEAM_LEADER) && auth()->user()->id == $dealAuthorTeamLeaderId;
        $condition2Constraints = $validData['status'] == Deal::STATUS_FINALIZED && $authUser->hasRole(RolesDef::ACCOUNTANT);
        $condition3Constraints = $authUser->hasRole(RolesDef::TL_DEALS_APPROVER);

        if (!($condition1Constraints || $condition2Constraints || $condition3Constraints)) {
            return response(null, 403);
        }

        $deal->deal_status = $validData['status'];

        $this->operationHistoryService->addOperationHistory(
            $deal,
            'Deal status updated to [' . $validData['status'] . ']',
            $authUser
        );
        if (!empty($validData['operationHistory'])) {
            $this->operationHistoryService->addOperationHistory($deal, $validData['operationHistory'], $authUser);
        }
        $deal->save();

        // @TODO clarify with Usama when this should happen
        if ($validData['status'] == 'approved') {
            $property->status = 'rented';
            $property->rent_end_date = $deal->end_date;
            $property->save();

            $this->operationHistoryService->addOperationHistory($lead->contact, 'Deal [' . $this->dealsService->computeDealRefNo($deal->id) . '] was closed with status approved for listing[' . $property->ref_no . ']', auth()->user());
            $lead->state = Lead::APPROVED;
            $lead->save();
            $this->emailService->sendEmailToAgentAfterDealApproval($deal);
        }
        if ($deal->type === "RENTAL") {
            $this->operationHistoryService->addOperationHistory($lead->contact, 'The contact became tenant for [' . $property->ref_no . ']', auth()->user());
        }

        $clientDestinationEmail = '';
        if ($deal->client) {
            if (!is_null($deal->client->email_1)) {
                $clientDestinationEmail = $deal->client->email_1;
            } else if (!is_null($deal->client->email_2)) {
                $clientDestinationEmail = $deal->client->email_2;
            }
        }

        $landlordDestinationEmail = '';
        if ($deal->owner) {
            if (!is_null($deal->owner->email_1)) {
                $landlordDestinationEmail = $deal->owner->email_1;
            } else if (!is_null($deal->owner->email_2)) {
                $landlordDestinationEmail = $deal->owner->email_2;
            }
        }
        if ($validData['sendEmails'] == '1') {
            if (!empty($clientDestinationEmail)) {
                $this->emailService->sendEmailToClientWhenDealAreClosed($deal, $clientDestinationEmail, $propertySnapshot);
            }
            if (!empty($landlordDestinationEmail)) {
                $this->emailService->sendEmailToLandlordWhenDealAreClosed($deal, $landlordDestinationEmail, $propertySnapshot);
            }
            $allForms = [];
            foreach ($deal->forms as $formObject) {
                $formObject->sentAt = (new \DateTime())->format('d/m/Y');
                $allForms[] = $formObject;
            }
            $deal->forms = $allForms;
            $deal->save();
        }

        $this->dealsService->handleDealsAgents($deal, request());

        if ($deal->type == 'RENTAL') {
            // FGR Tenant
            $dbItem = Contact::where('id', $deal->client->id)->with(['tags'])->first();
            $FGRTenantTagInTags = $dbItem->tags->filter(fn($tag) => strpos($tag->label, 'FGR Tenant') > -1)->first();
            if (is_null($FGRTenantTagInTags)) {
                $fgrTenantTag = ContactsListTag::where('label', 'FGR Tenant')->first();
                if (!is_null($fgrTenantTag)) {
                    $dbItem->tags()->attach($fgrTenantTag->id);
                }
            }
        } elseif ($deal->type == 'SALE') {
            // FGR Landlord
            $dbItem = Contact::where('id', $deal->client->id)->with(['tags'])->first();
            $FGRLandlordTagInTags = $dbItem->tags->filter(fn($tag) => strpos($tag->label, 'FGR Landlord') > -1)->first();
            if (is_null($FGRLandlordTagInTags)) {
                $fgrLandlordTag = ContactsListTag::where('label', 'FGR Landlord')->first();
                if (!is_null($fgrLandlordTag)) {
                    $dbItem->tags()->attach($fgrLandlordTag->id);
                }
            }
        }

        if ($validData['status'] == Deal::STATUS_FINALIZED) {
            $this->generateDealPDF($deal);
        }

        return response(null, 200);
    }

    public function paymentStatusToDeal($dealId)
    {
        $validData = request()->validate([
            'landlordCommission' => '',
            'clientCommission' => '',
            'operationHistory' => ''
        ]);
        $deal = Deal::where('id', '=', $dealId)->first();
        $authUser = auth()->user();

        if ($deal->landlord_commission_cashed != $validData['landlordCommission']) {
            $deal->landlord_commission_cashed = $validData['landlordCommission'];

            if (!empty($validData['landlordCommission'])) {
                $this->operationHistoryService->addOperationHistory(
                    $deal,
                    'Deal payment status updated to [' . $validData['landlordCommission'] . ']',
                    $authUser
                );
            }
        }

        if ($deal->client_commission_cashed != $validData['clientCommission']) {
            $deal->client_commission_cashed = $validData['clientCommission'];

            if (!empty($validData['clientCommission'])) {
                $this->operationHistoryService->addOperationHistory(
                    $deal,
                    'Deal payment status updated to [' . $validData['clientCommission'] . ']',
                    $authUser
                );
            }
        }

        if (!empty($validData['operationHistory'])) {
            $this->operationHistoryService->addOperationHistory($deal, $validData['operationHistory'], $authUser);
        }
        $deal->save();
    }

    public function getDealDetails($dealId)
    {
        $deal = Deal::with(['client', 'owner', 'property', 'lead', 'property.owner', 'author'])->findOrFail($dealId);
        $deal['dealDocumentURL'] = $deal->deal_status == Deal::STATUS_FINALIZED ? route('deal.pdf.generateDealPDF', ['id' => $deal->id]) : '';
        return $deal->toArray();
    }

    public function getAdditionalData($action)
    {
        $user = auth()->user();
        $countries = $this->countriesService->getCountries();
        $userHasAccessToDealFiles = $user->hasPermissionTo(PermissionsDef::HAS_ACCESS_TO_DEAL_FILES);
        $dealId = request()->route('id');
        $item = Deal::where('id', $dealId)->first();

        $userCanEditDocuments = ($item->deal_status == Deal::STATUS_PENDING || $item->deal_status == Deal::STATUS_REJECTED) && ($user->id == $item->created_by || $user->id == $item->closing_agent_id);
        $formIsReadonly = true;
        if ($user->hasRole(RolesDef::ACCOUNTANT) || $user->hasRole(RolesDef::OFFICE_MANAGER)) {
            $formIsReadonly = false;
        }

        return [
            'countries' => $countries,
            'userHasAccessToDealFiles' => $userHasAccessToDealFiles,
            'userCanEditDocuments' => $userCanEditDocuments,
            'formIsReadonly' => $formIsReadonly
        ];
    }

    public function postUpdateCustom(DealUpdateRequest $dealUpdateRequest, $id)
    {
        $deal = Deal::where('id', $id)->first();
        $item = call_user_func($this->modelClass . '::find', $id);
        if ($this->currentUserCanAccessCurrentDeal($deal)) {
            $this->handleFormsFilesUploads($item, request());
            $this->handleOperationHistory($item);
        } else {
            $this->applyModelValues(request(), $item);
            $requestListingId = request()->get('listingId', null);
            if (!is_null($requestListingId)) {
                $oldProperty = $item->property;
                $newProperty = Property::firstWhere('id', $requestListingId);
                $oldRefNo = is_null($oldProperty) ? 'N/A' : $oldProperty->ref_no;
                $newRefNo = is_null($newProperty) ? 'N/A' : $newProperty->ref_no;
                $this->operationHistoryService->addOperationHistory($item, "Listing changed from [" . $oldRefNo . "] to [" . $newRefNo . "]", auth()->user());
                $item->property_id = $requestListingId;
            }
            if ($deal->end_date != $dealUpdateRequest->end_date) {
                $this->createReminderForDealEndDate($deal, $dealUpdateRequest);
            }

            $item->save();

            if (isset($this->afterItemUpdate)) {
                call_user_func($this->afterItemUpdate, request(), $item);
            }
        }


        return redirect()->route($this->editRoute, ['id' => $id])->with('message.success', 'The deal has been updated');
    }

    public function updateRelatedContacts($deal, $request)
    {
        // handle the client and landlord update
        $client = $deal->client;
        if (!is_null($client)) {
            foreach (['name', 'qatar_id_no', 'prefix_mobile_1', 'mobile_1', 'date_of_birth', 'email_1'] as $modelFieldName) {
                $fieldName = 'client-' . $modelFieldName;
                $fieldValue = $request->get($fieldName);
                if ($client->$modelFieldName != $fieldValue) {
                    $client->$modelFieldName = $fieldValue;
                }
            }
            if ($client->isDirty()) {
                $client->save();
            }
        }

        $owner = $deal->owner;
        if (!is_null($owner)) {
            foreach (['name', 'qatar_id_no', 'prefix_mobile_1', 'mobile_1', 'date_of_birth', 'email_1'] as $modelFieldName) {
                $fieldName = 'owner-' . $modelFieldName;
                $fieldValue = $request->get($fieldName);
                if ($owner->$modelFieldName != $fieldValue) {
                    $owner->$modelFieldName = $fieldValue;
                }
            }
            // dd($owner, $request->all());
            if ($owner->isDirty()) {
                $owner->save();
            }
        }
    }

    public function createReminderForDealEndDate($deal, $request)
    {
        if (isset($request['end_date']) && $request['end_date'] && $request['end_date'] != "") {
            //Create note end reminder for contract expiry

            $expiryDate = new Carbon($request['end_date']);
            $reminder_date = new Carbon($request['end_date']);
            $reminder_date->subMonths(2)->toDateTimeString();

            $reminderData
                = [
                    "title" => "Contract renewal follow-up for " . $deal->client->name,
                    "text" => "You should contact " . $deal->client->name . ". His/her contract on " . $deal->property->ref_no . " is about to expire on " . $expiryDate->toDateTimeString(),
                    "priority" => "MEDIUM",
                    "due_date" => $expiryDate,
                    "reminder_email_date" => $reminder_date,
                    "reminder_email" => "1",
                    "is_for_deal_end_date" => "1"
                ];

            $oldReminder = Reminder::where('object_id', $deal->id)
                ->where('object_type', 'deals')
                ->where('is_for_deal_end_date', '1')
                ->first();

            if (!is_null($oldReminder)) {
                $oldReminder->forceDelete();
            }

            $reminder = $this->notesService->createReminderWithObject($reminderData, $deal);
        }
    }

    public function downloadDocuments($id)
    {
        $file_path = request()->get('path');
        $formTitle = request()->get('title');
        return Storage::download($file_path, $formTitle);
    }

    public function handleOldDealsReminders()
    {
        return "Already executed";
        $deals = Deal::with(['client', 'property'])->whereRaw("end_date > NOW() AND id NOT IN (SELECT
                r.object_id
            FROM
                reminders r
            WHERE
                r.is_for_deal_end_date = 1)
        ")->get();

        foreach ($deals as $index => $deal) {
            $clientFullName
                = concatValues(
                    $deal->client->name,
                    " ",
                    ""
                );
            $expiryDate = new Carbon($deal->end_date);
            $reminder_date = new Carbon($deal->end_date);
            $reminder_date->subMonths(2)->toDateTimeString();
            $reminderData = [
                "title" => "Contract renewal follow-up for " . $clientFullName,
                "text" => "You should contact " . $clientFullName . ". His/her contract on " . $deal->property->ref_no . " is about to expire on " . $expiryDate->toDateTimeString(),
                "priority" => "MEDIUM",
                "due_date" => $expiryDate,
                "reminder_email_date" => $reminder_date,
                "reminder_email" => "1",
                "is_for_deal_end_date" => "1"
            ];
            $this->notesService->createReminderWithObject($reminderData, $deal);
        }
    }

    public function downloadDealDocument($id)
    {
        $deal = Deal::where('id', $id)->firstOrFail();
        $refNo = $this->dealsService->getDealRefNo($deal);
        $filePath = 'deals/deal_' . $refNo . '.pdf';
        // echo $filePath;
        return Storage::download($filePath);
    }

    private function generateDealPDF($deal)
    {
        // Fetch the deal with related information
        // $deal = Deal::with(['property.theTower', 'listingAgent', 'closingAgent', 'lead.location', 'client', 'owner'])
        //     ->where('id', $dealId)
        //     ->firstOrFail();

        $leadDocumentTypes = LeadsService::$documentsListType;

        $realEstateService = null;
        foreach ($leadDocumentTypes as $entry) {
            if ($entry['value'] == $deal->lead->documents_type) {
                $realEstateService = $entry['label'];
                break;
            }
        }

        $adType = null;
        $documentChecklist = [];
        if (!empty($deal->lead->documents_type)) {
            if ($deal->lead->documents_type == 'residentialRent' || $deal->lead->documents_type == 'commercialRent') {
                $adType = 'rent';
            }
            if ($deal->lead->documents_type == 'residentialSale' || $deal->lead->documents_type == 'commercialSale') {
                $adType = 'sale';
            }
            if ($deal->lead->documents_type == 'residentialRent') {
                $documentChecklist = $this->leadsService->residentialRentOptions;
            } elseif ($deal->lead->documents_type == 'residentialSale') {
                $documentChecklist = $this->leadsService->residentialSaleOptions;
            } elseif ($deal->lead->documents_type == 'commercialRent') {
                $documentChecklist = $this->leadsService->commercialRentOptions;
            } elseif ($deal->lead->documents_type == 'commercialSale') {
                $documentChecklist = $this->leadsService->commercialSaleOptions;
            }
        }
        if (is_null($adType)) {
            if (!empty($deal->lead->filter_operation_type)) {
                $adType = 'sale';
                if ($deal->lead->filter_operation_type == 'rent') {
                    $adType = 'rent';
                }
            }
        }

        $listingOwner = $deal->property->contact->contactName();
        $leadClient = $deal->lead->contact->contactName();
        $dealRefNo = "D" . str_pad($deal->id, 6, '0', STR_PAD_LEFT);
        // Prepare data for the PDF
        $data = [
            'deal_ref_no' => $dealRefNo,
            'date' => $deal->created_at->format('d/m/Y'),
            'property_ref_no' => $deal->property->ref_no ?? 'N/A',
            'location' => $deal->property->location->path() ?? 'N/A',
            'building_name' => $deal->property->theTower->name ?? 'N/A',
            'unit_number' => $deal->unit_number ?? 'N/A',
            'real_estate_service' => $realEstateService ?? "N/A",
            'landlord_name' => $deal->owner->name ?? 'N/A',
            'tenant_name' => $deal->client->name ?? 'N/A',
            'seller_name' => $deal->type === 'SALE' ? $deal->owner->name ?? 'N/A' : 'N/A',
            'buyer_name' => $deal->type === 'SALE' ? $deal->client->name ?? 'N/A' : 'N/A',
            'listing_agent_name' => $deal->listingAgent->name ?? 'N/A',
            'listing_agent_shared_commission' => $deal->listing_agent_shared_commission ?? '',
            'listing_referral_agent_name' => $deal->referredListingAgent->name ?? 'N/A',
            'referred_listing_agent_shared_commission' => $deal->referred_listing_agent_shared_commission ?? '',
            'listing_agent_amount' => $deal->listing_agent_shared_commission ?? 'N/A',
            'closing_agent_name' => $deal->closingAgent->name ?? 'N/A',
            'closing_agent_amount' => $deal->closing_agent_shared_commission ?? '',
            'referred_closing_agent_name' => $deal->closingReferralAgent->name ?? 'N/A',
            'referred_closing_agent_amount' => $deal->referred_closing_agent_shared_commission     ?? '',
            'lead_source' => $deal->lead->leadSource ?  $deal->lead->leadSource->name : 'N/A',
            'document_checklist' => $documentChecklist,
            'ad_type' => $adType,
            'listing_owner_name' => $listingOwner,
            'lead_client_name' => $leadClient
        ];

        $pdf = PDF::loadView('crm.deal.deal-document', $data);
        $pdf->save('deals/deal_' . $dealRefNo . '.pdf', 'local');
        Log::info('Generated PDF for deal ' . $dealRefNo);

        // return $pdf->download('deal.pdf');
    }

    private function currentUserCanAccessCurrentDeal(Deal $deal)
    {
        $user = auth()->user();
        $deal->load(['author', 'author.teamLeader', 'closingAgent']);
        return (in_array($deal->deal_status, [Deal::STATUS_PENDING, Deal::STATUS_REJECTED])) && ($deal->created_by == $user->id || $deal->closing_agent_id == $user->id || (!is_null($deal->author->teamLeader) && $deal->author->teamLeader->id == $user->id));
    }

    public function viewInvoicePDF($dealId, $invoiceId)
    {
        $invoice = Invoice::with(['deal.property.theTower', 'deal.property.location', 'deal.client', 'deal.owner', 'deal.listingAgent', 'deal.closingAgent'])
            ->where('id', $invoiceId)
            ->where('deal_id', $dealId)
            ->firstOrFail();

        $pdf = $this->generateInvoicePDF($invoice);

        return $pdf->stream('invoice_' . $invoice->ref_no . '.pdf');
    }

    public function downloadInvoicePDF($dealId, $invoiceId)
    {
        $invoice = Invoice::with(['deal.property.theTower', 'deal.property.location', 'deal.client', 'deal.owner', 'deal.listingAgent', 'deal.closingAgent'])
            ->where('id', $invoiceId)
            ->where('deal_id', $dealId)
            ->firstOrFail();

        $pdf = $this->generateInvoicePDF($invoice);

        return $pdf->download('invoice_' . $invoice->ref_no . '.pdf');
    }

    private function generateInvoicePDF($invoice)
    {
        $deal = $invoice->deal;

        // Prepare data for the PDF
        $data = [
            'invoice' => $invoice,
            'deal' => $deal,
            'property' => $deal->property,
            'tower' => $deal->property->theTower,
            'location' => $deal->property->location,
            'client' => $deal->client,
            'owner' => $deal->owner,
            'listing_agent' => $deal->listingAgent,
            'closing_agent' => $deal->closingAgent,
            'invoice_date' => now()->format('d/m/Y'),
            'logo_path' => public_path('images/svg/FGREALTY.svg'),
        ];

        return PDF::loadView('crm.invoice.invoice-pdf', $data);
    }

    public function delete(Request $request, $id)
    {
        if (FacadesGate::check(PermissionsDef::DEALS_DELETE)) {
            return parent::delete($request, $id);
        }

        return response("Not authorized", 403);
    }
}
