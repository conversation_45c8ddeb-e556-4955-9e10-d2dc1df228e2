<?php

namespace App\Http\Controllers\Crm;

use App\Exports\ContactsNAExport;
use App\Http\Controllers\CrmBaseController;
use App\Models\Contact;
use App\Models\ContactLandlordData;
use App\Models\Crm\Deal;
use App\Models\Crm\PermissionsDef;
use App\Models\Crm\RolesDef;
use App\Models\Lead;
use App\Models\Nationality;
use App\Models\Property;
use App\Models\PropertySnapshot;
use App\Models\Task;
use App\Models\User;
use App\Rules\BrokerLandlordUniqueContactEmail;
use App\Rules\BrokerLandlordUniqueContactPhoneNumber;
use App\Services\AttachmentsService;
use App\Services\BrokerLandlordsService;
use App\Services\EmailService;
use App\Services\OperationHistoryService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use App\Services\CountriesService;
use App\Services\UserService;
use Excel;
use DB;

class BrokerLandlordsControllerCrm extends CrmBaseController
{
    protected $views = [
        'index' => 'crm.broker-landlords.index',
        'create' => 'crm.broker-landlords.form.form',
        'edit' => 'crm.broker-landlords.form.form',
    ];

    protected $routes = [
        'index' => 'broker-landlords.index',
        'edit' => 'broker-landlords.edit'
    ];

    protected $validationMessages = [
        'first_name.required' => 'This field is required',
        'qatar_id_no.required_if' => 'This field is required when landlord type is private',
        'email_1.required' => 'This field is required',
        'email_1.email' => 'This field should be a valid email',
        'mobile_1.required' => 'This field is required',
        'mobile_1.regex' => 'The format of the field is invalid',
        'mobile_1.max' => 'The phone number should contain at least 4 chars and max 40 characters.',
        'mobile_1.min' => 'The phone number should contain at least 4 chars and max 40 characters.',
        'mobile_2.regex' => 'The format of the field is invalid.',
        'mobile_2.max' => 'The phone number should contain at least 4 chars and max 40 characters.',
        'mobile_2.min' => 'The phone number should contain at least 4 chars and max 40 characters.',
        'nationality_id.required' => 'This field is required',
        'is_corporate_landlord.required' => 'This field is required',
        'landlordData-is_corporate_landlord.required' => 'This field is required',
        // 'verified.required' => 'This field is required',
        'contact-availability_list.invalid' => 'Invalid file type for Availability list',
        'landlordData-payment_booking_fee.required' => 'This field is required',
        'landlordData-payment_cheque_addressed_to.required' => 'This field is required',
        'landlordData-payment_closing_requiring_documents.required' => 'This field is required',
        'landlordData-payment_security_deposit_dated.required' => 'This field is required',
        'landlordData-booking_form.required' => 'This field is required',
    ];

    protected $brokerLandlordsService;
    protected $attachmentsService;
    protected $emailService;
    protected $operationHistoryService;
    protected $countriesService;
    private $userService;


    public function __construct(
        CountriesService $countriesService,
        BrokerLandlordsService $brokerLandlordsService,
        AttachmentsService $attachmentsService,
        EmailService $emailService,
        OperationHistoryService $operationHistoryService,
        UserService $userService
    ) {
        $this->brokerLandlordsService = $brokerLandlordsService;
        $this->attachmentsService = $attachmentsService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->countriesService = $countriesService;
        $this->userService = $userService;
    }

    public function getFieldsToValidate(string $actionName, $itemId = null)
    {
        // dd(request()->all());
        if ($actionName === 'store') {
            return [
                // 'qatar_id_no' => [
                //     function ($attribute, $value, $fail) {
                //         if (request()->get('landlordData-is_corporate_landlord', "0") == "0") {
                //             if (empty(trim($value))) {
                //                 $fail('This field is mandatry');
                //             } else {
                //                 $contactsExist = Contact::where('qatar_id_no', $value)
                //                     ->first();
                //                 if (!is_null($contactsExist)) {
                //                     $fail('This Qatar ID No is already registered in the system.');
                //                 }
                //             }
                //         }
                //     }
                // ],
                'qatar_id_no' => '',
                'name' => 'required',
                'email_1' => [
                    'required',
                    new BrokerLandlordUniqueContactEmail()
                ],
                'prefix_mobile_1' => '',
                'mobile_1' => [
                    'min:4',
                    'max:40',
                    'regex:/^[0-9]*$/',
                    new BrokerLandlordUniqueContactPhoneNumber()
                ],
                'prefix_mobile_2' => '',
                'mobile_2' => [
                    'nullable',
                    'max:40',
                    'regex:/^[0-9]*$/',
                    new BrokerLandlordUniqueContactPhoneNumber()
                ],
                'unit_number' => '',
                'date_of_birth' => '',
                'last_contact_date' => '',
                'contract_end_date' => '',
                'operation-history' => '',
                'notes' => '',
                'nationality_id' => 'nullable',
                'landlordData-is_corporate_landlord' => [
                    Rule::requiredIf(function () {
                        $user = auth()->user();
                        $userIsAdmin = $user->hasAnyRole([
                            RolesDef::OFFICE_MANAGER,
                            RolesDef::MASTER_BROKER
                        ]);
                        $userCanEditCorporateLandlords = $user->hasPermissionTo(PermissionsDef::CAN_EDIT_CORPORATE_LANDLORDS);
                        return $userIsAdmin || $userCanEditCorporateLandlords;
                    })
                ],
                'landlordData-contract' => 'file',
                'landlordData-qatar_id_no_pdf' => 'file',
                'landlordData-contact_person' => '',
                'landlordData-developer_name' => '',
                // 'verified' => '',
                'landlordData-contract-remove' => 'nullable',
                'landlordData-qatar_id_no_pdf-remove' => 'nullable',
                'towers' => '',
                'landlordData-task_id' => '',
                'landlordData-marketing_agreement' => 'file',
                'landlordData-marketing_agreement-remove' => '',
                'landlordData-availability_lists' => 'array',
                'landlordData-availability_list_towers' => 'array',
                'landlordData-availability_lists-remove' => 'array',
                'landlordData-forms' => 'array',
                'landlordData-forms-remove' => 'array',
                'landlordData-payment_booking_fee' => 'required',
                'landlordData-payment_cheque_addressed_to' => 'required',
                'landlordData-payment_closing_requiring_documents' => 'required',
                'landlordData-payment_security_deposit_dated' => 'required',
                'landlordData-booking_form' => 'file|required',
                'landlordData-booking_form-remove' => 'nullable',
                'landlordData-unit_tower_id' => 'required|array',
                'landlordData-unit_tower_id.*' => 'required',
                'landlordData-unit_no.*' => 'required',
                'landlordData-unit_tower_details' => 'array',
                'landlordData-is_for_rent' => 'array',
                'landlordData-is_for_sale' => 'array',
                'landlordData-rent_agent_id' => 'array',
                'landlordData-sale_agent_id' => 'array',
                // 'source_contact_id' => ''
            ];
        } else {
            return [
                // 'qatar_id_no' => [
                //     function ($attribute, $value, $fail) use ($itemId) {
                //         if (request()->get('landlordData-is_corporate_landlord', "0") == "0") {
                //             if (empty(trim($value))) {
                //                 $fail('This field is mandatry');
                //             } else {
                //                 $contactsExist = Contact::where('qatar_id_no', $value)
                //                     ->first();
                //                 if (!is_null($contactsExist) && $itemId != $contactsExist->id) {
                //                     $fail('This Qatar ID No is already registered in the system.');
                //                 }
                //             }
                //         }
                //     },
                // ],
                'qatar_id_no' => '',
                'name' => 'required',
                'email_1' => [
                    'required',
                    'email',
                    new BrokerLandlordUniqueContactEmail($itemId)
                ],
                'prefix_mobile_1' => '',
                'mobile_1' => [
                    'nullable',
                    'max:40',
                    'regex:/^[0-9]*$/',
                    new BrokerLandlordUniqueContactPhoneNumber($itemId)
                ],
                'prefix_mobile_2' => '',
                'mobile_2' => [
                    'nullable',
                    'max:40',
                    'regex:/^[0-9]*$/',
                    new BrokerLandlordUniqueContactPhoneNumber($itemId)
                ],
                'unit_number' => '',
                'date_of_birth' => '',
                'last_contact_date' => '',
                'contract_end_date' => '',
                'operation-history' => '',
                'notes' => '',
                'nationality_id' => 'nullable',
                'landlordData-is_corporate_landlord' => [
                    Rule::requiredIf(function () {
                        $user = auth()->user();
                        $userIsAdmin = $user->hasAnyRole([
                            RolesDef::OFFICE_MANAGER,
                            RolesDef::MASTER_BROKER
                        ]);
                        $userCanEditCorporateLandlords = $user->hasPermissionTo(PermissionsDef::CAN_EDIT_CORPORATE_LANDLORDS);
                        return $userIsAdmin || $userCanEditCorporateLandlords;
                    })
                ],
                'landlordData-contract' => 'file',
                'landlordData-qatar_id_no_pdf' => 'file',
                'landlordData-contact_person' => '',
                'landlordData-developer_name' => '',
                // 'verified' => '',
                'landlordData-contract-remove' => 'nullable',
                'landlordData-qatar_id_no_pdf-remove' => 'nullable',
                'towers' => '',
                'landlordData-task_id' => '',
                'landlordData-marketing_agreement' => 'file',
                'landlordData-marketing_agreement-remove' => '',
                'landlordData-availability_lists' => 'array',
                'landlordData-availability_list_towers' => 'array',
                'landlordData-availability_lists-remove' => 'array',
                'landlordData-forms' => '',
                'landlordData-forms-remove' => '',
                'landlordData-payment_booking_fee' => 'required',
                'landlordData-payment_cheque_addressed_to' => 'required',
                'landlordData-payment_closing_requiring_documents' => 'required',
                'landlordData-payment_security_deposit_dated' => 'required',
                'landlordData-booking_form' => [
                    Rule::requiredIf(function ()  use ($itemId) {
                        $landlord = Contact::with('landlordData')->where('id', $itemId)->first();
                        if (!is_null($landlord->landlordData) && !is_null($landlord->landlordData->booking_form_path)) {
                            return false;
                        } else if (request()->get('landlordData-booking_form') == false) {
                            return true;
                        }
                    })
                ],
                'landlordData-booking_form-remove' => 'nullable',
                'landlordData-contact_x_tower_id' => 'array',
                'landlordData-unit_tower_id' => 'required|array',
                'landlordData-unit_tower_id.*' => 'required',
                'landlordData-unit_no.*' => 'required',
                'landlordData-unit_tower_details' => 'array',
                'landlordData-is_for_rent' => 'array',
                'landlordData-is_for_sale' => 'array',
                'landlordData-rent_agent_id' => 'array',
                'landlordData-sale_agent_id' => 'array',
            ];
        }
    }

    public function vSearch()
    {
        return $this->brokerLandlordsService->searchItemsForCurrentUser(request());
    }

    public function connect($landlordSlug)
    {
        $landlordParts = explode("-", $landlordSlug);
        if (count($landlordParts) == 2) {
            $existingLandlord = $this->brokerLandlordsService->getLandlordByIdAndQatarIdNo($landlordParts[1], $landlordParts[0]);
            if (!is_null($existingLandlord)) {
                $added = $this->brokerLandlordsService->connectUserWithLandlord(auth()->user(), $existingLandlord);
                $messageType = 'info';
                $message = 'The landlord is already in your list';
                if ($added) {
                    $messageType = 'success';
                    $message = 'The landlord has been assigned to your profile';
                }

                return redirect('crm/broker-landlords')->with($messageType, $message);
            }
        }

        return response('Invalid data', 419);
    }

    protected function ajaxRequestIndex()
    {
        $userIsAdmin = auth()->user()->hasAnyRole([
            RolesDef::OFFICE_MANAGER,
            RolesDef::MASTER_BROKER
        ]);
        $userHasEditPermission = auth()->user()->hasPermissionTo(PermissionsDef::CAN_EDIT_CORPORATE_LANDLORDS);
        $userIsTeamLeader = auth()->user()->hasRole(RolesDef::TEAM_LEADER);
        $userId = auth()->user()->id;
        $items = $this->brokerLandlordsService->getTableItems(request());

        $items->each(
            function ($item) use ($userIsAdmin, $userId, $userIsTeamLeader, $userHasEditPermission) {
                $towerDataArr = explode(",<br />", $item->towers);
                $towerMarkup = "";
                foreach ($towerDataArr as $index => $rowData) {
                    $currData = explode("||", $rowData);
                    if (count($currData) > 1) {
                        $towerMarkup .= "<div data-bs-toggle='tooltip' data-bs-placement='top' class='pe-1' title='" . $currData[1] . "'>" . $currData[0] . ($index < count($towerDataArr) - 1 ? ", " : "") . "</div>";
                    }
                }
                $item->name = $item->fullname;
                $item->towers = "<div class='landlord-towers'>" . $towerMarkup . "</div>";
                $item->can_edit = $userIsAdmin || $item->created_by == $userId || $userHasEditPermission;
                $item->can_delete = $userIsAdmin && in_array(auth()->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>']);
                $item->can_assign = $userIsAdmin;
                $item->is_verified_landlord = !!$item->marketing_agreement_path && !$item->is_contract_expired;
                $item->actions = '';

                if (!$userIsAdmin && !$userIsTeamLeader && $item->is_corporate_landlord && !$userHasEditPermission) {
                    $item->mobile = "******";
                    $item->mobile_1 = "******";
                    $item->mobile_2 = "******";
                    $item->email_1 = "******";
                    $item->email_2 = "******";
                    $item->email = "******";
                }
            }
        );

        $count = $this->brokerLandlordsService->getTableItemsCount(request());

        return [
            'data' => $items,
            'recordsTotal' => $count,
            'recordsFiltered' => $count
        ];
    }

    protected function getNewItem()
    {
        $landlord = new Contact();
        $landlord->created_by = auth()->user()->id;
        $landlord->contact_type = Contact::LANDLORD;
        $landlord->record_no = $this->brokerLandlordsService->getNextLandlordNoForUser();

        return $landlord;
    }

    protected function getDbItem($contactId)
    {
        $contact = Contact::with(['owner'])->find($contactId);
        if (!is_null($contact)) {
            if (empty($contact->record_no)) {
                $contact->record_no = $this->brokerLandlordsService->getNextLandlordNoForUser();
            }
            if (empty($contact->contact_type)) {
                $contact->contact_type = Contact::LANDLORD;
            }
        }
        return $contact;
    }

    protected function getLandlordData($contactData)
    {
        $landlordData = null;
        if ($contactData->exists) {
            if (!is_null($contactData->landlordData)) {
                $landlordData = $contactData->landlordData;
            } else {
                $landlordData = ContactLandlordData::firstWhere('contact_id', $contactData->id);
            }
        }

        if (is_null($landlordData)) {
            $landlordData = new ContactLandlordData();
            $landlordData->contact_id = $contactData->id;
            $landlordData->created_by = auth()->user()->id;
        }

        return $landlordData;
    }

    protected function updateObjectFieldsFromRequest($landlord, $validFields)
    {
        foreach ($validFields as $requestFieldName => $validValue) {
            if (in_array($requestFieldName, [
                'landlordData-is_corporate_landlord',
                'landlordData-developer_name',
                'landlordData-forms',
                'landlordData-forms-remove',
                'landlordData-contract',
                'landlordData-contract-remove',
                'landlordData-qatar_id_no_pdf',
                'landlordData-contact_person',
                'landlordData-qatar_id_no_pdf-remove',
                'landlordData-task_id',
                'towers',
                'landlordData-marketing_agreement',
                'landlordData-marketing_agreement-remove',
                'landlordData-availability_lists',
                'landlordData-availability_lists-remove',
                'landlordData-availability_list_towers',
                'operation-history',
                'landlordData-booking_form',
                'landlordData-booking_form-remove',
                'landlordData-payment_booking_fee',
                'landlordData-payment_cheque_addressed_to',
                'landlordData-payment_closing_requiring_documents',
                'landlordData-payment_security_deposit_dated',
                'landlordData-unit_tower_id',
                'landlordData-unit_no',
                'landlordData-unit_tower_details',
                'landlordData-is_for_rent',
                'landlordData-is_for_sale',
                'landlordData-rent_agent_id',
                'landlordData-sale_agent_id',
                'landlordData-contact_x_tower_id'
            ])) {
                continue;
            }
            $landlord->$requestFieldName = $validValue;
        }

        // if (!isset($validFields['verified'])) {
        //     $landlord->verified = 0;
        // }
        $landlord->contact_type = Contact::LANDLORD;
    }

    protected function updateLandlordDataFieldsFromRequest($landlordData, $validFields)
    {
        foreach ($validFields as $requestFieldName => $validValue) {
            if (in_array($requestFieldName, ['landlordData-is_corporate_landlord', 'landlordData-developer_name', 'landlordData-contact_person', 'landlordData-forms', 'landlordData-forms-remove', 'landlordData-contract', 'landlordData-contract-remove', 'landlordData-qatar_id_no_pdf', 'landlordData-qatar_id_no_pdf-remove', 'towers', 'landlordData-marketing_agreement', 'landlordData-marketing_agreement-remove', 'landlordData-availability_lists', 'landlordData-availability_lists-remove', 'landlordData-availability_list_towers', 'landlordData-booking_form', 'landlordData-booking_form-remove', 'landlordData-payment_booking_fee', 'landlordData-payment_cheque_addressed_to', 'landlordData-payment_closing_requiring_documents', 'landlordData-payment_security_deposit_dated'])) {
                if (!in_array($requestFieldName, ['towers', 'landlordData-contract', 'landlordData-contract-remove', 'landlordData-qatar_id_no_pdf', 'landlordData-qatar_id_no_pdf-remove', 'landlordData-marketing_agreement', 'landlordData-marketing_agreement-remove', 'landlordData-availability_lists', 'landlordData-availability_list_towers', 'landlordData-availability_lists-remove', 'landlordData-forms', 'landlordData-forms-remove', 'landlordData-booking_form', 'landlordData-booking_form-remove'])) {
                    $landlordData->{str_replace("landlordData-", "", $requestFieldName)} = $validValue;
                }
            }
        }
    }

    protected function viewVars(string $viewName, $assetId = null)
    {
        $countries = $this->countriesService->getCountries();
        if ($viewName == 'edit' || $viewName == 'create') {
            return $this->getViewVars($assetId);
        }

        $listLandlordType = 'allLandlords';
        if (request()->has('lt')) {
            if (request()->get('lt') === 'corporate') {
                $listLandlordType = 'corporate';
            } elseif (request()->get('lt') === 'private') {
                $listLandlordType = 'private';
            } elseif (request()->get('lt') === 'team_list') {
                $listLandlordType = 'team_list';
            }
        }
        $userIsTeamLeader = auth()->user()->hasAnyRole([
            RolesDef::TEAM_LEADER,
        ]);
        $usersWithRoles = $this->userService->getUsersWithRoles();

        return compact([
            'listLandlordType',
            'countries',
            'userIsTeamLeader',
            'usersWithRoles'
        ]);
    }

    private function getViewVars($contactId = null)
    {
        $empty = false;
        $delete = false;

        $contact = is_null($contactId) ? $this->getNewItem() : $this->getDbItem($contactId);
        $landlordData = $this->getLandlordData($contact);
        $countries = $this->countriesService->getCountries();

        $formActionQpArgs = [];
        if (request()->has('taskId')) {
            $formActionQpArgs['taskId'] = request()->get('taskId');
        }
        $formAction = is_null($contactId) ? route('broker-landlords.create.post', $formActionQpArgs) : route('broker-landlords.update', array_merge(['id' => $contactId], $formActionQpArgs));
        $currentUserIsOwner = $contact->created_by == auth()->user()->id;
        $authorIsInCurrentUsersTeam = false;
        if (!is_null($contactId) && !is_null($contact->owner) && $contact->owner->team_leader_id == auth()->user()->id) {
            $authorIsInCurrentUsersTeam = true;
        }

        $userIsAdmin = auth()->user()->hasAnyRole([
            RolesDef::OFFICE_MANAGER,
            RolesDef::MASTER_BROKER,
            RolesDef::MATRIX_AGENT_MANAGER
        ]);
        $userIsTeamLeader = auth()->user()->hasAnyRole([
            RolesDef::TEAM_LEADER,
        ]);
        $userHasEditPermission = auth()->user()->hasPermissionTo(PermissionsDef::CAN_EDIT_CORPORATE_LANDLORDS);
        if ($landlordData->is_corporate_landlord && !$userIsAdmin && !$userIsTeamLeader && !$userHasEditPermission) {
            $contact->mobile_1 = "******";
            $contact->mobile_2 = "******";
            $contact->phone_1 = "******";
            $contact->phone_2 = "******";
            $contact->email_1 = "******";
            $contact->email_2 = "******";
            // dd($contact);
        }
        $nationalities = Nationality::all();
        $agents = $this->userService->getPossibleListingOwners();
        $userIsConnectedToContact = false;
        $contact->load(['connectionUsers', 'connectionUsers.user']);
        foreach ($contact->connectionUsers as $cu) {
            if ($cu->user->email === auth()->user()->email) {
                $userIsConnectedToContact = true;
                break;
            }
        }
        $createFromTask = null;
        $userHasTemporaryAccess = false;
        if (request()->has('taskId')) {
            $createFromTask = Task::firstWhere('id', request()->get('taskId'));
        }
        if (($createFromTask && $createFromTask->automatization->status == 'ACTIVE' && $createFromTask->assigned_to == auth()->user()->id) || ($landlordData && $landlordData->created_by == auth()->user()->id)) {
            $userIsConnectedToContact = true;
            $userHasTemporaryAccess = true;
        }

        $accessReadonly = !$currentUserIsOwner && !$userIsAdmin && !$authorIsInCurrentUsersTeam && !$userIsConnectedToContact && !$userHasEditPermission;

        $is_corporateOptions = $this->brokerLandlordsService->landlordCategory;
        if ($createFromTask) {
            $filtered = array_filter($is_corporateOptions, function ($option) {
                return $option['label'] === 'Please select' || $option['label'] === 'Private';
            });
            $is_corporateOptions = array_values($filtered);
        }

        $datedUndatedOpts = array_map(function ($item) {
            return (object)$item;
        }, [
            ['value' => '', 'label' => 'Please select'],
            ['value' => '1', 'label' => 'Dated'],
            ['value' => '0', 'label' => 'Undated'],
        ]);

        $formSections = [
            'broker-landlord' => 'Landlord',
        ];
        if ($userIsAdmin) {
            $formSections['contract'] = 'Contract';
            $formSections['closing'] = 'Closing';
            $formSections['documents'] = 'Documents';
        }
        if ($contact->exists) {
            $formSections['listings'] = 'Broker Listings';
        }
        $formSections['operationHistory'] = 'Notes';

        $towersList = $this->brokerLandlordsService->getTowers()->map(function ($a) {
            $label = (!empty($a->geography) ? $a->geography->name . ' - ' : '') . $a->name;
            return [
                'value' => $a->id,
                'label' => $label
            ];
        });

        if (!is_null($contact)) {
            $operationHistory = $contact->operationHistory;
        }

        if (empty(old('landlordData-unit_tower_id')) && !empty($landlordData)) {
            $selectedTowers = $landlordData->towers->map(function ($t) {
                return [$t->pivot->tower_id, $t->pivot->unit_no, $t->pivot->unit_type, $t->pivot->is_for_rent, $t->pivot->is_for_sale, $t->pivot->rent_agent_id, $t->pivot->sale_agent_id, $t->pivot->id];
            });
        } else {
            $selectedTowers = [];
            $unitNo = old('landlordData-unit_no') ?? [];
            $unitTowerDetails = old('landlordData-unit_tower_details') ?? [];
            $isForRent = old('landlordData-is_for_rent') ?? [];
            $isForSale = old('landlordData-is_for_sale') ?? [];
            $rentAgent = old('landlordData-rent_agent_id') ?? [];
            $saleAgent = old('landlordData-sale_agent_id') ?? [];
            $theId = old('landlordData-contact_x_tower_id') ?? [];

            foreach (old('landlordData-unit_tower_id') ?? [] as $index => $towerId) {
                $unitNoValue = $unitNo[$index] ?? null;
                $unitTowerDetailsValue = $unitTowerDetails[$index] ?? null;
                $isForRentValue = $isForRent[$index] ?? null;
                $isForSaleValue = $isForSale[$index] ?? null;
                $rentAgentValue = $rentAgent[$index] ?? null;
                $saleAgentValue = $saleAgent[$index] ?? null;

                $selectedTowers[] = [
                    $towerId,
                    $unitNoValue,
                    $unitTowerDetailsValue,
                    $isForRentValue,
                    $isForSaleValue,
                    $rentAgentValue,
                    $saleAgentValue,
                    $theId
                ];
            }
        }
        $selectedTowersString = json_encode($selectedTowers);

        return compact([
            'empty',
            'delete',
            'accessReadonly',
            'contact',
            'landlordData',
            'formAction',
            'nationalities',
            'agents',
            'currentUserIsOwner',
            'userIsAdmin',
            'userHasEditPermission',
            'userIsTeamLeader',
            'towersList',
            'is_corporateOptions',
            'formSections',
            'operationHistory',
            'countries',
            'datedUndatedOpts',
            'createFromTask',
            'selectedTowersString',
            'userHasTemporaryAccess'
        ]);
    }

    protected function executePostCreate($item, $validFields)
    {
        $landlordData = $this->getLandlordData($item);

        $this->updateLandlordDataFieldsFromRequest($landlordData, $validFields);
        $landlordData->save();
        $this->operationHistoryService->addOperationHistory($item, "Landlord entity has been created", auth()->user());
        $this->handleAssignments($landlordData, $validFields);
        $this->handleTowers($landlordData, $validFields);
        $this->handleOperationHistory($item, $validFields);
    }

    protected function executePreCreate($item, $validFields)
    {
        $this->handleLandlordExistsEmail($item, $validFields);
    }

    protected function executePostUpdate($item, $validFields)
    {
        $landlordData = $this->getLandlordData($item);
        $this->updateLandlordDataFieldsFromRequest($landlordData, $validFields);
        if (request()->has('taskId')) {
            $createFromTask = Task::firstWhere('id', request()->get('taskId'));
            if ($createFromTask && $createFromTask->automatization->status == 'ACTIVE' && $createFromTask->assigned_to == auth()->user()->id) {
                $landlordData->is_corporate_landlord = 0;
                $landlordData->task_id = request()->get('taskId');
                $this->operationHistoryService->addOperationHistory($item, "This Landlord has been created by ".auth()->user()->name." from task #".request()->get('taskId'), auth()->user());
                Log::info("Landlord # - ".$landlordData->id." has been created by ".auth()->user()->name." from task #".request()->get('taskId'));
            }
        }
        $landlordData->save();
        $this->operationHistoryService->addOperationHistory($item, "Landlord entity has been updated", auth()->user());
        $this->handleAssignments($landlordData, $validFields);
        $this->handleTowers($landlordData, $validFields);
        $this->handleOperationHistory($item, $validFields);
    }

    private function handleTowers($landlordData, $validFields)
    {
        $existingTowers = $landlordData->towers;
        $aliveIds = [];
        $newTowers = [];
        $rentAgentsToNotify = [];
        $saleAgentsToNotify = [];
        foreach ($validFields['landlordData-unit_tower_id'] ?? [] as $index => $towerId) {
            $currentId = isset($validFields['landlordData-contact_x_tower_id']) ? $validFields['landlordData-contact_x_tower_id'][$index] : "";
            $recordData = [
                'tower_id' => $towerId,
                'unit_no' => $validFields['landlordData-unit_no'][$index],
                'unit_type' => $validFields['landlordData-unit_tower_details'][$index],
                'is_for_rent' => isset($validFields['landlordData-is_for_rent'][$index]) ? $validFields['landlordData-is_for_rent'][$index] : '0',
                'is_for_sale' => isset($validFields['landlordData-is_for_sale'][$index]) ? $validFields['landlordData-is_for_sale'][$index] : '0',
                'rent_agent_id' => isset($validFields['landlordData-rent_agent_id'][$index]) ? $validFields['landlordData-rent_agent_id'][$index] : null,
                'sale_agent_id' => isset($validFields['landlordData-sale_agent_id'][$index]) ? $validFields['landlordData-sale_agent_id'][$index] : null,
                'rent_email_sent_at' => null,
                'sale_email_sent_at' => null,
            ];
            if (!empty($currentId)) {
                $existingTower = null;
                foreach ($existingTowers as $et) {
                    if ($et->pivot->id == $currentId) {
                        $existingTower = $et;
                    }
                }
                if (!empty($existingTower)) {
                    if (!empty($recordData['rent_agent_id']) && $existingTower->pivot->rent_agent_id != $recordData['rent_agent_id']) {
                        $user = User::find($recordData['rent_agent_id']);
                        $rentAgentsToNotify[] = $user;
                    }
                    if (!empty($recordData['sale_agent_id']) && $existingTower->pivot->sale_agent_id != $recordData['sale_agent_id']) {
                        $user = User::find($recordData['sale_agent_id']);
                        $saleAgentsToNotify[] = $user;
                    }
                    $existingTower->pivot->update($recordData);
                    $aliveIds[] = $currentId;
                }
            } else {
                if (!empty($recordData['rent_agent_id'])) {
                    $user = User::find($recordData['rent_agent_id']);
                    $rentAgentsToNotify[] = $user;
                }
                if (!empty($recordData['sale_agent_id'])) {
                    $user = User::find($recordData['sale_agent_id']);
                    $saleAgentsToNotify[] = $user;
                }
                $newTowers[] = $recordData;
            }
        }
        DB::table('contact_x_towers')->where('contact_id', $landlordData->id)->whereNotIn('id', $aliveIds)->delete();
        $landlordData->towers()->attach($newTowers);
        foreach ($rentAgentsToNotify as $agent) {
            $this->emailService->sendEmailToAgentsForLandlords($agent, $landlordData, $validFields);
        }
        foreach ($saleAgentsToNotify as $agent) {
            $this->emailService->sendEmailToAgentsForLandlords($agent, $landlordData, $validFields);
        }
    }

    private function handleAssignments($landlordData, $validFields)
    {
        $contactContractFile = $validFields['landlordData-contract'] ?? null;
        $deleteOldFile = isset($validFields['landlordData-contract-remove']);
        $this->attachmentsService->syncContactContract($landlordData, $contactContractFile, $deleteOldFile);

        if ($contactContractFile) {
            $destinationEmail = $validFields['email_1'];
            $this->emailService->saveLandlordContract($destinationEmail, [
                'attachment_path' => Storage::path($landlordData->contract_path),
                'attachment_title' => $landlordData->contract_title,
            ]);
        }

        $contactQatarIdNoPdfFile = $validFields['landlordData-qatar_id_no_pdf'] ?? null;
        $deleteOldQatarIdNoPdfFile = isset($validFields['landlordData-qatar_id_no_pdf-remove']);
        $this->attachmentsService->syncContactQatarIdNoPdf($landlordData, $contactQatarIdNoPdfFile, $deleteOldQatarIdNoPdfFile);

        $contactBookingFormPdfFile = $validFields['landlordData-booking_form'] ?? null;
        $deleteOldBookingFormPdfFile = isset($validFields['landlordData-booking_form-remove']);
        $this->attachmentsService->syncContactBookingPdf($landlordData, $contactBookingFormPdfFile, $deleteOldBookingFormPdfFile);

        $user = auth()->user();
        $userIsAdmin = $user->hasAnyRole([RolesDef::MASTER_BROKER, RolesDef::OFFICE_MANAGER]);
        $userCanEditCorporateLandlords = $user->hasPermissionTo(PermissionsDef::CAN_EDIT_CORPORATE_LANDLORDS);
        if ($userIsAdmin || $userCanEditCorporateLandlords) {
            $marketingAgreementFile = $validFields['landlordData-marketing_agreement'] ?? null;
            $deleteOldFile = isset($validFields['landlordData-marketing_agreement-remove']);
            $this->attachmentsService->syncMarketingAgreement($landlordData, $marketingAgreementFile, $deleteOldFile);

            $this->handleFormsFilesUploads($landlordData, $validFields);
            $this->handleAvailabilityListsFilesUploads($landlordData, $validFields);
        }
    }

    public function downloadBookingForm($contactId)
    {
        $landlordData = ContactLandlordData::firstWhere('contact_id', $contactId);
        if ($landlordData->booking_form_path) {
            return Storage::download($landlordData->booking_form_path, $landlordData->booking_form_title);
        }
        return response();
    }

    public function downloadContactContract($contactId)
    {
        $landlordData = ContactLandlordData::firstWhere('contact_id', $contactId);
        if ($landlordData->contract_path) {
            return Storage::download($landlordData->contract_path, $landlordData->brochure_title);
        }
        return response();
    }

    public function downloadContactQatarIdNoPdf($contactId)
    {
        $landlordData = ContactLandlordData::firstWhere('contact_id', $contactId);
        if ($landlordData->qatar_id_no_pdf_path) {
            return Storage::download($landlordData->qatar_id_no_pdf_path, $landlordData->qatar_id_no_pdf_title);
        }
        return response();
    }

    public function downloadMarketingAgreement($contactId)
    {
        $landlordData = ContactLandlordData::firstWhere('contact_id', $contactId);
        if ($landlordData->marketing_agreement_path) {
            return Storage::download($landlordData->marketing_agreement_path, $landlordData->marketing_agreement_title);
        }
        return response();
    }

    public function downloadAvailabilityList($contactId)
    {
        if (request()->has('availabilityFilePath')) {
            $availabilityFilePath = request()->get('availabilityFilePath');
            $landlordData = ContactLandlordData::firstWhere('contact_id', $contactId);
            $availability = $landlordData->availability_lists ?? [];
            $filteredFiles = array_filter($availability, function ($availabilityFile) use ($availabilityFilePath) {
                return $availabilityFile->path == $availabilityFilePath;
            });
            if (count($filteredFiles) > 0) {
                $firstArrayPos = array_values($filteredFiles)[0];
                return Storage::download($firstArrayPos->path, $firstArrayPos->title);
            }
        }

        return response();
    }

    public function downloadForm($contactId)
    {
        if (request()->has('filePath')) {
            $filePath = request()->get('filePath');
            $contact = Contact::findOrFail($contactId);
            // $contactLandlordData = ContactLandlordData::find($contactId);
            $contactLandlordData = $contact->landlordData;
            $forms = $contactLandlordData->forms ?? [];
            $filteredFiles = array_filter($forms, function ($formFile) use ($filePath) {
                return $formFile->path == $filePath;
            });

            if (count($filteredFiles) > 0) {
                $firstArrayPos = array_values($filteredFiles)[0];
                return Storage::download($firstArrayPos->path, $firstArrayPos->title);
            }
        }

        return response();
    }

    public function exportNA()
    {
        return Excel::download(new ContactsNAExport, 'contacts_na.xlsx');
    }

    private function handleLandlordExistsEmail($item, $validFields)
    {
        $searchForExistingLL = false;
        if (!empty($validFields['qatar_id_no']) || !empty($validFields['email_1'])) {
            $searchForExistingLL = true;
        }

        if (!$searchForExistingLL) {
            return;
        }

        $landlordQB = Contact::whereNotNull('record_no')->whereNull('deleted_at');
        if (!empty($validFields['qatar_id_no'])) {
            $landlordQB->where('qatar_id_no', $validFields['qatar_id_no']);
        }
        if (!empty($validFields['email_1'])) {
            $landlordQB->where(function ($sql) use ($validFields) {
                $sql->where('email_1', $validFields['email_1'])->orWhere('email_2', $validFields['email_1']);
            });
        }

        $landlord = $landlordQB->first();

        if ($landlord) {
            Log::info("Existing LL found: " . $landlord->record_no);

            $mailPayload = [
                'name' => isset($validFields['first_name']) ? $validFields['first_name'] : $validFields['name'],
                'email' => isset($validFields['email_1']) ? $validFields['email_1'] : $validFields['email'],
                'phone' => isset($validFields['mobile_1']) ? $validFields['mobile_1'] : $validFields['phone']
            ];

            $user = auth()->user();
            $destinationEmail = env('EMAIL_OFFICE');
            $this->emailService->ifExistLandlordEmail($landlord, $mailPayload, $user, $destinationEmail);
        }
    }

    private function handleFormsFilesUploads($landlordData, $validFields)
    {
        $formIndexesToRemove = $validFields['landlordData-forms-remove'] ?? [];
        $itemForms = $landlordData->forms ?? [];
        $cleanupForms = [];
        if (count($formIndexesToRemove)) {
            foreach ($itemForms as $index => $itemForm) {
                $deleted = false;
                if (in_array($index, $formIndexesToRemove)) {
                    $deleted = $this->attachmentsService->removeContactForm($itemForm->path);
                }
                if (!$deleted) {
                    $cleanupForms[] = $itemForm;
                }
            }
        } else {
            $cleanupForms = $itemForms;
        }
        $forms = $validFields['landlordData-forms'] ?? null;
        $uploadedForms = [];
        if (!is_null($forms)) {
            $uploadedForms = $this->attachmentsService->uploadLandlordFormFiles($landlordData, $forms);
        }

        $allForms = array_merge($cleanupForms, $uploadedForms);
        $landlordData->forms = $allForms;
        $landlordData->save();
    }

    private function handleAvailabilityListsFilesUploads($landlordData, $validFields)
    {
        $availabilityListsIndexesToRemove = $validFields['landlordData-availability_lists-remove'] ?? [];
        $itemAvailabilityLists = $landlordData->availability_lists ?? [];
        $cleanupAvailabilityLists = [];
        if (count($availabilityListsIndexesToRemove)) {
            foreach ($itemAvailabilityLists as $index => $itemAvailabilityList) {
                $deleted = false;
                if (in_array($index, $availabilityListsIndexesToRemove)) {
                    $deleted = $this->attachmentsService->removeContactAvailabilityLists($itemAvailabilityList->path);
                }
                if (!$deleted) {
                    $cleanupAvailabilityLists[] = $itemAvailabilityList;
                }
            }
        } else {
            $cleanupAvailabilityLists = $itemAvailabilityLists;
        }
        $availabilityLists = $validFields['landlordData-availability_lists'] ?? null;
        $availabilityListTowers = $validFields['landlordData-availability_list_towers'] ?? [];
        $uploadedAvailabilityLists = [];
        if (!is_null($availabilityLists)) {
            $uploadedAvailabilityLists = $this->attachmentsService->uploadContactAvaiabilityListsFiles($landlordData, $availabilityLists, $availabilityListTowers);
        }

        $allAvailabilityLists = array_merge($cleanupAvailabilityLists, $uploadedAvailabilityLists);
        $landlordData->availability_lists = $allAvailabilityLists;
        $landlordData->save();
    }

    private function handleOperationHistory($item, $validFields)
    {
        if (!empty($validFields['operation-history'])) {
            $this->operationHistoryService->addOperationHistory($item, $validFields['operation-history'], auth()->user());

            // if (auth()->user()->id !== $item->created_by) {
            //     $this->emailService->sendEmailOnAddingOperationHistory($item, auth()->user(), $validFields['operation-history']);
            // }
        }
    }

    public function brokerLandlordOperationHistory($id)
    {
        $brokerLandlord = Contact::with(['operationHistory' => fn($qb) => $qb->orderBy('id', 'DESC'), 'operationHistory.author'])->where('id', '=', $id)->first();
        if (!is_null($brokerLandlord)) {
            return $brokerLandlord->operationHistory->map(function ($row) {
                $createdBy = "Unknown user";
                if (!is_null($row->author)) {
                    $createdBy = $row->author->name;
                }
                return [
                    'created_by' => $createdBy,
                    'created_at' => $row->created_at->format('Y-m-d H:i:s'),
                    'content' => $row->content
                ];
            });
        } else {
            return [];
        }
    }

    protected function destroy($id)
    {
        $user = auth()->user();
        if (!$user->hasRole(RolesDef::OFFICE_MANAGER)) {
            abort(403);
        }
        $contact = Contact::with(['landlordData'])->where('id', $id)->firstOrFail();
        if (!is_null($contact)) {
            if ($contact->properties->count()) {
                return response([
                    'message' => 'This landlord cannot be deleted because some listings are assigned to it: ' . $contact->properties->map(fn($listing) => $listing->ref_no)->join(','),
                    'error' => true
                ]);
            }
            $leads = Lead::where('contact_id', $id)->get();
            if ($leads->count() > 0) {
                return response([
                    'message' => 'This landlord cannot be deleted because some leads are in relation with it: ' . $leads->map(fn($lead) => $lead->id)->join(','),
                    'error' => true
                ]);
            }
            // deals
            $deals = Deal::where('client_id', $id)->get();
            if ($deals->count() > 0) {
                return response([
                    'message' => 'This landlord cannot be deleted because some deals are in relation with it: ' . $deals->map(fn($deal) => $deal->id)->join(','),
                    'error' => true
                ]);
            }
            try {
                $contact->forceDelete();
                return response(['message' => 'The landlord was successfully deleted', 'error' => false], 200);
            } catch (\Exception $Ex) {
                return response(['message' => 'The landlord cannot be deleted (' . $Ex->getMessage() . ')', 'error' => true], 200);
            }
        } else {
            abort(404);
        }
    }

    public function changeLandlordAgent($id)
    {
        $validData = request()->validate([
            'agent' => 'required',
            'operationHistory' => '',
            'assignListings' => ''
        ]);

        $authUser = auth()->user();

        $contact = Contact::find($id);

        if ($validData['agent'] != $contact->created_by) {
            $contact->created_by = $validData['agent'];
            if ($validData['assignListings'] == '1') {
                $properties = Property::where('contact_id', $id)->get();
                foreach ($properties as $property) {
                    $property->created_by = $validData['agent'];
                    $property->save();
                }
                $propertiesSnapshot = PropertySnapshot::where('contact_id', $id)->get();
                foreach ($propertiesSnapshot as $propertySnapshot) {
                    $propertySnapshot->created_by = $validData['agent'];
                    $propertySnapshot->save();
                }
            }
            $user = User::find($validData['agent']);

            $this->operationHistoryService->addOperationHistory(
                $contact,
                'New agent for this landlord is [' . $user->name . ']',
                $authUser
            );
            if (!empty($validData['operationHistory'])) {
                $this->operationHistoryService->addOperationHistory($contact, $validData['operationHistory'], $authUser);
            }
            $contact->save();
            return response([], 200);
        } else {
            return response([], 500);
        }
    }
}
