<?php

namespace App\Http\Controllers\Crm;

use App\Models\PropertyType;
use Grimzy\LaravelMysqlSpatial\Types\Point;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Http\Controllers\CrmBaseController;
use App\Models\Admin\ExportAssignment;
use App\Models\Admin\ExportPlatform;
use App\Models\API\Tower;
use App\Models\Asset;
use App\Models\AssetDefinition;
use App\Models\Attachment;
use App\Models\AttachmentAssignment;
use App\Models\Attribute;
use App\Models\AttributeDefinition;
use App\Models\CacheKeys;
use App\Models\Contact;
use App\Models\ContactLandlordData;
use App\Models\Crm\ListingPublishingStatus;
use App\Models\Crm\PermissionsDef;
use App\Models\Crm\RolesDef;
use App\Models\Geography;
use App\Models\Nationality;
use App\Models\Property;
use App\Models\PropertyRepresentative;
use App\Models\PropertySnapshot;
use App\Models\PropertyView;
use App\Models\PropertyTranslation;
use App\Models\Country;
use App\Models\Development;
use App\Models\InventoryTag;
use App\Models\PropertyStared;
use App\Models\RouteListingItem;
use App\Models\User;
use App\Services\AmenitiesService;
use App\Services\AttachmentsService;
use App\Services\BrokerLandlordsService;
use App\Services\EmailService;
use App\Services\ExportService;
use App\Services\InventoryService;
use App\Services\MenuHelperService;
use App\Services\PropertiesService;
use App\Services\OperationHistoryService;
use App\Services\PropertyTypesService;
use App\Services\PurifierService;
use App\Services\RepresentativeService;
use App\Services\RolesAndPermissionsService;
use App\Services\UserService;
use App\Services\PropertyFinderService;
use App\Services\PropertyStatsService;
use App\Services\CountriesService;
use Share;

class InventoryControllerCrm extends CrmBaseController
{
    protected $routes = [
        'index' => 'inventory.index',
        'edit' => 'inventory.edit'
    ];

    protected $views = [
        'index' => 'crm.inventory.index',
        'create' => 'crm.inventory.inventory-form',
        'edit' => 'crm.inventory.inventory-form',
    ];

    protected $conditionalFields = [
        'property-field-commission_ll_rent',
        'property-field-commission_ll_sale',
        'property-field-commission_ll_rent_and_sale',
        'property-field-payment_method_rent',
        'property-field-payment_method_sale',
        'property-field-payment_method_rent_and_sale'
    ];

    private $listingStatusOptions = [
        ['value' => '', 'label' => 'Please select'],
        ['value' => 'available', 'label' => 'Available'],
        ['value' => 'occupied', 'label' => 'Occupied', 'onlyFor' => 'sale'],
        ['value' => 'sold', 'label' => 'Sold', 'onlyFor' => 'sale'],
        ['value' => 'to-be-available', 'label' => 'To be available', 'onlyFor' => 'rent'],
        ['value' => 'rented', 'label' => 'Rented', 'onlyFor' => 'rent'],
    ];

    protected $messages = [
        'edit.success' => 'The item was successfully updated',
        'operation-history-added.success' => 'The remarks were successfully added',
        'deleted.success' => 'The listing was marked as delete',
        'request-delete.success' => 'The request for delete was successfully sent to Listing Admin',
    ];

    private $listingFormAttributesMap = [
        'property-features-build-up-area',
        'property-features-construction-year',
        'property-features-bedrooms',
        'property-features-bathrooms',
        'property-features-furnishings',
        'property-features-furnishings-office',
        'property-features-kitchen',
        'property-features-pantry',
        'property-features-balcony',
        'property-features-parking-info',
        'property-features-service_charge',
        'property-features-transfer_fee'
    ];

    protected $validationMessages = [
        'landlord-fullname.required' => 'This field is required',
        'landlord-email.required' => 'This field is required',
        'landlord-email.email' => 'This field should be a valid email address',
        'landlord-mobile_no.required' => 'This field is required',
        'representative-fullname.required' => 'This field is required',
        'representative-email.email' => 'This field should be a valid email address',
        'representative-mobile_no.required' => 'This field is required',
        'representative-mobile_no.regex' => 'The format of the field is invalid',
        'representative-mobile_no.max' => 'The phone number should contain at least 4 chars and max 40 characters',
        'representative-mobile_no.min' => 'The phone number should contain at least 4 chars and max 40 characters',
        'property-field-title.required' => 'This field is required.',
        'property-field-title.min' => 'The title should contain at least 30 chars.',
        'property-field-title.max' => 'The title should contain max 50 chars.',
        'property-field-propertyfinder_title.required' => 'This field is required.',
        'property-field-propertyfinder_title.min' => 'The title should contain at least 30 chars.',
        'property-field-propertyfinder_title.max' => 'The title should contain max 50 chars.',
        'property-field-property_type_id.required' => 'Please select the property type',
        'property-field-ad_type.required' => 'Please select the operation type',
        'property-field-location_id.required' => 'Please select the location',
        'property-field-contact_id.required' => 'Please select the landlord',
        'property-brochure.invalid' => 'Invalid file type for Brochure',
        'property-layout.invalid' => 'Invalid file type for Layout',
        'property-info.invalid' => 'Invalid file type for More info',
        'property-booking.invalid' => 'Invalid file type for Booking form',
        'property-features-build-up-area.required' => 'This field is required.',
        'property-features-bedrooms.required' => 'Please select the bedrooms.',
        'property-features-furnishings.required' => 'This field is required.',
        'property-features-furnishings-office.required' => 'This field is required.',
        'property-features-furnishings-retail.required' => 'This field is required.',
        'property-features-balcony.required' => 'Please select the balcony.',
        'property-features-kitchen.required' => 'This field is required.',
        'property-features-pantry.required' => 'This field is required.',
        'property-features-bathrooms.required' => 'Please select the bathrooms.',
        'property-features-service_charge.required' => 'This field is required.',
        'property-features-transfer_fee.required' => 'This field is required.',
        'property-features-parking-info.required' => 'This field is required.',
        'property-features-description.required' => 'This field is required.',
        'property-features-construction-year.numeric' => 'The value should be a number',
        'property-features-construction-year.min' => 'The value should be greater than 1900',
        'property-field-status.required' => 'Please select the status.',
        'property-field-unit_no.required' => 'This field is required.',
        'property-field-keys_place.required' => 'This field is required.',
        'property-field-price.required' => 'This field is required.',
        'property-field-best_price.required' => 'This field is required.',
        'property-field-offers.required' => 'This field is required.',
        'property-field-prorated_rata.required' => 'This field is required.',
        'property-field-tower_id.required' => 'This field is required.',
        'property-field-payment_method_rent.required' => 'Please select the payment method.',
        'property-field-payment_method_sale.required' => 'Please select the payment method.',
        'property-field-payment_method_rent_and_sale.required' => 'Please select the payment method.',
        'property-field-geo_lat.required' => 'Please select the location on the map.',
        'property-translation-title-ar.required' => 'Title for [AR] language is required',
        'property-translation-title-ar.min' => 'The title should contain at least 30 chars.',
        'property-translation-title-ar.max' => 'The title should contain max 50 chars.',
        'property-translation-propertyfinder_title-ar.required' => 'Title for [AR] language is required',
        'property-translation-propertyfinder_title-ar.min' => 'The title should contain at least 30 chars.',
        'property-translation-propertyfinder_title-ar.max' => 'The title should contain max 50 chars.',
        'property-translation-description-ar.required' => 'Description for [AR] language is required',
    ];

    protected function getFieldsToValidate(string $actionName, $itemId = null)
    {
        $unitNoAndTowerChecker = function ($attribute, $value, $fail) use ($itemId) {
            if (!empty($value) && $value != 'N/A' && request()->get('property-field-location_id') != "" && request()->get('property-field-tower_id') != "") {
                $locationId = request()->get('property-field-location_id');
                $towerId = request()->get('property-field-tower_id');
                $operationType = request()->get('property-field-ad_type');
                $unitNo = $value;
                $qb = Property::where(['location_id' => $locationId, 'tower_id' => $towerId, 'unit_no' => $unitNo, 'ad_type' => $operationType]);
                if (!empty($itemId)) {
                    $qb->where('asset_id', 'NOT LIKE', $itemId);
                }
                $existingListings = $qb->get();
                if ($existingListings->count() > 0) {
                    $refNos = $existingListings->map(function ($item) {
                        return $item->ref_no;
                    })->join(", ");
                    $fail("The unit no / tower already exists in system for other listing(s): $refNos");
                }
            }
        };
        if ($actionName !== 'draft') {
            return [
                'representative-fullname' => '',
                'representative-email' => 'nullable|email',
                'representative-mobile_no' => 'nullable|min:4|max:40|regex:/^[0-9]*$/',
                'representative-prefix_mobile_no' => '',
                'representative-nationality_id' => '',
                'representative-qatar_id_no' => '',

                'property-field-is_exclusive' => '',
                // 'property-field-pictures_taken' => '',
                'property-field-is_homepage_promoted' => '',
                'property-field-offplan' => '',
                'property-field-handover_date' => 'nullable|date',
                'property-field-rent_end_date' => 'nullable|date',
                'property-field-ad_type' => 'required',
                'property-field-project_id' => '',
                'property-field-keys_place' => 'required',
                'property-field-unit_no' => [$unitNoAndTowerChecker],
                'property-field-tour_360' => 'nullable|url',
                'property-field-embeed_youtube' => 'nullable|url',
                'property-field-location_id' => 'required',
                'property-field-parking' => '',

                'property-field-title' => 'required',
                'property-field-propertyfinder_title' =>  [Rule::when(
                    request()->has('export_platforms') ? in_array('4', request()->get('export_platforms')) : "",
                    ['required', 'min:30', 'max:50']
                )],

                'property-field-property_type_id' => 'required',
                'property-field-tower_id' => 'required',
                'property-field-bills' => '',

                'property-field-status' => 'required',
                'property-field-available_when' => '',
                'property-field-title_deed' => '',
                'property-field-reffered_by' => '',
                'operation-history' => '',
                'property-amenities' => '',
                'property-views_ids' => '',
                'property-field-contact_id' => 'required',
                'property-field-is_short_stay' => '',

                'property-features-construction-year' => '',
                'property-features-build-up-area' => 'exclude_if:property-field-property_type_id,20,31,32,33,34|required',
                'property-features-bedrooms' => 'exclude_if:property-field-property_type_id,3,8,9,11,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-furnishings' => 'exclude_if:property-field-property_type_id,3,8,9,11,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-furnishings-office' => 'exclude_if:property-field-property_type_id,1,7,9,14,18,2,15,16,17,8,11,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-furnishings-retail' => 'exclude_if:property-field-property_type_id,1,7,9,14,18,2,15,16,17,3,11,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-balcony' => 'exclude_if:property-field-property_type_id,9,11,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-kitchen' => 'exclude_if:property-field-property_type_id,9,3,8,11,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-pantry' => 'exclude_if:property-field-property_type_id,1,7,9,14,18,2,15,16,17,3,11,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-bathrooms' => 'exclude_if:property-field-property_type_id,3,9,11,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                'property-features-service_charge' => [
                    Rule::requiredIf(function () {
                        $isRent = request()->get('property-field-ad_type') === 'rent';
                        $isOffice = request()->get('property-field-property_type_id') == 3;
                        $isLand = request()->get('property-field-property_type_id') == 11;
                        return !$isRent && !$isOffice && !$isLand;
                    })
                ],
                'property-features-transfer_fee' => [
                    Rule::requiredIf(function () {
                        $isRent = request()->get('property-field-ad_type') === 'rent';
                        $isOffice = request()->get('property-field-property_type_id') == 3;
                        $isLand = request()->get('property-field-property_type_id') == 11;
                        return !$isRent && !$isOffice && !$isLand;
                    })
                ],
                // 'property-features-transfer_fee' => 'exclude_if:property-field-ad_type,rent|required',
                'property-features-parking-info' => 'exclude_if:property-field-property_type_id,8,9,11,21,22,23,24,25,26,27,28,29,30,31,32,33,34|required',
                // 'property-features-description' => 'required|string|min:750|max:2000',
                'property-features-description' => [
                    function ($attribute, $value, $fail) {
                        $descriptionLength = mb_strlen(strip_tags($value));
                        if ($descriptionLength < 750 || $descriptionLength > 2000) {
                            $fail('The description should contain at least 750 chars and max 2000 characters.xx');
                        }
                    },
                ],
                'property-field-offers' => 'required',
                'property-field-current_offer' => '',
                'property-field-price_on_request' => '',
                'property-field-price' => 'required',
                'property-field-best_price' => 'required',
                'property-field-commission_ll_sale' => '',
                'property-field-commission_ll_rent' => '',
                'property-field-commission_ll_rent_and_sale' => '',
                'property-field-commission_tenant' => '',
                'property-field-commission_buyer' => '',
                'property-field-prorated_rata' => 'exclude_if:property-field-ad_type,sale|required',
                'property-field-payment_method_rent' => 'exclude_if:property-field-ad_type,sale|required',
                'property-field-payment_method_sale' => 'exclude_if:property-field-ad_type,rent|required',
                'property-field-payment_method_rent_and_sale' => '',
                'property-field-payment_cheques_no' => '',
                'property-field-payment_plan_info' => '',
                'property-field-rented_for' => '',
                'property-field-minimum_contract' => '',
                'property-field-payment_years' => '',

                'property-field-geo_lat' => 'required',
                'property-field-geo_lon' => 'required',
                'property-field-is_short_stay' => '',

                'property-brochure' => 'file|mimes:pdf',
                'property-brochure-remove' => '',

                'property-layout' => 'file',
                'property-layout-remove' => '',

                'property-info' => 'file',
                'property-info-remove' => '',

                'property-booking' => 'file',
                'property-booking-remove' => '',

                'primary_attachment' => '',
                'new_property_files' => '',
                'temp_dir' => '',
                'image_names' => 'required|array|min:5',
                'image_alts' => '',
                'primary_image' => '',
                'export_platforms' => 'array',

                // translations
                'property-translation-title-ar' => 'required',
                //disable the arabic title for the moment, because we don dont sync it
                'property-translation-propertyfinder_title-ar' => '',
                // 'property-translation-propertyfinder_title-ar'  => [Rule::when(
                //     request()->has('export_platforms') ? in_array('4', request()->get('export_platforms')) : "",
                //     ['required', 'min:30', 'max:50']
                // )],
                // 'property-translation-description-ar' => 'required|string|min:750|max:2000',
                'property-translation-description-ar' => [
                    function ($attribute, $value, $fail) {
                        $descriptionLength = mb_strlen(strip_tags($value));
                        if ($descriptionLength < 750 || $descriptionLength > 2000) {
                            $fail('The description should contain at least 750 chars and max 2000 characters.');
                        }
                    },
                ],
            ];
        } else {
            return [
                'representative-fullname' => '',
                'representative-email' => 'nullable|email',
                'representative-mobile_no' => 'nullable|max:40|regex:/^[0-9]*$/',
                'representative-prefix_mobile_no' => '',
                'representative-nationality_id' => '',
                'representative-qatar_id_no' => '',

                'property-field-is_exclusive' => '',
                // 'property-field-pictures_taken' => '',
                'property-field-is_homepage_promoted' => '',
                'property-field-is_sold_leased' => '',
                'property-field-ad_type' => 'required',
                'property-field-project_id' => '',
                'property-field-keys_place' => '',
                'property-field-unit_no' => [$unitNoAndTowerChecker],
                'property-field-tour_360' => 'nullable|url',
                'property-field-embeed_youtube' => 'nullable|url',
                'property-field-location_id' => 'required',
                'property-field-parking' => '',

                'property-field-title' => '',
                'property-field-propertyfinder_title' => '',
                'property-field-property_type_id' => 'required',
                'property-field-tower_id' => '',
                'property-field-bills' => '',

                'property-field-status' => '',
                'property-field-occupied_by' => '',
                'property-field-available_when' => '',
                'property-field-title_deed' => '',
                'property-field-reffered_by' => '',
                'operation-history' => '',
                'property-amenities' => '',
                'property-views_ids' => '',
                'property-field-contact_id' => 'required',
                'property-field-is_short_stay' => '',

                'property-features-construction-year' => '',
                'property-features-build-up-area' => '',
                'property-features-bedrooms' => '',
                'property-features-furnishings' => '',
                'property-features-furnishings-office' => '',
                'property-features-furnishings-retail' => '',
                'property-features-balcony' => '',
                'property-features-kitchen' => '',
                'property-features-pantry' => '',
                'property-features-bathrooms' => '',
                'property-features-service_charge' => '',
                'property-features-transfer_fee' => '',
                'property-features-parking-info' => '',
                'property-features-description' => '',

                'property-field-offers' => '',
                'property-field-current_offer' => '',
                'property-field-price_on_request' => '',
                'property-field-price' => '',
                'property-field-best_price' => '',
                'property-field-commission_ll_sale' => '',
                'property-field-commission_ll_rent' => '',
                'property-field-commission_ll_rent_and_sale' => '',
                'property-field-commission_tenant' => '',
                'property-field-commission_buyer' => '',
                'property-field-prorated_rata' => '',
                'property-field-payment_method_rent' => '',
                'property-field-payment_method_sale' => '',
                'property-field-payment_method_rent_and_sale' => '',
                'property-field-payment_cheques_no' => '',
                'property-field-payment_plan_info' => '',
                'property-field-rented_for' => '',
                'property-field-minimum_contract' => '',
                'property-field-payment_years' => '',

                'property-field-geo_lat' => '',
                'property-field-geo_lon' => '',

                'property-brochure' => 'file|mimes:pdf',
                'property-brochure-remove' => '',

                'property-layout' => 'file',
                'property-layout-remove' => '',

                'property-info' => 'file',
                'property-info-remove' => '',

                'property-booking' => 'file',
                'property-booking-remove' => '',

                'property-seo-title' => '',
                'property-seo-description' => '',
                'property-seo-keywords' => '',
                'primary_attachment' => '',
                'new_property_files' => '',
                'temp_dir' => '',
                'image_names' => '',
                'image_alts' => '',
                'primary_image' => '',
                'export_platforms' => '',

                // translations
                'property-translation-title-ar',
                'property-translation-propertyfinder_title-ar',
                'property-translation-description-ar',
            ];
        }
    }

    private $inventoryService;
    protected $propertiesService;
    protected $regionsService;
    protected $geographyService;
    protected $attachmentsService;
    protected $purifierService;
    protected $emailService;
    protected $operationHistoryService;
    protected $rolesAndPermissionsService;
    protected $amenitiesService;
    protected $attributesService;
    protected $representativeService;
    protected $userService;
    protected $propertyTypesService;
    protected $brokerLandlordsService;
    protected $exportService;
    protected $propertyFinderService;
    protected $propertyStatsService;
    protected $countriesService;

    public function __construct(
        CountriesService $countriesService,
        InventoryService           $inventoryService,
        PropertiesService          $propertiesService,
        RolesAndPermissionsService $rolesAndPermissionsService,
        UserService                $userService,
        AttachmentsService         $attachmentsService,
        AmenitiesService           $amenitiesService,
        RepresentativeService      $representativeService,
        PurifierService            $purifierService,
        EmailService               $emailService,
        OperationHistoryService     $operationHistoryService,
        PropertyTypesService       $propertyTypesService,
        BrokerLandlordsService     $brokerLandlordsService,
        ExportService              $exportService,
        PropertyFinderService      $propertyFinderService,
        PropertyStatsService       $propertyStatsService
    ) {
        $this->inventoryService = $inventoryService;
        $this->propertiesService = $propertiesService;
        $this->rolesAndPermissionsService = $rolesAndPermissionsService;
        $this->userService = $userService;
        $this->attachmentsService = $attachmentsService;
        $this->amenitiesService = $amenitiesService;
        $this->representativeService = $representativeService;
        $this->purifierService = $purifierService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->propertyTypesService = $propertyTypesService;
        $this->brokerLandlordsService = $brokerLandlordsService;
        $this->exportService = $exportService;
        $this->propertyFinderService = $propertyFinderService;
        $this->propertyStatsService = $propertyStatsService;
        $this->countriesService = $countriesService;
    }

    public function imagesResponse()
    {
        return response(null, 200);
    }

    protected function getDbItem($assetId)
    {
        return $this->propertiesService->getDbItem($assetId);
    }

    private function getIndexVars()
    {
        $geography = Geography::select(['id', 'name', 'type', 'slug', 'parent_id', 'visible', 'avg_price', 'created_at', 'updated_at', 'deleted_at'])->orderBy('name', 'ASC')->get()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        foreach ($geography as $g) {
            $g->content = "";
        }

        $cities = $geography->filter(function ($item) {
            return $item->type == 'city';
        });

        $regions = $geography->filter(function ($item) {
            return $item->type == 'region';
        });

        $areas = $geography->filter(function ($item) {
            return $item->type == 'area';
        });

        $user = auth()->user();
        $propertyViews = PropertyView::all();
        $valueLists = $this->inventoryService->getFilterAttributeValueLists();

        $bedroomsOptions = array_merge($valueLists['property-features-bedrooms']);
        $bathroomsOptions = array_merge($valueLists['property-features-bathrooms']);
        $kitchenOptions = array_merge($valueLists['property-features-kitchen']);
        $pantryOptions = array_merge($valueLists['property-features-pantry']);
        $furnishingOptions = $valueLists['property-features-furnishings'];
        $furnishingOptionsOffice = $valueLists['property-features-furnishings-office'];
        $billsOptions = $valueLists['property-features-bills'];
        $prmin = $this->inventoryService->minRentPrices;
        $prmax = $this->inventoryService->maxRentPrices;
        $psmin = $this->inventoryService->maxSalePrices;
        $psmax = $this->inventoryService->maxSalePrices;
        $tags = InventoryTag::get();
        $isCorporateOptions = $this->brokerLandlordsService->landlordCategory;
        $operationTypeOptions = array_merge([['value' => '', 'label' => 'All']], $this->inventoryService->operationTypeOptions);
        $developments = Development::where('is_published', 1)->get();
        $projectsOptions = array_merge([['value' => '', 'label' => 'Please select']], $developments->map(function ($dev) {
            return ['value' => $dev->id, 'label' => $dev->title];
        })->toArray());
        $propertyTypes = $this->propertiesService->getPropertyTypes(function ($item) {
            return $item->used;
        });

        $propertyViewOptions = $this->inventoryService->getPropertyViews()->map(function ($view) {
            return ['value' => $view->id, 'label' => $view->name];
        })->toArray();

        $isAdmin = $user->hasAnyRole([RolesDef::OFFICE_MANAGER]);

        $userCanEditAllListings = $user->hasPermissionTo(PermissionsDef::CAN_EDIT_ALL_LISTINGS);
        $hasMasterListAccess = $user->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::MASTER_BROKER, RolesDef::AGENT, RolesDef::MATRIX_AGENT, RolesDef::MATRIX_AGENT_MANAGER, RolesDef::TEAM_LEADER, RolesDef::MARKETING);
        $hasPendingListAccess = $user->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::MASTER_BROKER, RolesDef::MATRIX_AGENT_MANAGER, RolesDef::TEAM_LEADER) || $userCanEditAllListings;
        $hasPersonalListAccess = $user->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::AGENT, RolesDef::MATRIX_AGENT, RolesDef::MATRIX_AGENT_MANAGER, RolesDef::TEAM_LEADER);
        $hasTeamListAccess = $user->hasAnyRole(RolesDef::TEAM_LEADER);
        $hasPublishingRoles = $user->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::MASTER_BROKER);
        $listingStatusOptions = $this->listingStatusOptions;
        $listViewType = 'personal';
        $currentListLabel = "Personal List";
        if ($isAdmin) {
            $listViewType = 'master';
            $currentListLabel = "Available List";
        }

        // marketing platforms
        $onlyUsersInTeam = false;
        $marketingPlatforms = ExportPlatform::where('is_active', 1)->orderBy('name')->get();
        if (request()->has('vt')) {
            if (request()->get('vt') === 'master') {
                $listViewType = 'master';
                $currentListLabel = "Available List";
            } elseif (request()->get('vt') === 'not_available') {
                $listViewType = 'not_available';
                $currentListLabel = "Not Available";
            } elseif (request()->get('vt') === 'pending') {
                $listViewType = 'pending';
                $currentListLabel = "Pending List";
            } elseif (request()->get('vt') === 'archived') {
                $listViewType = 'archived';
                $currentListLabel = "Archived";
            } elseif (request()->get('vt') === 'personal') {
                $listViewType = 'personal';
                $currentListLabel = "Personal List";
            } else if (request()->get('vt') === 'all_listings') {
                $listViewType = 'all_listings';
                $currentListLabel = "All Listings";
            } else if (request()->get('vt') === 'team_list') {
                $listViewType = 'team_list';
                $currentListLabel = "My Team List";
                $onlyUsersInTeam = true;
            }
        }

        $agents = $this->userService->getPossibleListingOwners($onlyUsersInTeam);

        $consultantRole = $this->rolesAndPermissionsService
            ->allRolesCache()
            ->filter(function ($r) {
                return trim($r->name) == RolesDef::AGENT;
            })
            ->first();

        $viewColumnsSet = 'regular';

        if (!is_null($consultantRole) && $user->hasRole($consultantRole->id)) {
            $viewColumnsSet = 'rec';
        }

        $accessReadonly = false;

        return compact(
            'propertyViews',
            'cities',
            'accessReadonly',
            'regions',
            'areas',
            // 'users',
            'agents',
            'bedroomsOptions',
            'bathroomsOptions',
            'kitchenOptions',
            'pantryOptions',
            'listViewType',
            'propertyTypes',
            'furnishingOptions',
            'furnishingOptionsOffice',
            'propertyViewOptions',
            'viewColumnsSet',
            'billsOptions',
            'prmin',
            'prmax',
            'psmin',
            'psmax',
            'tags',
            'isCorporateOptions',
            'operationTypeOptions',
            'projectsOptions',
            'hasMasterListAccess',
            'hasPendingListAccess',
            'hasPersonalListAccess',
            'hasTeamListAccess',
            'currentListLabel',
            'hasPublishingRoles',
            'listingStatusOptions',
            'marketingPlatforms'
        );
    }

    protected function getItemUsedId($item)
    {
        return $item->asset_id;
    }

    public function store($mappedFields = null)
    {
        $isShortStayInventoryType = request()->has('inventoryType') && request()->get('inventoryType') == 'short-stay';
        if (request()->has('save')) {
            $validator = Validator::make(request()->all(), $this->getFieldsToValidate('store'), $this->validationMessages);
            $validator->sometimes('property-field-unit_no', 'required|max:128', function ($input) {
                $llId = request()->get('property-field-contact_id');

                $dbContact = Contact::where('id', $llId)->first();
                if (!is_null($dbContact)) {
                    if (!$dbContact->is_corporate_landlord) {
                        return true;
                    }
                }
                return false;
            });
            $validator->sometimes('property-features-construction-year', 'numeric|min:1900', function ($input) {
                $requestContstructionYear = request()->get('property-features-construction-year');
                if (!empty($requestContstructionYear)) {
                    return true;
                }
                return false;
            });
        } else if (request()->has('draft')) {
            $validator = Validator::make(request()->all(), $this->getFieldsToValidate('draft'), $this->validationMessages);
        }

        if ($validator->fails()) {
            return redirect()
                ->route('inventory.create', $isShortStayInventoryType ? ['inventoryType' => 'short-stay'] : [])
                ->withErrors($validator)
                ->withInput();
        }

        $validFields = $validator->getData();

        $item = $this->propertiesService->getNewItem();

        $this->updateObjectFieldsFromRequest($item, $validFields);
        $this->executePreCreate($item, $validFields);

        if (request()->has('draft')) {
            $item->publishing_status = ListingPublishingStatus::STATUS_DRAFT;
        }

        if ($isShortStayInventoryType) {
            $item->is_short_stay = true;
        }

        $item->asset->save();
        $item->asset_id = $item->asset->id;
        $item->save();

        $this->executePostCreate($item, $validFields);
        $this->inventoryService->getTopListingsForSale();

        return $this->handleRedirect($item);
    }

    protected function executePreCreate($item, $validFields)
    {
        $contact = Contact::find($item->contact_id);
        $nextRefNo = $this->propertiesService->getNextRefNo($validFields['property-field-property_type_id'], $item->ad_type);
        $item->ref_no = $nextRefNo . '-' . (empty($contact->record_no) ? '000' : $contact->record_no);
        $item->publishing_status = ListingPublishingStatus::STATUS_PENDING;
    }

    protected function executePostCreate($item, $validFields)
    {
        $this->addPropertyCreatedOperationHistory($item);
        $this->postSaveActions($item, $validFields);
        $this->handleAttributes($item, $validFields);
        $this->handleTranslations($item, $validFields);
        $this->syncMarketingPlatformsAssignment($item, $validFields);
        $this->emailService->sendPropertyAddedEmail($item);
    }

    private function postSaveActions($item, $validFields)
    {
        $this->saveRepresentative($item, $validFields);
        $this->handleOperationHistory($item, $validFields);
        $this->addPropertyUpdatedOperationHistory($item);
        $this->handlePropertyViews($item, $validFields);
        $this->handleAssignments($item, $validFields);
        $this->handlePrimaryAttachment($item, $validFields);
        $item->save();

        $this->resetCaches();
        // dd($validFields);
    }

    private function addPropertyCreatedOperationHistory($item)
    {
        $this->operationHistoryService->addOperationHistory($item, 'The listing was created by ' . auth()->user()->name . ' with the publishing status [' . $item->publishing_status . ']', auth()->user());
        $this->operationHistoryService->addOperationHistory($item->contact, 'A new listing [#' . $item->ref_no . '] has been added for this contact by ' . auth()->user()->name, auth()->user());
    }

    private function addPropertyUpdatedOperationHistory($item)
    {
        $this->operationHistoryService->addOperationHistory($item, 'The listing was saved by ' . auth()->user()->name . '. Listing publishing status is [' . $item->publishing_status . ']', auth()->user());
    }

    private function handlePropertyViews($item, $validFields)
    {
        $viewsToSyncWith = empty($validFields['property-views_ids']) ? [] : explode(',', ($validFields['property-views_ids'] ?? ''));
        $item->propertyViews()->sync($viewsToSyncWith);
    }

    private function saveRepresentative($item, $validFields)
    {
        $this->representativeService->saveFormListingRepresentative($item, $validFields);
    }

    private function handleOperationHistory($item, $validFields)
    {
        if (!empty($validFields['operation-history'])) {
            $this->operationHistoryService->addOperationHistory($item, $validFields['operation-history'], auth()->user());

            if (auth()->user()->id !== $item->created_by) {
                $this->emailService->sendEmailOnAddingOperationHistory($item, auth()->user(), $validFields['operation-history']);
            }
        }
    }

    private function handleAttributes($item, $validFields)
    {
        $definition = $this->getDbDefinition();
        foreach ($definition->attributeGroups as $group) {
            foreach ($group->attributes as $attrDef) {
                $attribute = Attribute::where('attribute_definition_id', $attrDef->id)->where('asset_id', $item->asset_id)->first();
                if (is_null($attribute)) {
                    $attribute = new Attribute();
                    $attribute->attribute_definition_id = $attrDef->id;
                    $attribute->asset_id = $item->asset_id;
                }

                $clientId = $attrDef->name;
                $value = request()->input($clientId);

                if (request()->has($clientId)) {
                    Log::info("Found " . $attrDef->name . " = " . $value);
                }

                switch ($attrDef->type->name) {
                    case "dropdown":
                        if ($value == null || $value == "") {
                            $attribute->value = null;
                        } else {
                            $attribute->value = $value;
                        }
                        break;
                    case "text-box":
                        $value_large = rawurldecode(str_replace('i-f-r-a-m-e', 'iframe', $value));
                        $attribute->value_large = $this->purifierService->purify($value_large);
                        break;
                    default:
                        $attribute->value = $value;
                        break;
                }
                $attribute->save();
            }
        }

        $existingAttributes = Attribute::with('definition')->where('asset_id', '=', $item->asset_id)->get()->mapWithKeys(function ($item) {
            return [$item->definition->name => $item];
        });

        foreach ($validFields as $fieldName => $fieldValue) {
            if (Str::startsWith($fieldName, 'property-features-')) {
                $dbKey = $fieldName;
                if ($fieldName === 'property-features-description') {
                    $dbKey = 'description';
                }
                if (!$existingAttributes->has($dbKey)) {
                    $attrDef = AttributeDefinition::with('type')->where('name', '=', $dbKey)->first();
                    if (is_null($attrDef)) {
                        dd('null definition for ', $fieldName);
                    }

                    $attribute = new Attribute();
                    $attribute->attribute_definition_id = $attrDef->id;
                    $attribute->asset_id = $item->asset_id;
                } else {
                    $attribute = $existingAttributes->get($dbKey);
                    $attrDef = $attribute->definition;
                }

                switch ($attrDef->type->name) {
                    case "dropdown":
                        if ($fieldValue == null || $fieldValue == "") {
                            $attribute->value = null;
                        } else {
                            $attribute->value = $fieldValue;
                        }
                        break;
                    case "text-box":
                        $value_large = rawurldecode(str_replace('i-f-r-a-m-e', 'iframe', $fieldValue));
                        $attribute->value_large = $this->purifierService->purify($value_large);

                        break;
                    default:
                        $attribute->value = $fieldValue;
                        break;
                }

                $attribute->save();
            }

            if ($fieldName == 'property-amenities') {
                // remove all the amenities of the listing and insert them again
                $this->amenitiesService->syncListingAmenities($item, $fieldValue);
            }
        }
    }

    private function handleAssignments($item, $validFields)
    {
        if (!empty(request()->input('temp-dir'))) {
            $assignments = AttachmentAssignment::where('temp_object_id', request()->input('temp-dir'))
                ->get();

            foreach ($assignments as $assignment) {

                // move the images from temp to
                $dirName = $assignment->temp_object_id;
                $assignment->object_type = 'properties';
                $assignment->temp_object_id = null;
                $assignment->object_id = $item->id;
                $assignment->save();

                $this->attachmentsService->moveFromTempDir($dirName, $assignment->attachment->name, $item->id);
            }
        }

        $propertyBrochureFile = $validFields['property-brochure'] ?? null;
        $deleteOldBrochureFile = isset($validFields['property-brochure-remove']);
        $this->attachmentsService->syncPropertyBrochure($item, $propertyBrochureFile, $deleteOldBrochureFile);

        $propertyMoreInfoFile = $validFields['property-info'] ?? null;
        $deleteOldInfoFile = isset($validFields['property-info-remove']);
        $this->attachmentsService->syncPropertyInfo($item, $propertyMoreInfoFile, $deleteOldInfoFile);

        $propertyBookingFormFile = $validFields['property-booking'] ?? null;
        $deleteOldBookingFile = isset($validFields['property-booking-remove']);
        $this->attachmentsService->syncPropertyBooking($item, $propertyBookingFormFile, $deleteOldBookingFile);

        $propertyLayoutFormFile = $validFields['property-layout'] ?? null;
        $deleteOldLayoutFile = isset($validFields['property-layout-remove']);
        $this->attachmentsService->syncPropertyLayout($item, $propertyLayoutFormFile, $deleteOldLayoutFile);
    }

    private function handlePrimaryAttachment($item, $validFields)
    {
        $primaryAttachment = $validFields["primary_attachment"] ?? '';

        if ($primaryAttachment) {

            $attachments = $this->attachmentsService->getObjectAttachments($item, false);
            $oldPrimary = $attachments->first(function ($value, $key) {
                return $value->is_primary == 1;
            });

            if ($oldPrimary && $oldPrimary->id != $primaryAttachment) {
                $oldPrimary->is_primary = 0;
                unset($oldPrimary->seo);
                $oldPrimary->save();
            }

            $attachment = $this->attachmentsService->getAttachmentById($primaryAttachment);

            if ($attachment) {
                $attachment->is_primary = 1;
            }
            $attachment->save();
        }
    }

    private function syncMarketingPlatformsAssignment($item, $validFields)
    {
        // only for listing admins
        $user = auth()->user();
        $isAdmin = $user->hasAnyRole([RolesDef::OFFICE_MANAGER]);
        $userCanEditAllListings = $user->hasPermissionTo(PermissionsDef::CAN_EDIT_ALL_LISTINGS);
        if ($isAdmin || $userCanEditAllListings) {
            if (isset($validFields['export_platforms'])) {
                $selectedPlatforms = collect($validFields['export_platforms']);
                foreach ($this->exportService->allExportPlatforms() as $platform) {
                    if ($selectedPlatforms->contains(function ($key) use ($platform) {
                        return $platform->id == $key;
                    })) {
                        $this->exportService->assignPropertyToPlatform($item->id, $platform->id);
                    } else {
                        $this->exportService->removePropertyFromPlatform($item->id, $platform->id);
                    }
                }
            } else {
                // remove this listing from all the exporting platforms
                foreach ($this->exportService->allExportPlatforms() as $platform) {
                    $this->exportService->removePropertyFromPlatform($item->id, $platform->id);
                }
            }
        }
    }

    private function handleTranslations($item, $validFields)
    {
        // ar
        $translations = PropertyTranslation::where('property_id', $item->id)->get();
        $translationAr = $translations->filter(function ($item) {
            return $item->language === 'ar';
        })->first();
        if (is_null($translationAr)) {
            $translationAr = new PropertyTranslation();
            $translationAr->property_id = $item->id;
            $translationAr->language = 'ar';
        }
        $translationAr->title = $validFields['property-translation-title-ar'];
        $translationAr->propertyfinder_title = $validFields['property-translation-propertyfinder_title-ar'];
        $translationAr->description = $validFields['property-translation-description-ar'];
        $translationAr->save();
    }

    protected function executePreUpdate($item, $validFields, $originalItem = null)
    {
        if (
            $validFields['property-field-property_type_id'] != $originalItem->property_type_id || $validFields['property-field-ad_type'] != $originalItem->ad_type
            || $validFields['property-field-contact_id'] != $originalItem->contact_id
        ) {
            $contact = Contact::find($item->contact_id);
            $nextRefNo = $this->propertiesService->getNextRefNo($validFields['property-field-property_type_id'], $item->ad_type);
            $item->ref_no = $nextRefNo . '-' . (empty($contact->record_no) ? '000' : $contact->record_no);
        }
        $this->inventoryDataChangeSendEmail($item, $validFields, $originalItem);
    }

    public function update($itemId)
    {
        if (request()->has('save') || request()->has('savePhotos')) {
            $validator = Validator::make(request()->all(), $this->getFieldsToValidate('update', $itemId), $this->validationMessages);
            $validator->sometimes('property-field-unit_no', 'required|max:128', function ($input) {
                $llId = request()->get('property-field-contact_id');
                $dbContact = Contact::where('id', $llId)->first();
                if (!is_null($dbContact)) {
                    if (!$dbContact->is_corporate_landlord) {
                        return true;
                    }
                }
                return false;
            });
            $validator->sometimes('property-features-construction-year', 'numeric|min:1900', function ($input) {
                $requestContstructionYear = request()->get('property-features-construction-year');
                if (!empty($requestContstructionYear)) {
                    return true;
                }
                return false;
            });
        } else if (request()->has('draft')) {
            $validator = Validator::make(request()->all(), $this->getFieldsToValidate('draft', $itemId), $this->getFieldsToValidate('draft', $itemId));
        }

        if ($validator->fails()) {
            return redirect('crm/inventory/' . $itemId . '/edit')
                ->withErrors($validator)
                ->withInput();
        }

        $validFields = $validator->getData();
        $item = $this->getDbItem($itemId);
        $originalItem = $item->replicate();
        $this->updateObjectFieldsFromRequest($item, $validFields);
        $this->executePreUpdate($item, $validFields, $originalItem);
        $isPhotographer = auth()->user()->hasRole(RolesDef::PHOTOGRAPHER);

        if (request()->has('draft')) {
            $item->publishing_status = ListingPublishingStatus::STATUS_DRAFT;
        }

        if (
            request()->has('save') && ($item->created_by == auth()->user()->id || $isPhotographer)
        ) {
            $item->publishing_status = ListingPublishingStatus::STATUS_PENDING;
        }

        $item->updated_at = new \DateTime();
        $item->updated_by = auth()->user()->id;
        $item->save();
        // execute post save hooks
        $this->executePostUpdate($item, $validFields);
        $this->inventoryService->getTopListingsForSale();

        return $this->handleRedirect($item);
    }

    protected function executePostUpdate($item, $validFields)
    {
        $this->postSaveActions($item, $validFields);
        $this->handleAttributes($item, $validFields);
        $this->syncMarketingPlatformsAssignment($item, $validFields);
        $this->handleTranslations($item, $validFields);
        $this->handlePropertySnapshot($item);
    }

    protected function updateObjectFieldsFromRequest($listing, $validFields)
    {
        foreach ($validFields as $requestFieldName => $validValue) {
            if (Str::startsWith($requestFieldName, 'property-field-')) {
                if (in_array($requestFieldName, $this->conditionalFields)) {
                    $fieldData = $this->getConditionalFieldsResult($requestFieldName, $validFields);
                    if (is_array($fieldData)) {
                        $listing->{str_replace('property-field-', '', $fieldData[0])} = $fieldData[1];
                    }
                } else {
                    $fieldName = str_replace('property-field-', '', $requestFieldName);
                    $listing->$fieldName = $validValue;
                }
            }
        }

        // images
        $listing->images = empty($validFields['image_names']) ? [] : $validFields['image_names'];
        $listing->image_alts = empty($validFields['image_alts']) ? [] : $validFields['image_alts'];
        if (!empty($validFields['primary_image']) && in_array($validFields['primary_image'], $validFields['image_names'])) {
            $listing->primary_image = $validFields['primary_image'];
        }

        $isManager = auth()->user()->hasAnyRole([RolesDef::OFFICE_MANAGER]);
        $isAdmin = auth()->user()->hasAnyRole([RolesDef::MASTER_BROKER, RolesDef::OFFICE_MANAGER]);

        if ($isManager) {
            $is_exclusive = false;
            if (isset($validFields['property-field-is_exclusive'])) {
                $is_exclusive = true;
            }

            $listing->is_exclusive = $is_exclusive;
        }

        if ($isAdmin) {
            $is_homepage_promoted = false;
            if (isset($validFields['property-field-is_homepage_promoted'])) {
                $is_homepage_promoted = true;
            }
            $listing->is_homepage_promoted = $is_homepage_promoted;

            $price_on_request = false;
            if (isset($validFields['property-field-price_on_request'])) {
                $price_on_request = true;
            }
            $listing->price_on_request = $price_on_request;
            $isShortStay = false;
            if (isset($validFields['property-field-is_short_stay'])) {
                $isShortStay = true;
            }
            $listing->is_short_stay = $isShortStay;
        }

        $hasAccesToInvestmentOpportunity = in_array(auth()->user()->email, ['<EMAIL>']);

        if ($isAdmin || $hasAccesToInvestmentOpportunity || (!empty($validFields['property-field-country_id']) && $validFields['property-field-country_id'] != 187)) {
            $is_investment_opportunity = false;
            if (isset($validFields['property-field-is_investment_opportunity']) || (!empty($validFields['property-field-country_id']) && $validFields['property-field-country_id'] != 187)) {
                $is_investment_opportunity = true;
            }
            $listing->is_investment_opportunity = $is_investment_opportunity;
        }

        $offplan = false;
        if (isset($validFields['property-field-offplan'])) {
            $offplan = true;
        }
        $listing->offplan = $offplan;

        $listing->geo_point = null;

        if (!empty($listing->geo_lat) && !empty($listing->geo_lon)) {
            $listing->geo_point = Point::fromPair($listing->geo_lon . ' ' . $listing->geo_lat);
        }
    }

    private function getConditionalFieldsResult($requestFieldName, $validFields)
    {
        $adType = $validFields['property-field-ad_type'];
        $fieldRoot = str_replace(['_rent', '_sale'], '', str_replace('_rent_and_sale', '', $requestFieldName));

        $computedFieldName = $fieldRoot . '_' . $adType;

        return [$fieldRoot, $validFields[$computedFieldName]];
    }

    private function getProperty($assetId)
    {
        if (empty($assetId)) {
            $property = new Property();
            $property->publishing_status = ListingPublishingStatus::STATUS_PENDING;

            // default fields for new items
            $property->unit_no = 'N/A';
            $property->keys_place = 'at reception';

            return $property;
        }

        return $this->getDbItem($assetId);
    }

    private function getEditViewVars($assetId = null)
    {
        $succesDuplicate = request()->get('succes');
        $isCreateAction = is_null($assetId);
        $empty = false;
        $delete = false;
        $readonly = false;

        $asset = empty($assetId) ? new Asset() : Asset::findOrFail($assetId);
        $definition = AssetDefinition::with(['attributeGroups' => function ($query) {
            $query->where('visible', '=', 1);
        }])->find(1);

        $user = auth()->user();

        $property = $this->getProperty($assetId);
        $propertyData = $property->attributesToArray();

        $translations = [
            'ar' => null,
        ];
        foreach ($property->translations as $translation) {
            $translations[$translation->language] = [
                'title' => $translation->title,
                'propertyfinder_title' => $translation->propertyfinder_title,
                'description' => $translation->description,
            ];
        }

        unset($propertyData['published_snapshot']);

        $isAuthor = $property->created_by == $user->id;
        $userIsRefferer = $property->reffered_by == $user->id;
        $isOfficeManager = $user->hasRole([RolesDef::OFFICE_MANAGER]);
        $isPhotographer = $user->hasRole([RolesDef::PHOTOGRAPHER]);
        $isTeamLeader = $user->hasRole([RolesDef::TEAM_LEADER]);
        $authorIsInCurrentUsersTeam = !is_null($property->author) && ($property->author->team_leader_id == $user->id);
        $userIsTeamLeaderAndOwnsListing = $isTeamLeader && $property->created_by == auth()->user()->id;
        $userIsPhotographAndListingNeedsPictures = $isPhotographer && $property->publishing_status == ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES;
        $userCanChangePublishingStatus = auth()->user()->hasPermissionTo(PermissionsDef::CAN_UPDATE_LISTING_PUBLISHING_STATUS);
        $userCanEditAllListings = auth()->user()->hasPermissionTo(PermissionsDef::CAN_EDIT_ALL_LISTINGS);

        $accessReadonly = !empty($assetId) && !($isAuthor || $userIsRefferer || $isOfficeManager || $userIsPhotographAndListingNeedsPictures || $authorIsInCurrentUsersTeam || $userCanEditAllListings);
        $itemHasStatusChangeable = in_array($property->publishing_status, [
            ListingPublishingStatus::STATUS_PENDING,
            ListingPublishingStatus::STATUS_MASTERLIST,
            ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE,
            ListingPublishingStatus::STATUS_PUBLISHED,
            ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES,
        ]);
        $userCanPublishOwnListings = $user->hasPermissionTo(PermissionsDef::CAN_PUBLISH_OWN_LISTINGS);
        $userCanPublishThisListing = $userCanPublishOwnListings && $isAuthor;
        $canChangeStatus = $itemHasStatusChangeable && ($isOfficeManager || ($isTeamLeader && $authorIsInCurrentUsersTeam) || $userIsTeamLeaderAndOwnsListing || $userCanChangePublishingStatus || $userCanPublishThisListing);

        if (!empty(old('property-field-contact_id'))) {
            $contact = Contact::find(old('property-field-contact_id'));
        } elseif ($property->exists) {
            $contact = $property->contact;
        } else {
            $contact = new Contact();
        }

        $landlordData = $contact->landlordData ?? new ContactLandlordData();

        $geography = Geography::select(['id', 'name', 'type', 'slug', 'parent_id', 'visible', 'avg_price', 'created_at', 'updated_at', 'deleted_at'])->orderBy('name', 'ASC')->get()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        $countries = Country::get()->map(function ($item) {
            return [
                'label' => $item->name,
                'value' => $item->id
            ];
        });

        // dd($countries);

        foreach ($geography as $g) {
            $g->content = "";
        }

        $cities = $geography->filter(function ($item) {
            return $item->type == 'city';
        });

        $regions = $geography->filter(function ($item) {
            return $item->type == 'region';
        });

        $areas = $geography->filter(function ($item) {
            return $item->type == 'area';
        });

        $actionName = $isCreateAction ? 10 : 20;

        $attributes = is_null($assetId) ? collect([]) : Attribute::where('asset_id', $assetId)->get();
        $attributesMap = $attributes->mapWithKeys(function ($a) {
            return [$a->attribute_definition_id => $a->name];
        });

        $listingFormAttributesMap = array_merge($this->listingFormAttributesMap, ['property-features-description']);

        $formAttributes = (is_null($attributes) ? collect([]) : $attributes)->filter(function ($att) use ($listingFormAttributesMap) {
            return in_array($att->definition->name, $listingFormAttributesMap) || $att->definition->name === 'description';
        })->mapWithKeys(function ($att) {
            $key = $att->definition->name === 'description' ? 'property-features-description' : $att->definition->name;
            return [$key => $att];
        });
        $propertyViews = PropertyView::all();

        if (!is_null($property)) {
            $operationHistory = $property->operationHistory;
        }

        $operationTypeOptions = array_merge([['value' => '', 'label' => 'Please select']], $this->inventoryService->operationTypeOptions);
        $developments = Development::where('is_published', 1)->get();
        $projectsOptions = array_merge([['value' => '', 'label' => 'Please select']], $developments->map(function ($dev) {
            return ['value' => $dev->id, 'label' => $dev->title];
        })->toArray());
        $valueLists = $this->inventoryService->getFilterAttributeValueLists();
        $pleaseSelectOption = (object)['label' => 'Please select', 'value' => ''];

        // amenities
        $amenitiesList = $this->amenitiesService->getListingAmenitiesOptions()->map(function ($a) {
            return [
                'value' => $a->id,
                'label' => $a->label
            ];
        });

        $selectedAmenitiesIds = [];
        foreach ($amenitiesList as $ai) {
            if ($attributesMap->has($ai['value'])) {
                $selectedAmenitiesIds[] = $ai['value'];
            }
        }

        $bedroomsOptions = array_merge([$pleaseSelectOption], $valueLists['property-features-bedrooms']);
        $bathroomsOptions = array_merge([$pleaseSelectOption], $valueLists['property-features-bathrooms']);
        $kitchenOptions = array_merge([$pleaseSelectOption], $valueLists['property-features-kitchen']);
        $pantryOptions = array_merge([$pleaseSelectOption], $valueLists['property-features-pantry']);
        $billsOptions = $valueLists['property-features-bills'];
        $furnishingOptions = array_merge([$pleaseSelectOption], $valueLists['property-features-furnishings']);
        $furnishingRetailOptions = array_merge([$pleaseSelectOption], $valueLists['property-features-furnishings-retail']);
        $listingStatusOptions = $this->listingStatusOptions;
        $propertyNationalities = Nationality::all();
        $isNewRepresentative = empty($property->representative_id);
        $representative = $isNewRepresentative ? new PropertyRepresentative() : $property->representative;
        $publishingStatus = $property->publishing_status;
        $geographyTowers = [];
        if (!is_null($property->location_id) || old('property-field-location_id', $property->location_id)) {
            $geographyTowers = \App\Models\API\Tower::where('geography_id', '=', old('property-field-location_id', $property->location_id))->get()->map(function ($item) {
                return [
                    'label' => $item->name,
                    'value' => $item->id
                ];
            })->toArray();
        }

        $locations = [];
        if (!is_null($property->country_id) || old('property-field-country_id', $property->country_id)) {
            $locations = \App\Models\Geography::where('country_id', '=', old('property-field-country_id', $property->country_id))->get()->map(function ($item) {
                return [
                    'label' => $item->name,
                    'value' => $item->id
                ];
            })->toArray();
        }

        $isNewTower = empty($property->tower_id);
        $tower = $isNewTower ? new Tower() : $property->theTower;
        if (is_null($tower) && $property->tower_id == 1000) {
            $tower = new Tower();
            $tower->id = "1000";
            $tower->name = "Any";
        }

        $currentStatus = old('property-field-status') ?? $property->status;
        $currentPropertyTypeId = old('property-field-property_type_id') ?? $property->property_type_id;
        $currentPropertyType = null;
        $propertyTypes = $this->propertiesService->cachedPropertyTypes();

        if (!empty($currentPropertyTypeId)) {
            $currentPropertyType = $propertyTypes->filter(function ($pt) use ($currentPropertyTypeId) {
                return $pt['id'] == $currentPropertyTypeId;
            })->first();
        }

        $possibleListingOwners = [];
        $possibleListingOwners = $this->userService->getPossibleListingOwners()->map(function ($item) {
            return ['value' => $item->id, 'label' => $item->selectName];
        })->toArray();

        // documents
        $documents = $this->attachmentsService->getPropertyDocuments($property)->map(function ($item) use ($property) {
            $attachment = $item->attachment;
            $attachment->url = Storage::url('listing-documents/' . $property->id . '/' . $item->name);
            return $attachment;
        });

        // commissions
        $emptyOption = (object)['value' => '', 'label' => 'Please select'];
        $arr5 = [$emptyOption];
        $arr9 = [$emptyOption];
        $arr11 = [$emptyOption];

        for ($i = 0; $i < 11; $i++) {
            if ($i < 5) {
                $arr5[] = $i;
            }
            if ($i < 9) {
                $arr9[] = $i;
            }
            $arr11[] = $i;
        }

        $percentMapper = function ($item) {
            if (is_object($item)) {
                return $item;
            };
            $tempArr = ['value' => $item, 'label' => $item . ' %'];
            return (object)$tempArr;
        };
        $weekMapper = function ($item) {
            if (is_object($item)) {
                return $item;
            };
            $tempArr = ['value' => $item, 'label' => $item . ' ' . Str::plural('week', $item)];
            return (object)$tempArr;
        };
        $weekIncMapper = function ($item) {
            if (is_object($item)) {
                return $item;
            }
            $tempArr = ['value' => $item + 1, 'label' => ($item + 1) . ' weeks'];
            return (object)$tempArr;
        };
        $commissionLLOptions = array_map($percentMapper, $arr5);
        $commissionLLRentOptions = array_map($weekMapper, $arr9);
        $commissionBuyerOptions = array_map($percentMapper, $arr11);
        $commissionTenantOptions = array_map($weekIncMapper, $arr9);

        $yesNoOptions = array_map(function ($item) {
            return (object)$item;
        }, [
            ['value' => '', 'label' => 'Please select'],
            ['value' => '1', 'label' => 'Yes'],
            ['value' => '0', 'label' => 'No'],
        ]);

        $datedUndatedOpts = array_map(function ($item) {
            return (object)$item;
        }, [
            ['value' => '', 'label' => 'Please select'],
            ['value' => '1', 'label' => 'Dated'],
            ['value' => '0', 'label' => 'Undated'],
        ]);

        $shareLinks = null;
        $propertyfinderReference = null;
        if ($property->publishing_status === 'published') {
            $snapshot = $this->propertiesService->getSnapshot($property->id, $property->ref_no);
            if (!is_null($snapshot)) {
                $routeListingItem = new RouteListingItem();
                $routeListingItem->id = $snapshot->listing_id;
                $routeListingItem->ref_no = $snapshot->ref_no;
                $routeListingItem->ad_type = $snapshot->ad_type;
                $routeListingItem->geography_slug = '';
                $routeListingItem->bedrooms = intval($snapshot->bedrooms) ?? 0;

                $cachedGeographies = $this->propertiesService->cachedGeographiesMap();

                foreach ($cachedGeographies as $geoSlug => $geoData) {
                    if ($geoData['id'] == $snapshot->location_id) {
                        $usedSlug = $geoSlug;
                        $routeListingItem->geography_slug = $usedSlug;
                    }
                }

                $cachedPropertyTypesHashmap = $this->propertiesService->cachedPropertyTypesHashmap();

                foreach ($cachedPropertyTypesHashmap as $_ => $propertyTypeData) {
                    if ($propertyTypeData['id'] == $snapshot->property_type_id) {
                        $routeListingItem->property_type_url = $propertyTypeData['filter_value'];
                    }
                }

                $snapshotURL = MenuHelperService::createListingURL($routeListingItem);
                $shareLinks = Share::page($snapshotURL, 'Share this listing')
                    ->facebook()
                    ->twitter()
                    ->whatsapp();

                if(!empty($snapshot->remote_propertyfinder_id)) {
                    $propertyfinderReference = $snapshot->remote_propertyfinder_id;
                }
            }
        }

        $isAdmin = auth()->user()->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::MASTER_BROKER);

        // export assignments
        $marketingPlatformAssignments = collect([]);
        if ($property->exists) {
            $marketingPlatformAssignments = ExportAssignment::where('property_id', $property->id)->get();
        }

        $minimumContractOptions = $this->propertiesService->getMinimumContractOptions();
        $countries = $this->countriesService->getCountries();

        $hasAccesToInvestmentOpportunity = in_array($user->email, ['<EMAIL>']);
        $isShortStay = old('property-field-ad_type', $property->ad_type) == 'rent' && in_array(old('property-field-minimum_contract', $property->minimum_contract), [0.01, 0.02, 0.03, 0.1]);
        $isShortStayInventoryType = (request()->has('inventoryType') && request()->get('inventoryType') == 'short-stay') || ($property->exists && $property->is_short_stay);

        return compact([
            'empty',
            'delete',
            'readonly',
            'accessReadonly',
            'actionName',
            'asset',
            'property',
            'contact',
            'landlordData',

            'countries',

            'cities',
            'regions',
            'areas',
            'definition',
            'attributes',
            'actionName',
            'formAttributes',
            'propertyViews',
            'operationHistory',
            'valueLists',
            'bedroomsOptions',
            'bathroomsOptions',
            'kitchenOptions',
            'pantryOptions',
            'billsOptions',
            'listingStatusOptions',
            'publishingStatus',
            'propertyNationalities',
            'representative',
            'canChangeStatus',
            'isAuthor',
            'userIsRefferer',
            'userCanEditAllListings',
            'propertyData',
            'furnishingOptions',
            'geographyTowers',
            'locations',
            'documents',
            'yesNoOptions',
            'datedUndatedOpts',
            'currentStatus',
            'currentPropertyType',
            'amenitiesList',
            'selectedAmenitiesIds',
            'possibleListingOwners',
            'shareLinks',
            'propertyTypes',
            'commissionLLOptions',
            'commissionLLRentOptions',
            'commissionBuyerOptions',
            'commissionTenantOptions',
            'furnishingRetailOptions',
            'operationTypeOptions',
            'projectsOptions',
            'marketingPlatformAssignments',
            'isNewTower',
            'tower',
            'minimumContractOptions',
            'succesDuplicate',
            'isOfficeManager',
            'isAdmin',
            'hasAccesToInvestmentOpportunity',
            'authorIsInCurrentUsersTeam',
            'isShortStay',
            'translations',
            'user',
            'countries',
            'isShortStayInventoryType',
            'propertyfinderReference'
        ]);
    }

    protected function viewVars(string $viewName, $assetId = null)
    {
        if (in_array($viewName, ['edit', 'create'])) {
            return $this->getEditViewVars($assetId,);
        }
        return $this->getIndexVars();
    }

    public function search($fromAPI = false, $extraClauses = [])
    {
        $now = new \DateTime();
        $user = auth()->user();
        $userIsOfficeManager = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $userIsMasterBroker = $user->hasRole(RolesDef::MASTER_BROKER);
        $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
        $userCanPublishOwnListings = $user->hasPermissionTo(PermissionsDef::CAN_PUBLISH_OWN_LISTINGS);
        $attributeValueLists = $this->inventoryService->getFilterAttributeValueLists();

        if (request()->ajax() || $fromAPI) {
            $items = $this->inventoryService->getTableItems(request(), false, $extraClauses)->map(function ($item) use ($now, $userIsOfficeManager, $userIsMasterBroker, $userIsTeamLeader, $attributeValueLists, $userCanPublishOwnListings) {
                return $this->tableItemsMapper($item, $now, $userIsOfficeManager, $userIsMasterBroker, $userIsTeamLeader, $attributeValueLists, null, $userCanPublishOwnListings);
            });
            $count = $this->inventoryService->getTableItemsCount(request(), $extraClauses);

            return [
                'data' => $items,
                'recordsTotal' => $count,
                'recordsFiltered' => $count
            ];
        }

        return [];
    }

    protected function tableItemsMapper($item, $now, $userIsOfficeManager, $userIsMasterBroker, $userIsTeamLeader, $attributeValueLists, $amenitiesDefinitions, $userCanPublishOwnListings)
    {
        $furnishingLabel = '';
        $currentUser = auth()->user();
        if ($item->furnishing) {
            $usedFurnishings = array_values(array_filter(array_merge($attributeValueLists['property-features-furnishings'], $attributeValueLists['property-features-furnishings-office']), function ($f) use ($item) {
                return $f['value'] == $item->furnishing;
            }));
            if (count($usedFurnishings) > 0) {
                $furnishingLabel = $usedFurnishings[0]['label'];
            }
        }
        $updatedAt = new \DateTime($item->updated_at);
        $updatedAtDiff = date_diff($now, $updatedAt);

        $brochureObject = null;
        if (!empty($item->brochure_path)) {
            $brochureObject = (object)['url' => route('properties.brochure.download', ['locale' => 'en', 'propertyWord' => __('property'), 'id' => $item->id, 'refNo' => $item->ref_no]), 'title' => $item->brochure_title];
        }

        $webLink = '';
        $personalWebLink = null;

        if ($item->publishing_status === 'published') {
            $routeListingItem = new RouteListingItem();
            $routeListingItem->id = $item->id;
            $routeListingItem->ref_no = $item->ref_no;
            $routeListingItem->ad_type = $item->ad_type;
            $routeListingItem->geography_slug = '';
            $routeListingItem->bedrooms = intval($item->bedrooms_no_next) ?? 0;

            $cachedGeographies = $this->propertiesService->cachedGeographiesMap();
            $cachedPropertyTypesHashmap = $this->propertiesService->cachedPropertyTypesHashmap();

            foreach ($cachedGeographies as $geoSlug => $geoData) {
                if ($geoData['id'] == $item->location_id) {
                    $usedSlug = $geoSlug;
                    $routeListingItem->geography_slug = $usedSlug;
                }
            }

            foreach ($cachedPropertyTypesHashmap as $_ => $propertyTypeData) {
                if ($propertyTypeData['id'] == $item->property_type_id) {
                    $routeListingItem->property_type_url = $propertyTypeData['filter_value'];
                }
            };
            $webLink = MenuHelperService::createListingURL($routeListingItem);

            if (!is_null($currentUser) && !empty($currentUser->slug)) {
                $personalWebLink = route('single.listing.share-personal', ['locale' => 'en', 'agentSlug' => $currentUser->slug, 'listingId' => $item->id]);
            }
        }

        $unitType = "";
        $unitTypePrefix = $this->propertiesService->getPrefixForPropertyTypeId($item->property_type_id);
        if (!empty($item->bedrooms_no_next) && doubleval($item->bedrooms_no_next) > 0) {
            if ($item->property_type_id == 1 && $item->bedrooms_no_next == 0) {
                $unitType = "Studio";
            } else {
                $unitType = $unitTypePrefix;
                $unitType .= " | " . $item->bedrooms_no_next . "BR";
            }
        }
        if (empty($unitType)) {
            $unitType = $unitTypePrefix;
        }
        if (!empty($item->build_up_area)) {
            $unitType .= " | " . $item->build_up_area . "sqm";
        }

        // handle ll availability_lists
        $currentAvailabilityListURL = "";
        if (!empty($item->availability_lists) && $item->is_corporate_landlord) {
            $availabilityLists = json_decode($item->availability_lists);
            if (is_array($availabilityLists) && count($availabilityLists) > 0) {
                usort($availabilityLists, function ($item1, $item2) {
                    $item1Date = new \DateTime($item1->aTime);
                    $item2Date = new \DateTime($item2->aTime);
                    $item1DateEpoch = $item1Date->format('U');
                    $item2DateEpoch = $item2Date->format('U');
                    if ($item1DateEpoch > $item2DateEpoch) {
                        return -1;
                    } else if ($item1DateEpoch < $item2DateEpoch) {
                        return 1;
                    }
                    return 0;
                });

                // if the listing has tower_id, try to find the availability list for that tower. If not, get the most recent availability list without any tower
                $usedAvailabilityListItem = null;
                $firstAvailabilityListItemWithoutTower = null;
                if (!empty($item->tower_id)) {
                    foreach ($availabilityLists as $availabilityListItem) {
                        if (is_null($firstAvailabilityListItemWithoutTower) && (!isset($availabilityListItem->towerId) || empty($availabilityListItem->towerId))) {
                            $firstAvailabilityListItemWithoutTower = $availabilityListItem;
                        }
                        if (isset($availabilityListItem->towerId) && $availabilityListItem->towerId == $item->tower_id) {
                            $usedAvailabilityListItem = $availabilityListItem;
                            break;
                        }
                    }
                }
                if (is_null($usedAvailabilityListItem) && !is_null($firstAvailabilityListItemWithoutTower)) {
                    $usedAvailabilityListItem = $firstAvailabilityListItemWithoutTower;
                }

                if (!is_null($usedAvailabilityListItem)) {
                    $currentAvailabilityListURL = route('broker-landlords.availability.download', ['contactId' => $item->contact_id, 'availabilityFilePath' => $usedAvailabilityListItem->path]);
                }
            }
        }

        if (request()->get('vt') === 'archived') {
            $notesLink = false;
        } else {
            $notesLink = true;
        }

        $teamMemberCheck = $userIsTeamLeader ? $this->getLeaderTeamMember($currentUser->id, $item->created_by) : null;
        $authorIsInCurrentUsersTeam = $userIsTeamLeader && !is_null($teamMemberCheck);
        $itemHasStatusChangeable = in_array($item->publishing_status, [
            ListingPublishingStatus::STATUS_PENDING,
            ListingPublishingStatus::STATUS_MASTERLIST,
            ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE,
            ListingPublishingStatus::STATUS_PUBLISHED,
            ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES,
        ]);
        $userCanChangePublishingStatus = $currentUser->hasPermissionTo(PermissionsDef::CAN_UPDATE_LISTING_PUBLISHING_STATUS);
        $userCanEditAllListings = $currentUser->hasPermissionTo(PermissionsDef::CAN_EDIT_ALL_LISTINGS);
        $canEditAvailabilityStatus = $currentUser->hasRole(RolesDef::OFFICE_MANAGER) || $currentUser->hasPermissionTo(PermissionsDef::CAN_EDIT_LISTING_AVAILABILITY_STATUS);
        $userCanPublishThisListing = $userCanPublishOwnListings && $item->created_by == $currentUser->id;
        return [
            'id' => $item->id,
            'ref_no' => $item->ref_no,
            // 'stared_at' => $item->propst_stared_at,
            'tags' => $item->tags,
            'stared_by' => $item->propst_user_name,
            'is_verified_landlord' => !!$item->marketing_agreement_path && !$item->is_contract_expired,
            'is_propertyfinder' => !empty($item->remote_propertyfinder_public_id),
            'reminder' => $item->reminder,
            'propertyfinder_redirect_url' => !empty($item->remote_propertyfinder_public_id) ? route('inventory.listing.navigate-to-propertyfinder', ['id' => $item->id, 'pfId' => $item->remote_propertyfinder_public_id]) : null,
            'ref_no_ui' => $this->getStatusUI($item->status, $item->available_when, $item->ref_no),
            'is_new' => intval($updatedAtDiff->format('%R%a')) > -30,
            'updated_at' => $updatedAt->format('d/m/Y'),
            'ad_type' => $item->ad_type,
            'is_exclusive' => $item->is_exclusive,
            'created_by_name' => $item->author_name,
            'author_designation' => ($item->author_position ?? ''),
            'status' => $this->getStatusUI($item->status, $item->available_when),
            'status_text' => $item->status,
            'status_css_class' => $this->getStatusCSSClass($item->status),
            'asset_id' => $item->asset_id,
            'location' => $item->location,
            'property_type' => $item->property_type,
            'bedrooms_no' => $item->bedrooms_no_next,
            'bathrooms_no' => $item->bathrooms_no_next,
            'contact_to_view' => $item->contact_to_view,
            'keys_place' => $item->keys_place,
            'property_tower' => $item->tower_name ?? '-',
            'build_up_area' => $item->build_up_area ?? '',
            'parking' => $item->parking,
            'furnishing' => $furnishingLabel,
            'balcony' => empty($item->balcony) ? 'No' : 'Yes',
            'view' => $item->property_views,
            'service_charge' => '-',
            'transfer_fee' => $item->transfer_fee,
            'commission_ll' => !empty($item->commission_ll) ? ($item->commission_ll . ($item->ad_type == 'rent' ? ' ' . Str::plural('week', $item->commission_ll) : '%')) : '-',
            'commission_buyer' => $item->commission_buyer . '%',
            'payment_method' => $item->payment_method,
            'commission_tenant' => !empty($item->commission_tenant) ? ($item->commission_tenant . ' ' . Str::plural('week', $item->commission_tenant)) : '-',
            'prorated_rata' => $item->prorated_rata,
            'title_deed' => empty($item->title_deed) ? 'No' : 'Yes',
            'bills' => $item->bills,
            'offers' => $item->offers,
            'unit_no' => $item->unit_no,
            'unit_type' => $unitType,
            'tower_no' => $item->tower_name ?? '',
            'price_on_request' => $item->price_on_request,
            'price' => number_format($item->price, 0),
            'best_price' => number_format($item->best_price, 0),
            'published' => $item->is_published,
            'brochure_link' => $brochureObject,
            'publishing_status' => $item->publishing_status,
            'auth_id' => $currentUser->id,
            'can_edit' => $userIsOfficeManager || $userIsMasterBroker || $currentUser->id == $item->created_by || $currentUser->id == $item->reffered_by || $currentUser->hasRole(RolesDef::PHOTOGRAPHER) || $authorIsInCurrentUsersTeam || $userCanEditAllListings,
            'can_duplicate' => $userIsOfficeManager || $userIsMasterBroker || $currentUser->id == $item->created_by || $userCanEditAllListings,
            'can_change_status' => $itemHasStatusChangeable && ($userIsOfficeManager || $userIsMasterBroker || $authorIsInCurrentUsersTeam || $userCanChangePublishingStatus || $userCanPublishThisListing),
            'can_change_availability_status' => $canEditAvailabilityStatus,
            'can_star' => isSerban(),
            'highlighted_row' => $item->publishing_status == ListingPublishingStatus::STATUS_PENDING && ($userIsOfficeManager || $userIsMasterBroker  || $userCanEditAllListings),
            'is_deleted' => !empty($item->property_deleted_at),
            'payment_closing_requiring_documents' => $item->payment_closing_requiring_documents,
            'web_link' => $webLink,
            'personal_web_link' => $personalWebLink,
            'notes_link' => $notesLink,
            'landlord_link' => $item->contact_id,
            'landlord' => $item->name,
            'title' => $item->title,
            'address' => $item->address,
            'can_update_their_listing' => $currentUser->id == $item->reffered_by || $item->publishing_status == 'published' && ($userIsOfficeManager || ($item->created_by == $currentUser->id)),
            'currentAvailabilityListURL' => $currentAvailabilityListURL,
            'landlord_created_by_name' => $item->landlord_created_by_name,
            'is_duplicated_for_deal' => $item->is_duplicated_for_deal ? true : false,
        ];
    }

    private function getStatusUI($status, $availableWhen, $label = null)
    {
        $statusUIOptions = array_filter($this->listingStatusOptions, function ($item) use ($status) {
            return $item['value'] === $status;
        });
        if (is_array($statusUIOptions) && count($statusUIOptions) > 0) {
            $neededData = array_values($statusUIOptions)[0];
            $statusClass = $this->mapStatusToBadgeClass($neededData['value']);
            $cssClasses = 'text-nowrap font-small badge p-2 ps-2 pe-2 ' . $statusClass;
            $markup = '<span class="' . $cssClasses . '"';

            if (!is_null($label)) {
                if ($neededData['value'] === 'to-be-available') {
                    $markup .= 'data-bs-toggle="tooltip" data-bs-placement="left" title="' . $neededData['label'] . " " . $availableWhen . '"';
                } else {
                    $markup .= 'data-bs-toggle="tooltip" data-bs-placement="left" title="' . $neededData['label'] . '"';
                }
            } else {
                if ($neededData['value'] === 'to-be-available') {
                    $markup .= 'data-bs-toggle="tooltip" data-bs-placement="left" title="' . $availableWhen . '"';
                }
            }

            $markup .= '>';
            $markup .= empty($label) ? $neededData['label'] : $label;
            $markup .= '</span>';

            return $markup;
        } else {
            return $label;
        }

        return null;
    }

    public function getStatusCSSClass($status)
    {
        $statusClass = $this->mapStatusToBadgeClass($status);
        return $statusClass;
    }

    private function mapStatusToBadgeClass($statusKey)
    {
        switch ($statusKey) {
            case 'available':
                return 'bg-teal-400'; // light green
                break;
            case 'occupied':
                return 'text-white bg-orange-500'; // orange
                break;
            case 'not-available':
                return 'text-white bg-secondary'; // gray
                break;
            case 'booked-by-fg':
            case 'rented':
            case 'sold':
                return 'text-white bg-danger'; // red
                break;
            case 'to-be-available':
                return 'bg-warning'; // yellow
                break;
        }
    }

    public function listingImages($assetId)
    {
        $property = Property::withTrashed()->where('asset_id', '=', $assetId)->first();
        if (!is_null($property)) {
            return $property->images;
        } else {
            return [];
        }
        //        $attachments = $this->attachmentsService->getObjectAttachments($property);
        //        return $attachments->map(function ($item) use ($property) {
        //            return [
        //                'url' => '/ic/crm-list/'.$item->name
        ////                'url' => 'image_url'//Image::url('/images_cache/' . $property->id . '/' . $item->name, 300, 300, array('crop'))
        //            ];
        //        })->toArray();
    }

    public function listingOperationHistory($assetId)
    {
        $property = Property::with(['operationHistory' => fn ($qb) => $qb->orderBy('id', 'DESC'), 'operationHistory.author'])->where('asset_id', '=', $assetId)->first();
        if (!is_null($property)) {
            return $property->operationHistory->map(function ($row) {
                $createdBy = "Unknown user";
                if (!is_null($row->author)) {
                    $createdBy = $row->author->name;
                }
                return [
                    'created_by' => $createdBy,
                    'created_at' => $row->created_at->format('Y-m-d H:i:s'),
                    'content' => $row->content
                ];
            });
        } else {
            return [];
        }
    }


    public function handleDeleteRequest($id)
    {
        $validData = request()->validate([
            'delete-reason' => 'required'
        ]);

        $property = Property::findOrFail($id);
        $this->operationHistoryService->addOperationHistory($property, 'Request for delete sent. Reason: ' . $validData['delete-reason'], auth()->user());
        $this->emailService->sendRequestForDelete($property, $validData['delete-reason'], auth()->user());

        return redirect()->route($this->routes['edit'], ['id' => $property->asset_id])->with('message.success', $this->messages['request-delete.success']);
    }

    public function downloadDocument($id, $attachmentId)
    {
        $attachment = Attachment::findOrFail($attachmentId);
        return Storage::download('listing-documents/' . $id . '/' . $attachment->name);
    }

    private function getDbDefinition()
    {
        return AssetDefinition::with(['attributeGroups' => function ($query) {
            $query->where('visible', '=', 1);
        }])->find(1);
    }

    private function resetCaches()
    {
        foreach (['recentProperties', 'recentPropertiesMetadata'] as $item) {
            Cache::forget($item);
        }
    }

    public function downloadBrochure($id)
    {
        $property = Property::where('id', $id)->firstOrFail();
        if ($property->brochure_path) {
            return Storage::download($property->brochure_path, $property->brochure_title);
        }
        return response();
    }

    public function downloadInfo($id)
    {
        $property = Property::where('id', $id)->firstOrFail();
        if ($property->info_path) {
            return Storage::download($property->info_path, $property->info_title);
        }
        return response();
    }

    public function downloadBooking($id)
    {
        $property = Property::where('id', $id)->firstOrFail();
        if ($property->booking_path) {
            return Storage::download($property->booking_path, $property->booking_title);
        }
        return response();
    }

    public function downloadLayout($id)
    {
        $property = Property::where('id', $id)->firstOrFail();
        if ($property->layout_path) {
            return Storage::download($property->layout_path, $property->layout_title);
        }
        return response();
    }

    public function storeImage()
    {
        $validData = request()->validate([
            'name' => 'required',
            'type' => 'required',
            'base64' => 'required'
        ]);

        try {
            $imageName = $this->attachmentsService->storeImage($validData);
            return ['uploadedName' => $imageName];
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
            return response($Ex->getMessage(), 500);
        }
    }

    // upload image from mobile app
    public function uploadImage($id)
    {
        $validData = request()->validate([
            'file' => 'image',
        ]);

        $file = $validData['file'];
        $fileContent = $file->getContent();
        $fileName = $file->getClientOriginalName();

        try {
            $dbRecord = Property::where('id', '=', $id)
                ->withTrashed()
                ->firstOrFail();

            $writtenImageName = $this->attachmentsService->storeImage([
                'name' => $fileName,
                'base64' => $fileContent
            ]);

            $images = $dbRecord->images;
            if (!is_array($images)) {
                $images = json_decode($images);
            }

            $images[] = $writtenImageName;
            $dbRecord->images = $images;
            $dbRecord->save();

            $this->operationHistoryService->addOperationHistory($dbRecord, 'Image uploaded from mobile app', auth()->user());

            return ['uploadedName' => $writtenImageName];
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
            return response($Ex->getMessage(), 500);
        }
    }

    // delete image from mobile app
    public function deleteImageFromMobileApp($id, $imageName)
    {
        Log::info("Deleting image from mobile app: " . $imageName);
        $item = Property::where('id', '=', $id)
            ->withTrashed()
            ->firstOrFail();

        $item->images = array_values(array_filter($item->images, function ($arrImageName) use ($imageName) {
            return $arrImageName !== $imageName;
        }));

        $imageDeleted = $this->attachmentsService->removeListingImage($imageName);

        if ($imageDeleted) {
            $item->save();
            $this->operationHistoryService->addOperationHistory($item, 'Image deleted from mobile app', auth()->user());
            return response(['message' => 'Ok'], 200);
        }

        return response(['message' => 'Nothing to do'], 200);
    }

    private function handlePropertySnapshot($listing)
    {
        $user = auth()->user();
        if ($user->hasAnyRole(RolesDef::OFFICE_MANAGER) && $listing->publishing_status == ListingPublishingStatus::STATUS_PUBLISHED) {
            $snapshot = $this->inventoryService->snapshot($listing);
            foreach (['qar', 'eur', 'gbp', 'usd'] as $currency) {
                foreach (['', 'ar', 'en'] as $locale) {
                    Cache::forget(CacheKeys::HOMEPAGE_RECENT_PROPERTIES_MAP . "_" . $currency . "_" . $locale);
                    Cache::forget(CacheKeys::SNAPSHOT_PART . $snapshot->listing_id . "_" . $locale . "_" . $currency);
                }
            }
        }
    }

    function getLeaderTeamMember($teamLeaderId, $userId)
    {
        return User::where(['team_leader_id' => $teamLeaderId, 'id' => $userId])->first();
    }


    public function updatePublishingStatus($id)
    {
        $validData = request()->validate([
            'status' => 'required',
            'operationHistory' => ''
        ]);

        $authUser = auth()->user();
        $item = $this->getDbItem($id);

        if (is_null($item) || $item->publishing_status == $validData['status']) {
            Log::info("CHANGE PUBLISHING STATUS - SAME STATUS [" . $authUser->name . "]");
            return response("Nothing to do");
        }

        $userIsTeamLeader = $authUser->hasRole(RolesDef::TEAM_LEADER);
        $userIsAuthor = $item->created_by == $authUser->id;
        $userIsAdmin = $authUser->hasRole(RolesDef::OFFICE_MANAGER);
        $userCanPublishOwnListings = $authUser->hasPermissionTo(PermissionsDef::CAN_PUBLISH_OWN_LISTINGS);
        $userCanPublishThisListing = $userCanPublishOwnListings && $userIsAuthor;
        $userCanChangePublishingStatus = $authUser->hasPermissionTo(PermissionsDef::CAN_UPDATE_LISTING_PUBLISHING_STATUS) || $userCanPublishThisListing;

        // Check if author is in current user's team using the getLeaderTeamMember function
        $teamMemberCheck = $userIsTeamLeader ? $this->getLeaderTeamMember($authUser->id, $item->created_by) : null;
        $authorIsInCurrentUsersTeam = $userIsTeamLeader && !is_null($teamMemberCheck);

        if (!$userIsAdmin && !$authorIsInCurrentUsersTeam && !$userCanChangePublishingStatus) {
            Log::info("CHANGE PUBLISHING STATUS - NOT ENOUGH PERMISSION [" . $authUser->name . "]");
            return response(null, 403);
        }

        $destinationEmail = "";
        if (!is_null($item->author)) {
            $destinationEmail = $item->author->email;
        }

        $item->publishing_status = $validData['status'];
        $mailSent = false;

        $this->operationHistoryService->addOperationHistory(
            $item,
            'Publishing status updated to [' . $validData['status'] . ']',
            $authUser
        );

        if (!empty($validData['operationHistory'])) {
            $this->operationHistoryService->addOperationHistory($item, $validData['operationHistory'], $authUser);

            if (!!$destinationEmail) {
                if (str_contains($validData['operationHistory'], 'change')) {
                    $this->emailService->sendPublishingStatusChangedEmail(array_merge($validData, [
                        'ref_no' => $item->ref_no,
                        'asset_id' => $item->asset_id,
                        'authenticated_user' => $authUser,
                        'item' => $item
                    ]), $destinationEmail, true);

                    $mailSent = true;
                }
            }

            $this->emailService->sendEmailOnAddingOperationHistory($item, $authUser, $validData['operationHistory']);
        }

        if (!$mailSent && $destinationEmail) {
            $this->emailService->sendPublishingStatusChangedEmail(array_merge($validData, [
                'ref_no' => $item->ref_no,
                'asset_id' => $item->asset_id,
                'authenticated_user' => $authUser,
                'item' => $item
            ]), $destinationEmail);
        }

        $item->save();

        if ($validData['status'] == ListingPublishingStatus::STATUS_PUBLISHED) {
            $theSnapshot = $this->inventoryService->snapshot($item);


            if (is_null($item->landlord_email_sent_at)) {
                $destinationEmail = $item->contact->email_1 ? $item->contact->email_1 : $item->contact->email_2;
                if (!empty($destinationEmail)) {
                    $theSnapshot->load('author');
                    $this->emailService->sendLandlordPublishedListingEmail($theSnapshot, $destinationEmail);
                    $item->landlord_email_sent_at = new \DateTime();
                    $item->save();
                }
            }

            foreach (['eur', 'qar', 'usd', 'gbp'] as $currency) {
                foreach (['', 'en', 'ar'] as $locale) {
                    Cache::forget(CacheKeys::HOMEPAGE_RECENT_PROPERTIES_MAP . "_" . $currency . "_" . $locale);
                    Cache::forget(CacheKeys::SNAPSHOT_PART . $theSnapshot->listing_id . "_" . $locale . "_" . $currency);
                }
            }
        }

        if ($validData['status'] == ListingPublishingStatus::STATUS_MASTERLIST) {
            $this->inventoryService->takeDownSnapshot($item);
        }

        return response(null, 200);
    }

    public function updateStaredStatus($listingId)
    {
        try {
            $staredStatusRow = PropertyStared::with(['listing', 'user'])->firstWhere('listing_id', $listingId);
            if (is_null($staredStatusRow)) {
                $statusRow = PropertyStared::create([
                    'listing_id' => $listingId,
                    'user_id' => auth()->user()->id
                ]);
                return ['is_stared' => true, 'user' => $statusRow->user->name, 'staredAt' => $statusRow->created_at->format('d.m.Y H:i')] ;
            } else {
                $staredStatusRow->forceDelete();
                return ['is_stared' => false];
            }
        } catch (\Exception $Ex) {
            return response(['message' => $Ex->getMessage()], 500);
        }
        if (is_null($staredStatusRow)) {
        } else {
        }
        return [
            'is_stared' => false
        ];
    }


    // public function pfIntegration($id) {
    //     $listing = Property::where('asset_id', $id)->first();
    //     if(!is_null($listing)) {
    //         $snapshot = PropertySnapshot::where('listing_id', $listing->id)->first();
    //         if(!is_null($snapshot)) {
    //             $attributes = Attribute::where('asset_id', $snapshot->asset_id)->get();
    //             PropertySnapshotAttribute::where('asset_id', $snapshot->asset_id)->forceDelete();
    //             foreach ($attributes as $attribute) {
    //                 $attributeData = $attribute->toArray();
    //                 unset($attributeData['id']);
    //                 PropertySnapshotAttribute::create($attributeData);
    //             }
    //             $remoteData = $this->propertyFinderService->syncRemoteData($snapshot, $attributes);
    //             if(!is_null($remoteData)) {
    //                 if(empty($snapshot->remote_propertyfinder_id)) {
    //                     if(isset($remoteData['property']) && isset($remoteData['property']['id'])) {
    //                         $snapshot->remote_propertyfinder_id = $remoteData['property']['id'];
    //                         $this->operationHistoryService->addForSnapshot($snapshot, "Data created in PROPERTYFINDER WITH ID: ".$remoteData['property']['id'], auth()->user());
    //                     }
    //                 } else {
    //                     $this->operationHistoryService->addForSnapshot($snapshot, "Data updated in PROPERTYFINDER", auth()->user());
    //                 }
    //             }
    //             dd($snapshot);
    //         } else {
    //             die('No snapshot for this item');
    //         }
    //     } else {
    //         die("No listing found");
    //     }
    // }

    public function deleteImage($id, $imageName)
    {
        $item = $this->getDbItem($id);
        if (!is_null($item)) {
            $item->images = array_values(array_filter($item->images, function ($arrImageName) use ($imageName) {
                return $arrImageName !== $imageName;
            }));
            $imageDeleted = $this->attachmentsService->removeListingImage($imageName);
            if ($imageDeleted) {
                if ($item->publishing_status === ListingPublishingStatus::STATUS_PUBLISHED) {
                    $item->publishing_status = ListingPublishingStatus::STATUS_PENDING;
                }

                $item->save();
                return response('OK', 200);
            }
        }

        return response('Nothing to do', 200);
    }

    public function addOperationHistory($id)
    {
        $validData = request()->validate([
            'operation-history' => 'required'
        ]);

        $property = Property::findOrFail($id);
        $this->operationHistoryService->addOperationHistory($property, $validData['operation-history'], auth()->user());

        if (auth()->user()->id !== $property->created_by) {
            $this->emailService->sendEmailOnAddingOperationHistory($property, auth()->user(), $validData['operation-history']);
        }

        return redirect()->route($this->routes['edit'], ['id' => $property->asset_id])->with('message.success', $this->messages['operation-history-added.success']);
    }

    private function inventoryDataChangeSendEmail($listing, $validFields, $dbItem)
    {
        // dd($listing, $validFields, $dbItem);
        // skip the email sending
        // if (!in_array($listing->publishing_status, [
        //     ListingPublishingStatus::STATUS_MASTERLIST,
        //     ListingPublishingStatus::STATUS_PUBLISHED,
        //     ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE,
        //     ListingPublishingStatus::STATUS_PENDING,
        // ])) {
        //     Log::info("Skipped the email sending");
        //     return;
        // }

        $shouldSendDiffEmail = false;

        $currentValues = $listing->attributesToArray();
        $originalValues = $listing->getOriginal();



        $imagesChanged = count(array_diff($currentValues["images"], $originalValues["images"])) > 0 || count(array_diff($originalValues["images"], $currentValues["images"])) > 0;
        //        dd($validFields['image_names'],$currentValues["images"], $originalValues["images"], $imagesChanged);
        $geoLatChanged = (empty($currentValues['geo_lat']) && !empty($originalValues['geo_lat'])) || (!empty($currentValues['geo_lat']) && empty($originalValues['geo_lat'])) || round($currentValues['geo_lat'], 8) != $originalValues['geo_lat'];
        $geoLonChanged = (empty($currentValues['geo_lon']) && !empty($originalValues['geo_lon'])) || (!empty($currentValues['geo_lon']) && empty($originalValues['geo_lon'])) || round($currentValues['geo_lon'], 8) != $originalValues['geo_lon'];
        // dd($originalValues['geo_lat'], round($currentValues['geo_lat'], 8), $geoLatChanged, $geoLonChanged);
        $currentValues["images"] = "";
        $originalValues["images"] = "";
        $currentValues["image_alts"] = "";
        $originalValues["image_alts"] = "";

        // listing changes

        $changes = array_diff($currentValues, $originalValues);

        foreach(["created_at", "updated_at", "geo_lat", "geo_lon"] as $ignoreKey) {
            if(isset($changes[$ignoreKey])) {
                unset($changes[$ignoreKey]);
            }
        }
        if($geoLatChanged) {
            $changes['geo_lat'] = $currentValues['geo_lat'];
        }
        if($geoLonChanged) {
            $changes['geo_lon'] = $currentValues['geo_lon'];
        }
        // $changes = array_filter(array_keys($changes), function ($arrayItem) {
        //     return !in_array($arrayItem, );
        // });

        // dd($changes);

        if (count($changes) || $imagesChanged || $geoLatChanged || $geoLonChanged) {
            $shouldSendDiffEmail = true;
        }

        // Log::info("shouldSendDiffEmail " . $shouldSendDiffEmail);

        //features
        $featureChanges = $this->getPropertyFeaturesChanges($listing, $validFields);
        $representativeChanges = $this->getRepresentativeChanges($listing, $validFields);

        if (count(array_keys($featureChanges)) > 0 || count(array_keys($representativeChanges))) {
            $shouldSendDiffEmail = true;
        }

        // landlord
        if (isset($changes['contact_id'])) {
            $originalValues['contact_id'] = Contact::find($originalValues['contact_id'])->label();
            $changes['contact_id'] = Contact::find($changes['contact_id'])->label();
        }

        if (isset($changes['property_type_id'])) {
            $originalValues['property_type_id'] = PropertyType::find($originalValues['property_type_id'])->label;
            $changes['property_type_id'] = PropertyType::find($changes['property_type_id'])->label;
        }

        if (isset($changes['location_id'])) {
            $originalValues['location_id'] = Geography::find($originalValues['location_id'])->path();
            $changes['location_id'] = Geography::find($changes['location_id'])->path();
        }

        if (isset($changes['tower_id'])) {
            $originalTower = Tower::find($originalValues['tower_id']);
            $newTower = Tower::find($changes['tower_id']);
            $originalValues['tower_id'] = is_null($originalTower) ? 'No tower' : $originalTower->name;
            $changes['tower_id'] = is_null($newTower) ? 'No tower' : $newTower->name;
        }

        if (isset($changes['reffered_by'])) {
            $originalUser = User::find($originalValues['reffered_by']);
            $originalValues['reffered_by'] = is_null($originalUser) ? 'No Reffered' : $originalUser->name;
            $changes['reffered_by'] = User::find($changes['reffered_by'])->name;
        }
        // dd($changes/*, $currentValues, $originalValues*/, $shouldSendDiffEmail, $featureChanges, $representativeChanges);

        if (
            $shouldSendDiffEmail
            && auth()->user()->id == $listing->created_by
            && !auth()->user()->hasAnyRole(RolesDef::OFFICE_MANAGER)
        ) {
            $allChanges = [
                'listing' => [
                    'current' => $originalValues,
                    'new' => $changes
                ],
                'features' => $featureChanges,
                'representative' => $representativeChanges,
            ];

            // skip send this email - modified because the content is not ok
            // if (env('APP_ENV') == 'production') {
                $this->emailService->sendEditChangesEmail($listing, $allChanges, auth()->user());
            // }
        }
        if (!auth()->user()->hasAnyRole(RolesDef::OFFICE_MANAGER,  RolesDef::PHOTOGRAPHER)) {
            $statusPending = ListingPublishingStatus::STATUS_PENDING;
            if ($listing->publishing_status != $statusPending) {
                $listing->publishing_status = $statusPending;
                $this->operationHistoryService->addOperationHistory($listing, 'Publishing status updated to [' . $statusPending . ']', auth()->user());
            }
        }
    }

    private function getPropertyFeaturesChanges($listing, $validFields)
    {
        $changes = [];
        $featureFieldsKeys = array_filter(array_keys($validFields), function ($item) {
            return Str::startsWith($item, 'property-features-');
        });

        $dbValues = Attribute::with('definition')->where('asset_id', $listing->asset_id)->get();

        foreach ($dbValues as $dbValue) {
            $key = $dbValue->definition->name;
            if (in_array($key, $featureFieldsKeys) && $validFields[$key] != $dbValue->value) {
                $changes[$key] = [
                    'current' => $dbValue->value,
                    'new' => $validFields[$key]
                ];
            }

            if (in_array($key, ['description'/*, 'Property-Highlights', 'Property-Details'*/]) && request()->input("property-features-{$key}") != $dbValue->value_large) {
                $inputStr = request()->input("property-features-{$key}");
                $inputStr = str_replace(["&nbsp;", "\r\n", "\n"], ["", "", "", ""], $inputStr);
                $dbInputStr = str_replace(["&nbsp;", "\r\n", "\n"], ["", "", "", ""], $dbValue->value_large);
                similar_text($dbValue->value_large, $inputStr, $similarPercent);

                if(strcmp($dbValue->value_large, $inputStr) != 0) {
                    $diff = new \cogpowered\FineDiff\Diff;
                    $differences = $diff->render($dbInputStr, $inputStr);
                    $changes[$key] = [
                        'current' => $dbValue->value_large,
                        'new' => $inputStr,
                        'diff' => $differences
                    ];
                }
            }
        }

        return $changes;
    }

    private function getRepresentativeChanges($listing, $validFields)
    {
        $representative = $listing->representative;
        if (is_null($representative)) {
            return;
        }
        $representativeFieldKeys = array_filter(array_keys($validFields), function ($item) {
            return Str::startsWith($item, 'representative-');
        });

        $changes = [];

        foreach ($representativeFieldKeys as $usedKey) {
            $shortKey = str_replace("representative-", "", $usedKey);
            if ($validFields[$usedKey] != $representative->$shortKey) {

                if ($shortKey == 'nationality_id') {
                    $representativeCurrentNationality = Nationality::find($representative->$shortKey);
                    $newNationality = Nationality::find($validFields[$usedKey]);
                    if (!is_null($newNationality)) {
                        $changes[$shortKey] = [
                            'current' => null,
                            'new' => $newNationality->name
                        ];
                    }
                    if (!is_null($representativeCurrentNationality)) {
                        $changes[$shortKey] = [
                            'current' => $representativeCurrentNationality->name
                        ];
                    }
                } else {
                    $changes[$shortKey] = [
                        'current' => $representative->$shortKey,
                        'new' => $validFields[$usedKey]
                    ];
                }
            }
        }

        return $changes;
    }

    public function delete($id)
    {
        $canDirectlyDelete = auth()->user()->can(PermissionsDef::INVENTORY_DELETE);

        if ($canDirectlyDelete) {
            $property = Property::where('asset_id', '=', $id)->first();
            $refNo = $property->ref_no;
            $propertyId = $property->id;
            $property->delete();
            Log::info("The snapshot {$refNo} has been deleted");

            $snapshot = PropertySnapshot::where('listing_id', $propertyId)->first();
            if (!is_null($snapshot)) {
                $refNo = $snapshot->ref_no;
                $snapshot->delete();
                Log::info("The snapshot {$refNo} has been deleted");
            }
            return redirect()->route($this->routes['index'])->with('message.success', $this->messages['deleted.success']);
        }
    }

    public function createOperationHistory($id)
    {
        $validData = request()->validate([
            'operationHistory' => 'required'
        ]);
        try {
            $property = Property::where('asset_id', $id)->first();
            $this->operationHistoryService->addOperationHistory($property, $validData['operationHistory'], auth()->user());
            if (auth()->user()->id !== $property->created_by) {
                $this->emailService->sendEmailOnAddingOperationHistory($property, auth()->user(), $validData['operationHistory']);
            }
            return response(['message' => 'ok'], 200);
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
            return response($Ex->getMessage(), 500);
        }
    }

    public function impressionsStats($listingId)
    {
        $listingImpressions = $this->propertyStatsService->getImpressionsStatsForListing($listingId);
        return $listingImpressions;
    }

    /**
     * Core business logic for updating a listing's availability status
     *
     * @param Property $item The property item to update
     * @param string $status The new availability status
     * @param string|null $operationHistoryMessage Optional message for operation history
     * @param User|null $user User performing the action (defaults to authenticated user)
     * @return Property The updated property
     */
    private function updateItemAvailabilityStatus($item, $status, $operationHistoryMessage = null, $user = null)
    {
        $user = $user ?: auth()->user();

        // Check if we need to take down the snapshot
        if (in_array($status, ['rented', 'sold', 'to-be-available']) && $item->publishing_status == ListingPublishingStatus::STATUS_PUBLISHED) {
            $this->inventoryService->takeDownSnapshot($item);
            $item->publishing_status = ListingPublishingStatus::STATUS_MASTERLIST;
        }

        // Update the status
        $item->status = $status;
        $item->save();

        // Add operation history if a message was provided
        if ($operationHistoryMessage) {
            $this->operationHistoryService->addOperationHistory(
                $item,
                $operationHistoryMessage,
                $user
            );
        }

        return $item;
    }

    /**
     * Update availability status for a single listing
     *
     * @param string $assetId The asset ID of the listing
     * @return \Illuminate\Http\Response
     */
    public function updateAvailabilityStatus($assetId)
    {
        $validData = request()->validate([
            'availabilityStatus' => 'required'
        ]);

        $item = $this->getDbItem($assetId);
        $this->updateItemAvailabilityStatus($item, $validData['availabilityStatus']);

        return response([]);
    }

    public function handlePropertyFinderNavigation($assetId, $pfId)
    {
        return $this->propertyFinderService->tryToNavigateToPFURL($pfId);
    }

    public function getGeographies($countryId)
    {
        $cities = Geography::where('country_id', $countryId)->where('type', 'city')->get();
        $regions = Geography::where('country_id', $countryId)->where('type', 'region')->get();
        $areas = Geography::where('country_id', $countryId)->where('type', 'area')->get();

        $geographyMap = [];
        $locations = [];

        foreach ($cities as $city) {
            $geographyMap[$city->id] = [
                'value' => $city->id,
                'label' => $city->name,
                'regions' => []
            ];
        }

        foreach ($regions as $region) {
            $regionEntry = [
                'value' => $region->id,
                'label' => $region->name,
                'areas' => []
            ];

            if (isset($geographyMap[$region->parent_id])) {
                $geographyMap[$region->parent_id]['regions'][] = $regionEntry;
            }
        }

        foreach ($areas as $area) {
            $regionId = $area->parent_id;
            foreach ($geographyMap as &$city) {
                foreach ($city['regions'] as &$region) {
                    if ($region['value'] == $regionId) {
                        $region['areas'][] = [
                            'value' => $area->id,
                            'label' => $area->name
                        ];
                    }
                }
            }
        }

        foreach ($geographyMap as $cityId => $city) {
            $locations[] = [
                'value' => $city['value'],
                'label' => $city['label']
            ];

            foreach ($city['regions'] as $region) {
                $locations[] = [
                    'value' => $region['value'],
                    'label' => "{$city['label']} - {$region['label']}"
                ];

                foreach ($region['areas'] as $area) {
                    $locations[] = [
                        'value' => $area['value'],
                        'label' => "{$city['label']} - {$region['label']} - {$area['label']}"
                    ];
                }
            }
        }

        return response()->json($locations);
    }


    public function index()
    {
        $this->handleExtraChecks();
        $isAjaxRequest = request()->ajax();
        if ($isAjaxRequest) {
            return $this->ajaxRequestIndex();
        }
        if (!auth()->user()->hasPermissionTo(PermissionsDef::INVENTORY_MASTER_READ) && auth()->user()->hasPermissionTo(PermissionsDef::SHORT_STAY_USER)) {
            return redirect(route('short-stay.index'));
        };
        return view($this->views['index'], $this->viewVars('index'));
    }

    public function searchByRefNo() {
        $query = request()->query('q');
        $term = "%".$query."%";
        $listings = Property::where('ref_no', 'LIKE', $term)->orWhere('title', 'LIKE', $term)->limit(20)->select(['id', 'ref_no', 'title'])->get();
        return $listings;
    }

    public function deletePropertyfinderRef($assetId) {
        $inventorySnapshot = PropertySnapshot::where('asset_id', $assetId)->first();
        $inventory = Property::where('asset_id', $assetId)->first();
        $isSuccess = false;
        if(!is_null($inventory) && !is_null($inventorySnapshot) && !empty($inventorySnapshot->remote_propertyfinder_id)) {
            $deletionResponse = $this->propertyFinderService->deleteListing($inventorySnapshot->remote_propertyfinder_id);
            if($deletionResponse == true) {
                $inventorySnapshot->remote_propertyfinder_id = null;
                $inventorySnapshot->save();
                $this->operationHistoryService->addOperationHistory($inventory, "PROPERTYFINDER reference has been deleted");
                $isSuccess = true;
            }
        }

        return ['success' => $isSuccess, 'snapshotRefNo' => $inventorySnapshot ? $inventorySnapshot->ref_no : null ];
    }

    public function updateListingFromTable($id) {
        $today = date('Y-m-d H:i:s');
        $userId = auth()->user()->id;
        $snapshot = PropertySnapshot::where('asset_id', $id)->first();
        $property = Property::where('asset_id', $id)->first();

        $snapshot->updated_at = $today;
        $snapshot->updated_by = $userId;
        $property->updated_at = $today;
        $property->updated_by = $userId;

        $snapshot->save();
        $property->save();

        $this->operationHistoryService->addOperationHistory($property, "The updated date has been updated for listing snapshot", auth()->user());

        return ['message' => 'ok'];
    }

    public function batchUpdateListings()
    {
        $validData = request()->validate([
            'listingIds' => 'array'
        ]);

        if (is_array($validData['listingIds']) && count($validData['listingIds'])) {
            $listings = Property::whereIn('id', $validData['listingIds'])->get();
            $snapshots = PropertySnapshot::whereIn('listing_id', $validData['listingIds'])->get();

            $listings->each(function ($listing) {
                $listing->updated_at = date('Y-m-d H:i:s');
                $listing->updated_by = auth()->user()->id;
                $listing->save();

                $this->operationHistoryService->addOperationHistory($listing, "The updated date has been updated for listing snapshot", auth()->user());
            });

            $snapshots->each(function ($snapshot) {
                $snapshot->updated_at = date('Y-m-d H:i:s');
                $snapshot->updated_by = auth()->user()->id;
                $snapshot->save();
            });

            return ['msg' => 'ok'];
        }
    }

    private function deleteListings($listingIds)
    {
        $listings = Property::whereIn('id', $listingIds)->get();
        $listings->each(function ($property) {
            $property->delete();
        });

        $snapshots = PropertySnapshot::whereIn('listing_id', $listingIds)->get();
        $snapshots->each(function ($snapshot) {
            $snapshot->delete();
        });

        $this->propertiesService->generateSnapshotsStats();
    }

    public function batchDeleteListings()
    {
        $canDirectlyDelete = auth()->user()->can(PermissionsDef::INVENTORY_DELETE);

        if (!$canDirectlyDelete) {
            return  response([], 403);
        }

        $validData = request()->validate([
            'listingIds' => 'array'
        ]);

        if (is_array($validData['listingIds']) && count($validData['listingIds'])) {
            $this->deleteListings($validData['listingIds']);
            return ['msg' => 'ok'];
        }
    }

    private function updatePublishingStatusForItem($item, $newStatus, $operationHistory)
    {
        if ($item->publishing_status == $newStatus) {
            return false;
        }

        $destinationEmail = "";
        $authUser = auth()->user();

        if (!is_null($item->author)) {
            $destinationEmail = $item->author->email;
        }
        $previousStatus = $item->publishing_status;
        $item->publishing_status = $newStatus;
        $mailSent = false;

        $this->operationHistoryService->addOperationHistory(
            $item,
            'Publishing status updated to [' . $newStatus . ']',
            $authUser
        );

        if (!empty($operationHistory)) {
            $this->operationHistoryService->addOperationHistory($item, $operationHistory, $authUser);

            if (!!$destinationEmail) {
                if (str_contains($operationHistory, 'change')) {
                    $this->emailService->sendPublishingStatusChangedEmail(array_merge([
                        'status' => $newStatus,
                        'operationHistory' => $operationHistory
                    ], [
                        'ref_no' => $item->ref_no,
                        'asset_id' => $item->asset_id,
                        'authenticated_user' => $authUser,
                        'item' => $item
                    ]), $destinationEmail, true);

                    $mailSent = true;
                }
            }

            $this->emailService->sendEmailOnAddingOperationHistory($item, $authUser, $operationHistory);
        }

        if (!$mailSent && $destinationEmail) {
            $this->emailService->sendPublishingStatusChangedEmail(array_merge([
                'status' => $newStatus,
                'operationHistory' => $operationHistory
            ], [
                'ref_no' => $item->ref_no,
                'asset_id' => $item->asset_id,
                'authenticated_user' => $authUser,
                'item' => $item
            ]), $destinationEmail);
        }

        $item->save();

        if ($newStatus == ListingPublishingStatus::STATUS_PUBLISHED) {
            $theSnapshot = $this->inventoryService->snapshot($item);

            foreach (['eur', 'qar', 'usd', 'gbp'] as $currency) {
                foreach (['', 'en', 'ar'] as $locale) {
                    Cache::forget(CacheKeys::HOMEPAGE_RECENT_PROPERTIES_MAP . "_" . $currency . "_" . $locale);
                    Cache::forget(CacheKeys::SNAPSHOT_PART . $theSnapshot->listing_id . "_" . $locale . "_" . $currency);
                }
            }
        }

        if ($previousStatus == ListingPublishingStatus::STATUS_PUBLISHED) {
            $this->inventoryService->takeDownSnapshot($item);
        }

        return true;
    }

    public function batchUpdatePublishingStatus()
    {
        $authUser = auth()->user();
        $userIsAdmin = $authUser->hasRole(RolesDef::OFFICE_MANAGER);

        if (!$userIsAdmin) {
            return response([], 403);
        }

        $validData = request()->validate([
            'listingIds' => 'array',
            'operationHistory' => '',
            'status' => 'required'
        ]);

        $listings = Property::whereIn('id', $validData['listingIds'])->get();
        $updatedListings = 0;
        foreach ($listings as $listing) {
            $item = $this->getDbItem($listing->asset_id);
            $itemUpdated = $this->updatePublishingStatusForItem($item, $validData['status'], $validData['operationHistory']);
            if($itemUpdated) {
                $updatedListings++;
            }
        }

        return ['msg' => 'ok', 'updatedListings' => $updatedListings];
    }

    /**
     * Update availability status for a batch of listings
     *
     * @param array $listingIds Array of listing IDs
     * @param string $status The new availability status
     * @param string $adTypeFilter Optional filter for ad_type (e.g., 'rent' or 'sale')
     * @return int Number of updated listings
     */
    private function batchUpdateAvailabilityStatus($listingIds, $status, $adTypeFilter = null)
    {
        $authUser = auth()->user();
        $updatedCount = 0;

        $listings = Property::whereIn('id', $listingIds);

        // Apply ad_type filter if provided
        if ($adTypeFilter) {
            $listings = $listings->where('ad_type', $adTypeFilter);
        }

        $listings = $listings->get();

        foreach ($listings as $listing) {
            // Use the core business logic function with operation history message
            $this->updateItemAvailabilityStatus(
                $listing,
                $status,
                "Availability status updated to [{$status}] via batch action",
                $authUser
            );

            $updatedCount++;
        }

        return $updatedCount;
    }

    /**
     * Update snapshot date for a batch of listings
     *
     * @param array $listingIds Array of listing IDs
     * @return void
     */
    private function batchUpdateSnapshotDate($listingIds)
    {
        $authUser = auth()->user();

        $listings = Property::whereIn('id', $listingIds)->get();
        $snapshots = PropertySnapshot::whereIn('listing_id', $listingIds)->get();

        $listings->each(function ($listing) use ($authUser) {
            $listing->updated_at = now();
            $listing->updated_by = $authUser->id;
            $listing->save();

            $this->operationHistoryService->addOperationHistory(
                $listing,
                "The listing was updated via batch action",
                $authUser
            );
        });

        $snapshots->each(function ($snapshot) use ($authUser) {
            $snapshot->updated_at = now();
            $snapshot->updated_by = $authUser->id;
            $snapshot->save();
        });
    }

    /**
     * Handle batch actions for inventory items
     *
     * @return \Illuminate\Http\Response
     */
    public function batchAction()
    {
        $validData = request()->validate([
            'action' => 'required|string',
            'listingIds' => 'required|array'
        ]);

        $action = $validData['action'];
        $listingIds = $validData['listingIds'];

        // Ensure we have listings to process
        if (empty($listingIds)) {
            return response(['msg' => 'No listings selected'], 400);
        }

        // Process based on action type
        switch ($action) {
            case 'mark_rented':
                // Mark rent listings as rented
                $this->batchUpdateAvailabilityStatus($listingIds, 'rented', 'rent');
                break;

            case 'mark_sold':
                // Mark sale listings as sold
                $this->batchUpdateAvailabilityStatus($listingIds, 'sold', 'sale');
                break;

            default:
                return response(['msg' => 'Invalid action'], 400);
        }

        return ['msg' => 'ok'];
    }
}
