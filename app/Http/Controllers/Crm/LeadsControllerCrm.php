<?php

namespace App\Http\Controllers\Crm;

use App\Exports\FilteredLeadsExport;
use App\Exports\LeadsExport;
use App\Models\PropertyView;
use App\Http\Controllers\CrmBaseController;
use App\Models\Admin\ExportPlatform;
use App\Models\Crm\RolesDef;
use App\Models\LeadSource;
use App\Models\LeadStatus;
use App\Services\AuthorizationService;
use App\Services\GeographyService;
use App\Services\InventoryService;
use App\Services\LeadsExportService;
use App\Services\PropertiesService;
use Illuminate\Http\Request;
use App\Models\Lead;
use App\Models\AssetDefinition;
use App\Models\CacheKeys;
use App\Models\Contact;
use App\Models\Crm\ContactsListTag;
use App\Models\Geography;
use App\Models\LeadAssignment;
use App\Models\LeadProposal;
use App\Models\Property;
use App\Models\Task;
use App\Models\User;
use App\Services\AttachmentsService;
use App\Services\BrokerLandlordsService;
use App\Services\ContactsService;
use App\Services\LeadsService;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\OperationHistoryService;
use App\Services\UserService;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use App\Services\EmailService;
use App\Services\LeadStatusService;
use App\Services\NotesService;
use DB;
use Cache;
use Illuminate\Support\Facades\Storage;

class LeadsControllerCrm extends CrmBaseController
{
    public $prices;

    protected $views = [
        'index' => 'crm.leads.index',
        'create' => 'crm.leads.form.form',
        'edit' => 'crm.leads.form.form',
    ];

    protected $routes = [
        'index' => 'crm.leads.index',
        'edit' => 'crm.leads.edit',
        'save' => 'crm.leads.save',
        'store' => 'crm.leads.store',
    ];

    protected $messages = [
        'edit.success' => 'The item was successfully saved'
    ];

    protected $validationMessages = [
        'title.required' => 'The :attribute field is required. Either select an existing contact or create a new one.',
        'operation-history.required' => 'This field is required'
    ];

    protected function getFieldsToValidate(string $actionName, $itemId = null)
    {
        return [
            'contact_id' => 'required',
            'task_id' => '',
            'requirements' => '',
            'operation-history' => '',
            'move_in_date' => '',
            'inquired_ref_no' => '',
            'leads_request' => [
                Rule::requiredIf(function () {
                    $leadsRequestObj = json_decode(request()->get('leads_request'));
                    $opType = $leadsRequestObj && isset($leadsRequestObj->ot) ? $leadsRequestObj->ot != '' : '';
                    $propType = $leadsRequestObj && isset($leadsRequestObj->t) ? $leadsRequestObj->t != '' : '';
                    return !$opType && !$propType;
                })
            ],
            'lead_status_id' => '',
            'leads_viewing_scheduled' => '',
            'assigned-to' => 'required_if:lead_status_id,==,21',
            'rating' => '',
            'documents_type' => ''
        ];
    }

    private $geographyService;
    private $propertiesService;
    private $itemFetcher;
    private $ajaxListMapper;
    private $updateValidator;
    private $afterItemUpdate;
    private $operationHistoryService;
    private $userService;
    public $leadsService;
    public $leServ;
    public $authoServ;
    private $inventoryService;
    private $brokerLandlordsService;
    private $leadStatusService;
    protected $emailService;
    protected $notesService;
    protected $attachmentsService;
    protected $contactsService;

    public function __construct(
        AuthorizationService $authoServ,
        LeadsExportService $leServ,
        Request $request,
        GeographyService $geographyService,
        PropertiesService $propertiesService,
        OperationHistoryService $operationHistoryService,
        UserService $userService,
        InventoryService $inventoryService,
        BrokerLandlordsService $brokerLandlordsService,
        LeadsService $leadsService,
        EmailService $emailService,
        NotesService $notesService,
        LeadStatusService $leadStatusService,
        AttachmentsService $attachmentsService,
        ContactsService $contactsService
    ) {
        $this->geographyService = $geographyService;
        $this->propertiesService = $propertiesService;
        $this->operationHistoryService = $operationHistoryService;
        $this->inventoryService = $inventoryService;
        $this->userService = $userService;
        $this->brokerLandlordsService = $brokerLandlordsService;
        $this->leadsService = $leadsService;
        $this->emailService = $emailService;
        $this->leServ = $leServ;
        $this->authoServ = $authoServ;
        $this->notesService = $notesService;
        $this->leadStatusService = $leadStatusService;
        $this->attachmentsService = $attachmentsService;
        $this->contactsService = $contactsService;
        $extraConfig = $leServ->getExtraConfig($request);
        $this->itemFetcher = function ($offset = 0, $limit = 10) use ($authoServ, $extraConfig) {
            $visibleItems = $authoServ->getVisibleItems(AuthorizationService::CATEG_LEADS, null, $offset, $limit, $extraConfig);
            $idsArray = array_map(function ($item) {
                return $item->id;
            }, $visibleItems['items']);

            if (count($idsArray) > 0) {
                $remarks = DB::table('leads as l')
                    ->leftJoin(DB::raw('(
                    SELECT oh1.model_id, oh1.content as last_remark
                    FROM operation_history oh1
                    INNER JOIN (
                        SELECT model_id, MAX(id) as max_id
                        FROM operation_history
                        WHERE model_type LIKE "%Lead%" AND model_id IN (' . implode(',', $idsArray) . ')
                        GROUP BY model_id
                    ) oh2 ON oh1.id = oh2.max_id
                ) as oh'), 'oh.model_id', '=', 'l.id')
                    ->whereIn('l.id', $idsArray)
                    ->get(['l.id', 'oh.last_remark']);
            } else {
                $remarks = collect([]);
            }

            $remarksMap = $remarks->keyBy('id')->map(function ($item) {
                return $item->last_remark;
            })->toArray();

            foreach ($visibleItems['items'] as &$item) {
                $item->last_remark = $remarksMap[$item->id] ?? null;
            }

            return $visibleItems;
        };

        $this->ajaxListMapper = $leServ->ajaxListMapper;
        $this->createValidator = function ($controller, $request) {
            $controller->validate(
                $request,
                ['contact_id' => 'required'],
                ['contact_id.required' => 'The :attribute field is required. Either select an existing contact or create a new one.'],
                ['contact_id' => 'Client']
            );
        };
        $this->updateValidator = function ($controller, $request) {
            $controller->validate(
                $request,
                ['contact_id' => 'required'],
                ['contact_id.required' => 'The :attribute field is required. Either select an existing contact or create a new one.'],
                ['contact_id' => 'Client']
            );
        };
        $propertyPrices = $this->propertiesService->getMinMaxPrices();
        $prices = ["min" => [], "max" => []];

        foreach ($propertyPrices as $vals) {
            foreach ($vals as $k => $arrPrices) {
                foreach ($arrPrices as $priceK => $priceLabel) {
                    $prices[$k][$priceK] = $priceLabel;
                }
            }
        };

        $this->prices = $prices;
        $this->areaValues = $this->propertiesService->areas;
        $this->updateValidator = function ($controller, $request) {
            $controller->validate(
                $request,
                ['contact_id' => 'required'],
                ['contact_id.required' => 'The :attribute field is required. Either select an existing contact or create a new one.'],
                ['contact_id' => 'Client']
            );
        };
    }

    public function index()
    {
        $prmin = $this->inventoryService->minRentPrices;
        $prmax = $this->inventoryService->maxRentPrices;
        $psmin = $this->inventoryService->maxSalePrices;
        $psmax = $this->inventoryService->maxSalePrices;

        $this->handleExtraChecks();
        $isAjaxRequest = request()->ajax();
        if ($isAjaxRequest) {
            return $this->ajaxRequestIndex();
        }

        $geography = Geography::select(['id', 'name', 'type', 'slug', 'parent_id', 'visible', 'avg_price', 'created_at', 'updated_at', 'deleted_at'])->orderBy('name', 'ASC')->get()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        foreach ($geography as $g) {
            $g->content = "";
        }

        $cities = $geography->filter(function ($item) {
            return $item->type == 'city';
        });

        $regions = $geography->filter(function ($item) {
            return $item->type == 'region';
        });

        $areas = $geography->filter(function ($item) {
            return $item->type == 'area';
        });

        $operationTypeOptions = array_merge([['value' => '', 'label' => 'All']], $this->inventoryService->operationTypeOptions);

        $propertyTypes = $this->propertiesService->getPropertyTypes(function ($item) {
            return $item->used;
        });
        $leadStatuses = $this->leadStatusService->getLeadStatuses();
        $usersWithRoles = $this->userService->getUsersWithRoles();
        $agents = $this->userService->getPossibleListingOwners();
        $isCorporateOptions = $this->brokerLandlordsService->landlordCategory;
        $sources = $this->leadsService->getCachedSources();
        $vt = request()->get('vt', 'personal');
        $isManager = auth()->user()->hasAnyRole([RolesDef::MASTER_BROKER, RolesDef::OFFICE_MANAGER]);
        $isSerban = auth()->user()->email == env('EMAIL_ADDRESS_SERBAN');

        $prmin = $this->inventoryService->minRentPrices;
        $prmax = $this->inventoryService->maxRentPrices;
        $psmin = $this->inventoryService->maxSalePrices;
        $psmax = $this->inventoryService->maxSalePrices;

        return view($this->views['index'], array_merge($this->viewVars('index'), [
            'isManager' => $isManager,
            'isSerban' => $isSerban,
            'vt' => $vt,
            'geography' => $geography,
            'cities' => $cities,
            'regions' => $regions,
            'areas' => $areas,
            'operationTypeOptions' => $operationTypeOptions,
            'propertyTypes' => $propertyTypes,
            'agents' => $agents,
            'sources' => $sources,
            'isCorporateOptions' => $isCorporateOptions,
            'leadStatuses' => $leadStatuses,
            'usersWithRoles' => $usersWithRoles,
            'prmin' => $prmin,
            'prmax' => $prmax,
            'psmin' => $psmin,
            'psmax' => $psmax,
        ]));
    }

    public function ajaxRequestIndex()
    {
        $request = request();
        $offset = empty($request->query('start')) ? 0 : $request->query('start');
        $limit = empty($request->query('length')) ? 0 : $request->query('length');
        $items = call_user_func($this->itemFetcher, $offset, $limit);
        $duplicatesContacts = $this->authoServ->getDuplicatesContactInformation($items);

        foreach ($items['items'] as &$item) {
            $filteredContacts = $duplicatesContacts->filter(function ($duplicate) use ($item) {
                return $duplicate->id != $item->id && !empty($duplicate->contact->mobile_1) && $duplicate->contact->mobile_1 == $item->contact_mobile_1;
            });

            // $filteredContacts = $duplicateContacts->reject(function ($duplicate) use ($item) {
            //     return $duplicate->id == $item->id;
            // });

            if ($filteredContacts->isNotEmpty()) {
                $item->duplicate_contact_info = $filteredContacts;
            }
        }

        if (isset($items['items'])) {
            if (is_array($items['items'])) {
                $data = array_map($this->ajaxListMapper, $items['items']);
            } else {
                $data = $items['items']->map($this->ajaxListMapper);
            }
        } else {
            $data = $items->map($this->ajaxListMapper);
        }

        $payload = ['data' => $data, 'recordsTotal' => $items['count'] ?? 0, 'recordsFiltered' => $items['count'] ?? 0];
        return response()->json($payload);
    }

    public function show($id)
    {
        $item = Lead::findOrFail($id);
        $readonly = true;
        $empty = false;
        $delete = false;
        $action = route($this->routes['store']);

        $back = route($this->routes['index']);
        $listViewType = 'master';

        $definition = AssetDefinition::find(1);
        $propertyTypes = $this->propertiesService->getPropertyTypes();
        $geoData = $this->geographyService->getAndPrepareGeographyDataForFilters();
        $cities = $geoData[0];
        $regions = $geoData[1];
        $areas = $geoData[2];
        $prices = $this->prices;
        $areaValues = $this->areaValues;
        $usersWithRoles = $this->userService->getUsersWithRoles();
        $sources = $this->leadsService->getCachedSources();
        $statusOptions = LeadStatus::all();
        $leadStatuses = $this->leadStatusService->getLeadStatuses();
        if (!is_null($item)) {
            $operationHistory = $item->operationHistory;
        }

        $valueLists = $this->inventoryService->getFilterAttributeValueLists();
        $furnishingOptions = $valueLists['property-features-furnishings'];
        $furnishingOptionsOffice = $valueLists['property-features-furnishings-office'];
        $propertyViews = PropertyView::all();
        $billsOptions = $valueLists['property-features-bills'];
        $prmin = $this->inventoryService->minRentPrices;
        $prmax = $this->inventoryService->maxRentPrices;
        $psmin = $this->inventoryService->maxSalePrices;
        $psmax = $this->inventoryService->maxSalePrices;
        $wasReassignedToCurrentUser = false;
        $listingStatusOptions = [
            ['value' => '', 'label' => 'Please select'],
            ['value' => 'available', 'label' => 'Available'],
            ['value' => 'occupied', 'label' => 'Occupied', 'onlyFor' => 'sale'],
            ['value' => 'sold', 'label' => 'Sold', 'onlyFor' => 'sale'],
            ['value' => 'to-be-available', 'label' => 'To be available', 'onlyFor' => 'rent'],
            ['value' => 'rented', 'label' => 'Rented', 'onlyFor' => 'rent'],
        ];
        $hasPublishingRoles = auth()->user()->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::MASTER_BROKER);
        $isCorporateOptions = $this->brokerLandlordsService->landlordCategory;
        $operationTypeOptions = array_merge([['value' => '', 'label' => 'All']], $this->inventoryService->operationTypeOptions);
        $leadRequest = $item->leads_request;
        $hasInventoryTable = true;

        $geography = Geography::select(['id', 'name', 'type', 'slug', 'parent_id', 'visible', 'avg_price', 'created_at', 'updated_at', 'deleted_at'])->orderBy('name', 'ASC')->get()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        foreach ($geography as $g) {
            $g->content = "";
        }

        $cities = $geography->filter(function ($item) {
            return $item->type == 'city';
        });

        $regions = $geography->filter(function ($item) {
            return $item->type == 'region';
        });

        $areas = $geography->filter(function ($item) {
            return $item->type == 'area';
        });

        // marketing platforms
        $marketingPlatforms = ExportPlatform::where('is_active', 1)->orderBy('name')->get();

        return view($this->views['edit'], compact('item', 'readonly', 'empty', 'delete', 'back', 'definition', 'cities', 'regions', 'areas', 'propertyTypes', 'prices', 'areaValues', 'action', 'usersWithRoles', 'sources', 'statusOptions', 'operationHistory', 'furnishingOptions', 'furnishingOptionsOffice', 'propertyViews', 'billsOptions', 'prmin', 'prmax', 'psmin', 'psmax', 'listingStatusOptions', 'hasPublishingRoles', 'isCorporateOptions', 'operationTypeOptions', 'listViewType', 'leadRequest', 'hasInventoryTable', 'geography', 'cities', 'regions', 'areas', 'marketingPlatforms', 'leadStatuses', 'wasReassignedToCurrentUser'));
    }

    public function edit($itemId)
    {
        $id = $itemId;
        $definition = AssetDefinition::find(1);
        $readonly = false;
        $empty = false;
        $delete = false;
        $action = route($this->routes['save'], compact('id'));
        $back = route($this->routes['index']);
        $listViewType = 'master';

        $item = Lead::with(['agentContact', 'agentContact.listing', 'statuses', 'reassignedByAgentRelation', 'operationHistory' => function ($qb) {
            return $qb->where('model_type', Lead::class)->orderBy('id', 'DESC');
        }])->findOrFail($itemId);

        $wasReassignedByCurrentUser = false;
        $wasReassignedToCurrentUser = false;

        if (!is_null($item->reassignedByAgentRelation)) {
            $wasReassignedByCurrentUser = $item->reassignedByAgentRelation->user_id == auth()->user()->id;
            $wasReassignedToCurrentUser = !$wasReassignedByCurrentUser;
        }

        $propertyTypes = $this->propertiesService->getPropertyTypes();
        $geoData = $this->geographyService->getAndPrepareGeographyDataForFilters();
        $leadRatings = $this->leadsService->getLeadRatingsList();
        $windows = $this->leadsService->getCachedLeadFollowupTaskWindows();
        $daysMap = [
            'A' => $windows['LEAD_A_FOLLOWUP_TASK_WINDOW'],
            'B' => $windows['LEAD_B_FOLLOWUP_TASK_WINDOW'],
            'C' => $windows['LEAD_C_FOLLOWUP_TASK_WINDOW'],
            'D' => $windows['LEAD_D_FOLLOWUP_TASK_WINDOW'],
            'E' => $windows['LEAD_E_FOLLOWUP_TASK_WINDOW'],
            'F' => $windows['LEAD_F_FOLLOWUP_TASK_WINDOW']
        ];
        $cities = $geoData[0];
        $regions = $geoData[1];
        $areas = $geoData[2];
        $prices = $this->prices;
        $areaValues = $this->areaValues;
        $sources = $this->leadsService->getCachedSources()->sortBy('name');
        $lastStatus = $item->lead_status_id;

        $statusOptions = LeadStatus::all();
        $leadStatuses = $this->leadStatusService->getLeadStatuses();

        $usersWithRoles = $this->userService->getUsersWithRoles();
        $operationHistory = null;
        if (!is_null($item)) {
            $operationHistory = $item->operationHistory;
        }

        $valueLists = $this->inventoryService->getFilterAttributeValueLists();
        $furnishingOptions = $valueLists['property-features-furnishings'];
        $furnishingOptionsOffice = $valueLists['property-features-furnishings-office'];
        $propertyViews = PropertyView::all();
        $billsOptions = $valueLists['property-features-bills'];
        $prmin = $this->inventoryService->minRentPrices;
        $prmax = $this->inventoryService->maxRentPrices;
        $psmin = $this->inventoryService->maxSalePrices;
        $psmax = $this->inventoryService->maxSalePrices;
        $listingStatusOptions = [
            ['value' => '', 'label' => 'Please select'],
            ['value' => 'available', 'label' => 'Available'],
            ['value' => 'occupied', 'label' => 'Occupied', 'onlyFor' => 'sale'],
            ['value' => 'sold', 'label' => 'Sold', 'onlyFor' => 'sale'],
            ['value' => 'to-be-available', 'label' => 'To be available', 'onlyFor' => 'rent'],
            ['value' => 'rented', 'label' => 'Rented', 'onlyFor' => 'rent'],
        ];
        $hasPublishingRoles = auth()->user()->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::MASTER_BROKER);
        $isCorporateOptions = $this->brokerLandlordsService->landlordCategory;
        $operationTypeOptions = array_merge([['value' => '', 'label' => 'All']], $this->inventoryService->operationTypeOptions);
        $leadRequest = $item->leads_request;
        $hasInventoryTable = true;

        $geography = Geography::select(['id', 'name', 'type', 'slug', 'parent_id', 'visible', 'avg_price', 'created_at', 'updated_at', 'deleted_at'])->orderBy('name', 'ASC')->get()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        foreach ($geography as $g) {
            $g->content = "";
        }

        $cities = $geography->filter(function ($item) {
            return $item->type == 'city';
        });

        $regions = $geography->filter(function ($item) {
            return $item->type == 'region';
        });

        $areas = $geography->filter(function ($item) {
            return $item->type == 'area';
        });

        $this->updateListingPropertiesFromRequest($item, true);
        // marketing platforms
        $marketingPlatforms = ExportPlatform::where('is_active', 1)->orderBy('name')->get();

        $questionsSpecificReq = DB::table('settings')
            ->whereIn('name', ['lead_specific_requirements_rent_q', 'lead_specific_requirements_sale_q'])
            ->get();

        $questionsForRent = $questionsSpecificReq->filter(function ($item) {
            return $item->name === 'lead_specific_requirements_rent_q';
        })->first();

        $questionsForSale = $questionsSpecificReq->filter(function ($item) {
            return $item->name === 'lead_specific_requirements_sale_q';
        })->first();

        $isEdit = isset($item) && $item->exists;

        return view($this->views['edit'], compact('item', 'readonly', 'empty', 'delete', 'action', 'back', 'definition', 'cities', 'regions', 'areas', 'propertyTypes', 'prices', 'areaValues', 'sources', 'lastStatus', 'statusOptions', 'operationHistory', 'usersWithRoles', 'furnishingOptions', 'furnishingOptionsOffice', 'propertyViews', 'billsOptions', 'prmin', 'prmax', 'psmin', 'psmax', 'listingStatusOptions', 'hasPublishingRoles', 'isCorporateOptions', 'operationTypeOptions', 'listViewType', 'leadRequest', 'hasInventoryTable', 'geography', 'cities', 'regions', 'areas', 'marketingPlatforms', 'leadStatuses', 'wasReassignedByCurrentUser', 'wasReassignedToCurrentUser', 'questionsForRent', 'questionsForSale', 'isEdit', 'leadRatings', 'daysMap'));
    }

    public function updateListingPropertiesFromRequest($item, $skipSave = false)
    {
        if (!empty($item->leads_request)) {
            $leadsRequestObj = json_decode($item->leads_request);
            if (property_exists($leadsRequestObj, 't')) {
                $filterPropertyTypeValue = $leadsRequestObj->t;
                if (is_array($leadsRequestObj->t)) {
                    $filterPropertyTypeValue = $leadsRequestObj->t[0] ?? "";
                    $item->filter_property_type = $filterPropertyTypeValue;
                    $item->lead_property_types_filter = json_encode($leadsRequestObj->t);
                } else {
                    $item->filter_property_type = $filterPropertyTypeValue;
                    $item->lead_property_types_filter = $leadsRequestObj->t;
                }
            }
            if (property_exists($leadsRequestObj, 'ot')) {
                $item->filter_operation_type = $leadsRequestObj->ot;
            }
            if (property_exists($leadsRequestObj, 'pf')) {
                $item->filter_budget_min = $leadsRequestObj->pf;
            }
            if (property_exists($leadsRequestObj, 'pt')) {
                $item->filter_budget_max = $leadsRequestObj->pt;
            }
            if (property_exists($leadsRequestObj, 'be')) {
                $filterBedroomsValue = $leadsRequestObj->be;
                if (is_array($leadsRequestObj->be)) {
                    $filterBedroomsValue = $leadsRequestObj->be[0] ?? "";
                }
                $item->filter_bedrooms = $filterBedroomsValue;
                $item->lead_bedrooms_filter = json_encode($leadsRequestObj->be);
            }
            if (property_exists($leadsRequestObj, 'ba')) {
                $item->filter_bathrooms = $leadsRequestObj->ba;
            }
            if (property_exists($leadsRequestObj, 'loc')) {
                if (is_array($leadsRequestObj->loc)) {
                    $item->location_id = $leadsRequestObj->loc[0] ?? "";
                } else {
                    $item->location_id = null;
                }
            }
            if (property_exists($leadsRequestObj, 'fu')) {
                if (is_array($leadsRequestObj->fu)) {
                    $item->filter_furnishings = $leadsRequestObj->fu[0] ?? "";
                } else {
                    $item->filter_furnishings = null;
                }
            }

            if (!$skipSave) {
                $item->save();
            }
        }
    }

    public function handleLeadAssignation($item, $requestAssignedTo)
    {
        $currentAssignments = LeadAssignment::where('lead_id', $item->id)->orderBy('created_at', 'DESC')->get();
        if (empty($requestAssignedTo)) {
            if ($currentAssignments->count()) {
                foreach ($currentAssignments as $old) {
                    $old->delete();
                    $this->operationHistoryService->addOperationHistory($item, 'The lead was unassigned from [' . $old->user->name . ']', auth()->user());
                }
            }
        } else {
            $currentAssignment = $currentAssignments->first();
            $shouldCreateAssignment = false;
            if (!is_null($currentAssignment)) {
                if ($currentAssignment->user_id != $requestAssignedTo) {
                    foreach ($currentAssignments as $old) {
                        $old->delete();
                        $this->operationHistoryService->addOperationHistory($item, 'The lead was unassigned from [' . $old->user->name . ']', auth()->user());
                    }
                    $shouldCreateAssignment = true;
                    // Log::info("Should create assignment set to true [1]");
                }
            } else {
                $shouldCreateAssignment = true;
                // Log::info("Should create assignment set to true [2]");
            }
            if ($shouldCreateAssignment) {
                // Log::info('Will create assignent');
                $userToAssignTo = User::find($requestAssignedTo);
                if (!is_null($userToAssignTo)) {
                    $assignment = new LeadAssignment();
                    $assignment->user_id = $userToAssignTo->id;
                    $assignment->lead_id = $item->id;
                    $assignment->save();
                    $this->operationHistoryService->addOperationHistory($item, 'The lead was assigned to [' . $userToAssignTo->name . ']', auth()->user());
                }
            } else {
                // Log::info('Assignment skipped');
            }
        }
        // if($currentAssignment->user_id == )
        // dd($currentAssignment);
        // dd($currentAssignments, $item, request()->all());
        // old code - commented on 18.02.2024
        // if (request()->has('lead_status_id')) {
        //     $requestStatusId = request()->get('lead_status_id');
        //     if ($requestStatusId == LeadStatus::STATUS_COLD_ID) {
        //         $oldAssignments = LeadAssignment::where('lead_id', $item->id)->get();
        //         foreach ($oldAssignments as $old) {
        //             $old->delete();
        //             $this->operationHistoryService->addOperationHistory($item, 'The lead was unassigned from [' . $old->user->name . ']', auth()->user());
        //         }
        //     } else {
        //         $requestAssignedTo = request()->input("assigned-to");
        //         $oldAssignments = LeadAssignment::where('lead_id', $item->id)->get();
        //         if (!empty($requestAssignedTo)) {
        //             $userAlreadyAssigned = false;
        //             foreach ($oldAssignments as $old) {
        //                 if ($old->user_id == $requestAssignedTo) {
        //                     $userAlreadyAssigned = true;
        //                     break;
        //                 }
        //             }
        //             if (!$userAlreadyAssigned) {
        //                 foreach ($oldAssignments as $old) {
        //                     $old->delete();
        //                     $this->operationHistoryService->addOperationHistory($item, 'The lead was unassigned from [' . $old->user->name . ']', auth()->user());
        //                 }

        //                 $userToAssignTo = User::find($requestAssignedTo);
        //                 if (!is_null($userToAssignTo)) {
        //                     $assignment = new LeadAssignment();
        //                     $assignment->user_id = $userToAssignTo->id;
        //                     $assignment->lead_id = $item->id;
        //                     $assignment->save();
        //                     $this->operationHistoryService->addOperationHistory($item, 'The lead was assigned to [' . $userToAssignTo->name . ']', auth()->user());
        //                 }
        //             }
        //         }
        //     }
        // }
    }

    public function save($itemId)
    {
        $request = request();
        if ($this->updateValidator) {
            call_user_func($this->updateValidator, $this, $request);
        }

        $item = Lead::with('leadStatus')->findOrFail($itemId);
        $oldStatusId = $item->lead_status_id;
        $oldStatusLabel = '';
        $newStatusLabel = '';
        $columns = $item->getColumns();
        foreach ($columns as $column) {
            if ($this->isFillableColumn($column)) {
                $value = $request->input($column);
                $item->$column = $value;
            }
        }
        $leadsViewingScheduledData = $request->input('leads_viewing_scheduled');

        // $scheduledDataDecode = json_decode($leadsViewingScheduledData);
        // dd($scheduledDataDecode);
        if (!is_null($leadsViewingScheduledData) && ($request->input('lead_status_id') != '14' && $request->input('lead_status_id') != '15' && $request->input('lead_status_id') != '23' && $request->input('lead_status_id') != '16')) {
            $leadsViewingScheduledData = null;
        }
        $leadStatus = LeadStatus::where('id', $request->input('lead_status_id'))->first();

        if ($request->input('lead_status_id') == '26') {
            $agent = auth()->user();
            $today = date('Y-m-d');
            $afterTreeMonths = date('Y-m-d', strtotime('+3 months', strtotime($today)));
            $reminderData = [
                'title' => 'Far moving decision',
                'text' => 'Far moving date on ' . $agent->name . ' by ' . $today,
                'priority' => 'low',
                'due_date' => $afterTreeMonths,
                'reminder_email' => false,
                'reminder_email_date' => null,
                'reminder_type' => $leadStatus->name,
            ];
            $this->notesService->createReminderForLead($item, $reminderData);
        }

        $scheduledDataDecode = json_decode($leadsViewingScheduledData);
        if (($request->input('lead_status_id') == '14' || $request->input('lead_status_id') == '15' || $request->input('lead_status_id') == '23' || $request->input('lead_status_id') == '16')) {
            // reminder
            if (isset($scheduledDataDecode->reminder)) {
                $reminderFormData = $scheduledDataDecode->reminder;
                $reminderData = [
                    'title' => $reminderFormData->title,
                    'text' => $reminderFormData->content,
                    'priority' => $reminderFormData->priority,
                    'due_date' => $reminderFormData->dueDate,
                    'reminder_email' => $reminderFormData->sendEmail,
                    'reminder_email_date' => $reminderFormData->sendReminderDate,
                    'reminder_type' => $leadStatus->name,
                ];
                $allLeadStatuses = $this->leadStatusService->getLeadStatuses();
                $newLeadStatus = $allLeadStatuses->where('id', $request->input('lead_status_id'))->first();
                $newLeadStatusLabel = '';
                if (!is_null($newLeadStatus)) {
                    $newLeadStatusLabel = $newLeadStatus->name;
                }
                $shouldSetTask = $newLeadStatusLabel == 'VIEWING_SCHEDULED' || $newLeadStatusLabel == 'MEETING_SCHEDULED';
                $taskData = null;
                if ($shouldSetTask) {
                    $taskData = [
                        'status' => $newLeadStatusLabel,
                        'listingSelection' => $scheduledDataDecode->listingSelection,
                    ];
                }
                $this->notesService->createReminderForLead($item, $reminderData, $taskData);
            }
            // proposals
            if (isset($scheduledDataDecode->listingSelection) && ($request->input('lead_status_id') == '14' || $request->input('lead_status_id') == '16')) {
                foreach ($scheduledDataDecode->listingSelection as $listingId => $listingRefNo) {
                    $listingAlreadyProposed = false;
                    foreach ($item->proposals as $proposal) {
                        if ($proposal->property->id == $listingId) {
                            $listingAlreadyProposed = true;
                        }
                    }
                    if (!$listingAlreadyProposed) {
                        $newProposal = new LeadProposal();
                        $newProposal->lead_id = $item->id;
                        $newProposal->property_id = $listingId;
                        $newProposal->save();
                    }
                }
                $properties = Property::with('contact', 'author')
                    ->whereIn('ref_no', array_values((array) $scheduledDataDecode->listingSelection))
                    ->get();
                if (isset($scheduledDataDecode->reminder)) {
                    foreach ($properties as $property) {
                        if (!is_null($property->contact->email_1) || !is_null($property->contact->email_2)) {
                            $this->emailService->sendPropertyVisitOwnerNotification($property, $item, $reminderData);
                        }

                        if (!is_null($item->contact->email_1) || !is_null($item->contact->email_2)) {
                            $this->emailService->sendPropertyVisitClientNotification($property, $item, $reminderData);
                        }
                    }
                }
            }
        }
        if (!empty($scheduledDataDecode->remarks)) {
            $this->operationHistoryService->addOperationHistory($item, $scheduledDataDecode->remarks, auth()->user());
        }

        if (($request->input('lead_status_id') == '14' || $request->input('lead_status_id') == '15' || $request->input('lead_status_id') == '23') && !isset($scheduledDataDecode->reminder)) {
            $item->lead_status_id = $oldStatusId;
        } else {
            $this->leadsService->trackLeadStatusChange($item, $leadStatus);
            $item->lead_status_id = $request->input('lead_status_id');
        }

        $allStatuses = $this->leadStatusService->getLeadStatuses();
        $previousStatus = $item->leadStatus;
        if (!is_null($previousStatus)) {
            $oldStatusLabel = $previousStatus->name;
        }
        $newStatus = $allStatuses->where('id', $request->input('lead_status_id'))->first();
        if (!is_null($newStatus)) {
            $newStatusLabel = $newStatus->name;
        }
        if (!empty($oldStatusLabel) || !empty($newStatusLabel)) {
            $remarksBody = "Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "] ";
            $this->operationHistoryService->addOperationHistory($item, $remarksBody, auth()->user());
        }

        $this->updateListingPropertiesFromRequest($item);
        $this->handleFormsFilesUploads($item, $request);

        if (isset($this->afterItemUpdate)) {
            call_user_func($this->afterItemUpdate, $request, $item);
        }

        $this->handleOperationHistory($item);
        $requestAssignedTo = request()->input("assigned-to");
        $this->handleLeadAssignation($item, $requestAssignedTo);

        return $this->handleRedirect($item);
    }

    public function executePreUpdate($item, $validFields, $originalItem = null) {}

    public function create()
    {
        $definition = AssetDefinition::find(1);
        $readonly = false;
        $empty = true;
        $delete = false;
        $action = route($this->routes['store']);
        $back = route($this->routes['index']);

        $createForContact = null;
        if (request()->has('source') && request()->get('source') == 'contact' && request()->has('contactId')) {
            $createForContact = Contact::firstWhere('id', request()->get('contactId'));
        }
        $createFromTask = null;
        if (request()->has('taskId')) {
            $createFromTask = Task::firstWhere('id', request()->get('taskId'));
        }
        $listViewType = 'master';
        $hasInventoryTable = true;
        $wasReassignedByCurrentUser = false;
        $wasReassignedToCurrentUser = false;

        $propertyTypes = $this->propertiesService->getPropertyTypes();
        $geoData = $this->geographyService->getAndPrepareGeographyDataForFilters();
        $leadRatings = $this->leadsService->getLeadRatingsList();
        $cities = $geoData[0];
        $regions = $geoData[1];
        $areas = $geoData[2];
        $prices = $this->prices;
        $areaValues = $this->areaValues;
        $statusOptions = LeadStatus::all();
        $leadStatuses = $this->leadStatusService->getLeadStatuses();
        $sources = $this->leadsService->getCachedSources();
        $usersWithRoles = $this->userService->getUsersWithRoles();

        $valueLists = $this->inventoryService->getFilterAttributeValueLists();
        $furnishingOptions = $valueLists['property-features-furnishings'];
        $furnishingOptionsOffice = $valueLists['property-features-furnishings-office'];
        $propertyViews = PropertyView::all();
        $billsOptions = $valueLists['property-features-bills'];
        $prmin = $this->inventoryService->minRentPrices;
        $prmax = $this->inventoryService->maxRentPrices;
        $psmin = $this->inventoryService->maxSalePrices;
        $psmax = $this->inventoryService->maxSalePrices;
        $listingStatusOptions = [
            ['value' => '', 'label' => 'Please select'],
            ['value' => 'available', 'label' => 'Available'],
            ['value' => 'occupied', 'label' => 'Occupied', 'onlyFor' => 'sale'],
            ['value' => 'sold', 'label' => 'Sold', 'onlyFor' => 'sale'],
            ['value' => 'to-be-available', 'label' => 'To be available', 'onlyFor' => 'rent'],
            ['value' => 'rented', 'label' => 'Rented', 'onlyFor' => 'rent'],
        ];
        $hasPublishingRoles = auth()->user()->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::MASTER_BROKER);
        $isCorporateOptions = $this->brokerLandlordsService->landlordCategory;
        $operationTypeOptions = array_merge([['value' => '', 'label' => 'All']], $this->inventoryService->operationTypeOptions);
        $leadRequest = old('leads_request', '');

        $geography = Geography::select(['id', 'name', 'type', 'slug', 'parent_id', 'visible', 'avg_price', 'created_at', 'updated_at', 'deleted_at'])->orderBy('name', 'ASC')->get()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        foreach ($geography as $g) {
            $g->content = "";
        }

        $cities = $geography->filter(function ($item) {
            return $item->type == 'city';
        });

        $regions = $geography->filter(function ($item) {
            return $item->type == 'region';
        });

        $areas = $geography->filter(function ($item) {
            return $item->type == 'area';
        });

        // marketing platforms
        $marketingPlatforms = ExportPlatform::where('is_active', 1)->orderBy('name')->get();

        $questionsSpecificReq = DB::table('settings')
            ->whereIn('name', ['lead_specific_requirements_rent_q', 'lead_specific_requirements_sale_q'])
            ->get();

        $questionsForRent = $questionsSpecificReq->filter(function ($item) {
            return $item->name === 'lead_specific_requirements_rent_q';
        })->first();

        $questionsForSale = $questionsSpecificReq->filter(function ($item) {
            return $item->name === 'lead_specific_requirements_sale_q';
        })->first();

        $isEdit = isset($item) && $item->exists;
        $windows = $this->leadsService->getCachedLeadFollowupTaskWindows();
        $daysMap = [
            'A' => $windows['LEAD_A_FOLLOWUP_TASK_WINDOW'],
            'B' => $windows['LEAD_B_FOLLOWUP_TASK_WINDOW'],
            'C' => $windows['LEAD_C_FOLLOWUP_TASK_WINDOW'],
            'D' => $windows['LEAD_D_FOLLOWUP_TASK_WINDOW'],
            'E' => $windows['LEAD_E_FOLLOWUP_TASK_WINDOW'],
            'F' => $windows['LEAD_F_FOLLOWUP_TASK_WINDOW']
        ];

        return view($this->views['create'], compact('readonly', 'empty', 'delete', 'action', 'back', 'definition', 'cities', 'regions', 'areas', 'propertyTypes', 'prices', 'areaValues', 'sources', 'statusOptions', 'usersWithRoles', 'createForContact', 'createFromTask', 'furnishingOptions', 'furnishingOptionsOffice', 'propertyViews', 'billsOptions', 'prmin', 'prmax', 'psmin', 'psmax', 'listingStatusOptions', 'hasPublishingRoles', 'isCorporateOptions', 'operationTypeOptions', 'listViewType', 'leadRequest', 'hasInventoryTable', 'geography', 'cities', 'regions', 'areas', 'marketingPlatforms', 'leadStatuses', 'wasReassignedByCurrentUser', 'wasReassignedToCurrentUser', 'questionsForRent', 'questionsForSale', 'isEdit', 'leadRatings', 'daysMap'));
    }

    public function store()
    {
        $validFields = request()->validate($this->getFieldsToValidate('store'), $this->validationMessages);
        //am facut acest check ca sa rezolv cazul in care au fost date in statu 'VIEWING SCHEDULED' dar inainte sa se faca save s-a schimbat statusul
        if (!is_null($validFields['leads_viewing_scheduled']) && ($validFields['lead_status_id'] != '14' && $validFields['lead_status_id'] != '15' && $validFields['lead_status_id'] != '23' && $validFields['lead_status_id'] != '16')) {
            $validFields['leads_viewing_scheduled'] = null;
        }

        $item = $this->getNewItem();
        $item->created_by = auth()->user()->id;
        $this->updateObjectFieldsFromRequest($item, $validFields);
        $this->updateListingPropertiesFromRequest($item);
        $this->executePreCreate($item, $validFields);
        // handle lead status.
        // $requestAssignedTo = request()->get('assigned-to', -1);
        // if ($requestAssignedTo != "-1") {
        //     $notQualifiedStatus = LeadStatus::where(['name' => 'NOT_QUALIFIED'])->first();
        //     if (!is_null($notQualifiedStatus)) {
        //         $item->lead_status_id = $notQualifiedStatus->id;
        //     }
        // } else {
        $scheduledDataDecode = json_decode($validFields['leads_viewing_scheduled']);
        $leadStatus = LeadStatus::where('id', $validFields['lead_status_id'])->first();

        if ($validFields['lead_status_id'] == '26') {
            $agent = auth()->user();
            $today = date('Y-m-d');
            $afterTreeMonths = date('Y-m-d', strtotime('+3 months', strtotime($today)));
            $reminderData = [
                'title' => 'Far moving decision',
                'text' => 'Far moving date on ' . $agent->name . ' by ' . $today,
                'priority' => 'low',
                'due_date' => $afterTreeMonths,
                'reminder_email' => false,
                'reminder_email_date' => null,
                'reminder_type' => $leadStatus->name,
            ];
            $this->notesService->createReminderForLead($item, $reminderData);
        }

        if (($validFields['lead_status_id'] == '14' || $validFields['lead_status_id'] == '15' || $validFields['lead_status_id'] == '23' || $validFields['lead_status_id'] == '16')) {
            // reminder
            if (isset($scheduledDataDecode->reminder)) {
                $reminderFormData = $scheduledDataDecode->reminder;
                $reminderData = [
                    'title' => $reminderFormData->title,
                    'text' => $reminderFormData->content,
                    'priority' => $reminderFormData->priority,
                    'due_date' => $reminderFormData->dueDate,
                    'reminder_email' => $reminderFormData->sendEmail,
                    'reminder_email_date' => $reminderFormData->sendReminderDate,
                    'reminder_type' => $leadStatus->name,
                ];
                $shouldSetTask = $leadStatus->name == 'VIEWING_SCHEDULED' || $leadStatus->name == 'MEETING_SCHEDULED';
                $taskData = null;
                if ($shouldSetTask) {
                    $taskData = [
                        'status' => $leadStatus->name,
                        'listingSelection' => $scheduledDataDecode->listingSelection,
                    ];
                }
                $this->notesService->createReminderForLead($item, $reminderData, $taskData);
            }
            // proposals
            if (isset($scheduledDataDecode->listingSelection) && ($validFields['lead_status_id'] == '14' || $validFields['lead_status_id'] == '16')) {
                foreach ($scheduledDataDecode->listingSelection as $listingId => $listingRefNo) {
                    $listingAlreadyProposed = false;
                    foreach ($item->proposals as $proposal) {
                        if ($proposal->property->id == $listingId) {
                            $listingAlreadyProposed = true;
                        }
                    }
                    if (!$listingAlreadyProposed) {
                        $newProposal = new LeadProposal();
                        $newProposal->lead_id = $item->id;
                        $newProposal->property_id = $listingId;
                        $newProposal->save();
                    }
                }
                $properties = Property::with('contact', 'author')
                    ->whereIn('ref_no', array_values((array) $scheduledDataDecode->listingSelection))
                    ->get();
                if (isset($scheduledDataDecode->reminder)) {
                    foreach ($properties as $property) {
                        if (!is_null($property->contact->email_1) || !is_null($property->contact->email_2)) {
                            $this->emailService->sendPropertyVisitOwnerNotification($property, $item, $reminderData);
                        }

                        if (!is_null($item->contact->email_1) || !is_null($item->contact->email_2)) {
                            $this->emailService->sendPropertyVisitClientNotification($property, $item, $reminderData);
                        }
                    }
                }
            }
        }

        if (!empty($scheduledDataDecode->remarks)) {
            $this->operationHistoryService->addOperationHistory($item, $scheduledDataDecode->remarks, auth()->user());
        }

        // $newStatus = LeadStatus::where(['name' => 'NEW'])->first();
        // if (!is_null($newStatus)) {
        //     $item->lead_status_id = $newStatus->id;
        // }
        // }

        //daca nu s-au bagat date in status 'VIEWING SCHEDULED' sa puna status NEW
        if (($validFields['lead_status_id'] == '14' || $validFields['lead_status_id'] == '15' || $validFields['lead_status_id'] == '23') && !isset($scheduledDataDecode->reminder)) {
            $item->lead_status_id = '11';
        } else {
            $this->leadsService->trackLeadStatusChange($item, $leadStatus);
            $item->lead_status_id = $validFields['lead_status_id'];
        }

        $item->save();
        if (isset($item->task_id)) {
            Task::where('id', $item->task_id)->update(['status' => 'completed']);
        }
        if (isset($this->afterItemUpdate)) {
            call_user_func($this->afterItemUpdate, request(), $item);
        }

        // if the user is not assigned to anyone, assign it the the current user
        $requestAssignedTo = request()->input("assigned-to");
        if (empty($requestAssignedTo)) {
            $requestAssignedTo = auth()->user()->id;
        }

        $this->executePostCreate($item, $validFields);
        $this->handleLeadAssignation($item, $requestAssignedTo);
        $this->handleFormsFilesUploads($item, request());
        $this->operationHistoryService->addOperationHistory($item, 'Lead added to the system by ' .  auth()->user()->name, auth()->user());
        $this->operationHistoryService->addOperationHistory($item->contact, 'Lead created by ' .  auth()->user()->name, auth()->user());
        $this->handleOperationHistory($item);

        return $this->handleRedirect($item);
    }

    private function handleFormsFilesUploads($lead, $request)
    {
        $validFields = request()->validate([
            'lead-forms' => 'array',
            'documents_options' => 'array',
            'lead-forms-remove' => 'array'
        ]);

        $formIndexesToRemove = $validFields['lead-forms-remove'] ?? [];
        $itemForms = $lead->forms ?? [];
        $cleanupForms = [];
        if (count($formIndexesToRemove)) {
            foreach ($itemForms as $index => $itemForm) {
                $deleted = false;
                if (in_array($index, $formIndexesToRemove)) {
                    $deleted = $this->attachmentsService->removeLeadForm($itemForm->path);
                    Log::info('Deleted status for ' . $index . ": " . $deleted);
                }
                if (!$deleted) {
                    $cleanupForms[] = $itemForm;
                }
            }
        } else {
            $cleanupForms = $itemForms;
        }
        $forms = $validFields['lead-forms'] ?? null;
        $formsOptions = $validFields['documents_options'] ?? [];
        $uploadedForms = [];
        if (!is_null($forms)) {
            $uploadedForms = $this->attachmentsService->uploadLeadFormFiles($lead, $forms, $formsOptions);
        }
        $allForms = array_merge($cleanupForms, $uploadedForms);
        // dd($itemForms, $cleanupForms, $allForms);
        $lead->forms = $allForms;
        $lead->save();
    }

    public function previewForm($leadId)
    {
        if (request()->has('filePath')) {
            $filePath = request()->get('filePath');
            $lead = Lead::find($leadId);
            $forms = $lead->forms ?? [];

            $filteredFiles = array_filter($forms, function ($formFile) use ($filePath) {
                return $formFile->path == $filePath;
            });

            if (count($filteredFiles) > 0) {
                $firstArrayPos = array_values($filteredFiles)[0];
                $fullPath = Storage::path($firstArrayPos->path);

                if (!file_exists($fullPath)) {
                    return abort(404, 'File not found');
                }

                $extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
                $viewableExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];

                if (in_array($extension, $viewableExtensions)) {
                    return response()->file($fullPath, [
                        'Content-Type' => mime_content_type($fullPath),
                        'Content-Disposition' => 'inline; filename="' . $firstArrayPos->title . '"'
                    ]);
                }

                return Storage::download($firstArrayPos->path, $firstArrayPos->title);
            }
        }

        return response('', 400);
    }

    public function downloadForm($leadId)
    {
        if (request()->has('filePath')) {
            $filePath = request()->get('filePath');
            $lead = Lead::find($leadId);
            $forms = $lead->forms ?? [];
            $filteredFiles = array_filter($forms, function ($formFile) use ($filePath) {
                return $formFile->path == $filePath;
            });
            if (count($filteredFiles) > 0) {
                $firstArrayPos = array_values($filteredFiles)[0];
                return Storage::download($firstArrayPos->path, $firstArrayPos->title);
            }
        }

        return response();
    }

    // new asignment - requested by Serban on 14.11.2022 - Telegram
    protected function executePostCreate($item, $validFields)
    {
        $item->save();
        // $assignment = new LeadAssignment();
        // $assignment->user_id = auth()->user()->id;
        // $assignment->lead_id = $item->id;
        // $assignment->save();
    }

    public function propertySuggestions(InventoryService $inventoryService, AuthorizationService $authService)
    {
        $request = request();
        if ($request->ajax()) {
            $properties = $inventoryService->getItems($request);
            $count = $inventoryService->getItemsCount($request);

            $allowedActions = $authService->getAllowedActions(AuthorizationService::CATEG_INVENTORY);
            $items = $inventoryService->prepareLeadSuggestionItems($properties, $allowedActions);

            $payload = ['data' => $items, 'recordsTotal' => $count, 'recordsFiltered' => $count];
            return response()->json($payload);
        }
    }

    protected function getNewItem()
    {
        $item = new Lead();
        $item->platform_from = '';
        $item->lead_status_id = '';
        return $item;
    }

    protected function updateObjectFieldsFromRequest($item, $validFields)
    {
        $columns = $item->getColumns();
        foreach ($columns as $column) {
            if ($this->isFillableColumn($column)) {
                $value = request()->input($column);
                $item->$column = $value;
            }
        }
        $item->platform_from = $item->platform_from ?? "";
        $item->lead_status_id = $item->lead_status_id ?? "";
        $item->rating = $validFields['rating'] ?? "";
    }

    private function isFillableColumn($colName)
    {
        return !in_array($colName, [
            'id',
            'lead_status_id',
            'created_at',
            'created_by',
            'deleted_at',
            'deleted_by',
            'updated_at',
            'updated_by',
            'lead_metadata',
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'first_assignment_user',
            "forms"
        ]);
    }

    public function export()
    {
        $user = auth()->user();
        if (!$user->hasRole(RolesDef::OFFICE_MANAGER)) {
            return response("Not authorized", 403);
        }
        return Excel::download(new LeadsExport, 'leads_' . (date("YMDHi")) . '.xlsx');
    }

    public function exportFiltered()
    {
        $user = auth()->user();
        if (!$user->hasRole(RolesDef::OFFICE_MANAGER)) {
            return response("Not authorized", 403);
        }

        $extraConfig = $this->leServ->getExtraConfigFromExportRequest(request());
        $this->itemFetcher = function ($offset = 0, $limit = -1) use ($extraConfig) {
            return $this->authoServ->getVisibleItems(AuthorizationService::CATEG_LEADS, null, $offset, $limit, $extraConfig);
        };

        $items = call_user_func($this->itemFetcher, 0, -1);

        if (isset($items['items'])) {
            if (is_array($items['items'])) {
                $data = array_map($this->ajaxListMapper, $items['items']);
            } else {
                $data = $items['items']->map($this->ajaxListMapper);
            }
        } else {
            $data = $items->map($this->ajaxListMapper);
        }

        // dd($data);

        return Excel::download(new FilteredLeadsExport($data), 'leads_' . (date("YMDHi")) . '.xlsx');
    }

    public function saveSource(Request $request)
    {
        $validFields = $request->validate([
            "name" => "required|unique:lead_sources|max:128"
        ]);

        $leadSource = LeadSource::create($validFields);
        Cache::forget(CacheKeys::LEAD_SOURCES);
        return $leadSource->toArray();
    }

    private function handleOperationHistory($item)
    {
        $validFields = request()->validate($this->getFieldsToValidate('store'), $this->validationMessages);

        if (!empty($validFields['operation-history'])) {
            $this->operationHistoryService->addOperationHistory($item, $validFields['operation-history'], auth()->user());

            $lead = $item->with('assignment', 'author', 'latestAssignment')->where('id', $item->id)->first();
            $this->emailService->sendEmailOnAddingOperationHistoryForLeads($lead, auth()->user(), $validFields['operation-history']);
            $item->last_contact_date = new \DateTime();
            $item->save();
        }
    }

    public function listingOperationHistory($id)
    {
        $lead = Lead::with([
            'operationHistory' => fn($qb) => $qb->orderBy('id', 'DESC'),
            'operationHistory.author'
        ])
            ->where('id', '=', $id)
            ->first();
        return $lead->operationHistory->map(function ($row) {
            return [
                'created_by' => isset($row->author['name']) ? $row->author['name'] : 'Deleted Account',
                'created_at' => $row->created_at->format('Y-m-d H:i:s'),
                'content' => $row->content
            ];
        });
    }

    public function createOperationHistory($id)
    {
        $validData = request()->validate([
            'operationHistory' => 'required'
        ]);
        try {
            $lead = Lead::with('assignment', 'author', 'contact', 'location', 'latestAssignment')->where('id', $id)->first();
            $this->operationHistoryService->addOperationHistory($lead, $validData['operationHistory'], auth()->user());
            $this->emailService->sendEmailOnAddingOperationHistoryForLeads($lead, auth()->user(), $validData['operationHistory']);
            $lead->updated_at = new \DateTime();
            $lead->last_contact_date = new \DateTime();
            $lead->save();
            return response(['message' => 'ok'], 200);
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
            return response($Ex->getMessage(), 500);
        }
    }

    public function updateLeadStatus($id)
    {
        $validData = request()->validate([
            'agent' => 'required_if:status,21',
            'status' => 'exists:lead_status,id',
            'remarks' => 'required_unless:status,14,15,23,26',
            'viewingScheduledData' => 'required_if:status,14',
        ]);

        try {
            $oldStatusLabel = '';
            $newStatusLabel = '';
            $reminderRemarks = '';

            $lead = Lead::where('id', $id)->with('latestAssignment')->firstOrFail();
            $allStatuses = $this->leadStatusService->getLeadStatuses();
            $leadStatus = LeadStatus::where('id', $validData['status'])->first();
            if ($validData['status'] == '26') {
                $agent = auth()->user();
                $today = date('Y-m-d');
                $afterTreeMonths = date('Y-m-d', strtotime('+3 months', strtotime($today)));
                $reminderData = [
                    'title' => 'Far moving decision',
                    'text' => 'Far moving date on ' . $agent->name . ' by ' . $today,
                    'priority' => 'low',
                    'due_date' => $afterTreeMonths,
                    'reminder_email' => false,
                    'reminder_email_date' => null,
                    'reminder_type' => $leadStatus->name,
                ];
                $this->notesService->createReminderForLead($lead, $reminderData);
            }

            if ($validData['status'] == '14' || $validData['status'] == '15' || $validData['status'] == '23' || $validData['status'] == '16') { // VIEWING_SCHEDULED && FOLLOW_UP && MEETING_SCHEDULED
                // reminder
                if (isset($validData['viewingScheduledData']['reminder'])) {
                    $reminderData = [
                        'title' => $validData['viewingScheduledData']['reminder']['title'],
                        'text' => $validData['viewingScheduledData']['reminder']['content'],
                        'priority' => $validData['viewingScheduledData']['reminder']['priority'],
                        'due_date' => $validData['viewingScheduledData']['reminder']['dueDate'],
                        'reminder_email' => $validData['viewingScheduledData']['reminder']['sendEmail'],
                        'reminder_email_date' => $validData['viewingScheduledData']['reminder']['sendReminderDate'],
                        'reminder_type' => $leadStatus->name,
                    ];
                    $newLeadStatus = $allStatuses->where('id', $validData['status'])->first();
                    $newLeadStatusLabel = null;
                    if (!is_null($newLeadStatus)) {
                        $newLeadStatusLabel = $newLeadStatus->name;
                    }
                    $shouldSetTask = $newLeadStatusLabel == 'VIEWING_SCHEDULED' || $newLeadStatusLabel == 'MEETING_SCHEDULED';
                    $taskData = null;
                    if ($shouldSetTask) {
                        $taskData = [
                            'status' => $newLeadStatusLabel,
                            'listingSelection' => $validData['viewingScheduledData']['listingSelection'],
                        ];
                    }
                    $this->notesService->createReminderForLead($lead, $reminderData, $taskData);
                }
                // proposals
                foreach ($validData['viewingScheduledData']['listingSelection'] as $listingId => $listingRefNo) {
                    $listingAlreadyProposed = false;
                    foreach ($lead->proposals as $proposal) {
                        if ($proposal->property->id == $listingId) {
                            $listingAlreadyProposed = true;
                        }
                    }
                    if (!$listingAlreadyProposed) {
                        $newProposal = new LeadProposal();
                        $newProposal->lead_id = $lead->id;
                        $newProposal->property_id = $listingId;
                        $newProposal->save();
                    }
                }
                $reminderRemarks = $validData['viewingScheduledData']['remarks'];
            }


            $previousStatus = $lead->leadStatus;
            if (!is_null($previousStatus)) {
                $oldStatusLabel = $previousStatus->name;
            }
            $newStatus = $allStatuses->where('id', $validData['status'])->first();
            if (!is_null($newStatus)) {
                $newStatusLabel = $newStatus->name;
            }

            $this->leadsService->trackLeadStatusChange($lead, $leadStatus);
            $lead->lead_status_id = $validData['status'];
            $lead->save();

            $remarksBody = "Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "] ";
            if (!empty($validData['remarks'])) {
                $remarksBody .= "\r\nRemarks:\r\n" . $validData['remarks'];
            }
            if (!empty($reminderRemarks)) {
                $remarksBody .= "\r\nReminder remarks:\r\n " . $reminderRemarks;
            }

            $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());

            // if there is no assignment, assign the lead to the authenticated user - https://fgreal.atlassian.net/browse/KAN-213
            if ($validData['status'] == '21') {
                $user = User::where('id', $validData['agent'])->first();
                $this->leadsService->assignLeadToUser($lead, $user);
                $this->operationHistoryService->addOperationHistory($lead, "The lead was assigned to [" . $user->name . "]", auth()->user());
            } elseif (is_null($lead->latestAssignment)) {
                $this->leadsService->assignLeadToUser($lead, auth()->user());
                $this->operationHistoryService->addOperationHistory($lead, "The lead was assigned to [" . auth()->user()->name . "]", auth()->user());
            }

            if ($validData['status'] == '14') {
                // Put this back after Serban confirms
                $properties = Property::with('contact', 'author')
                    ->whereIn('ref_no', $validData['viewingScheduledData']['listingSelection'])
                    ->get();

                foreach ($properties as $property) {
                    if (!is_null($property->contact->email_1) || !is_null($property->contact->email_2)) {
                        $this->emailService->sendPropertyVisitOwnerNotification($property, $lead, $reminderData);
                    }

                    if (!is_null($lead->contact->email_1) || !is_null($lead->contact->email_2)) {
                        $this->emailService->sendPropertyVisitClientNotification($property, $lead, $reminderData);
                    }
                }
            }

            return [
                "msg" => 'Ok'
            ];
        } catch (\Exception $Ex) {
            return response([
                'message' => $Ex->getMessage()
            ], 500);
        }
    }

    // lead dashboard methods:
    public function patchUpdateLead($id)
    {
        $validData = request()->validate([
            'status' => 'required',
            'remarks' => 'required_unless:status,FAR_MOVING_DECISION',
            'agent' => 'required_if:status,GHOSTED',
            'reminderFormData' => 'required_if:status,VIEWING_SCHEDULED, MEETING_SCHEDULED, FOLLOW_UP',
            'listingSelection' => 'required_if:status,VIEWING_SCHEDULED, OFFER_NEGOTIATION',
        ]);

        $lead = Lead::with(['proposals', 'proposals.property', 'contact'])->findOrFail($id);
        $nextLeadStatus = LeadStatus::where('name', $validData['status'])->firstOrFail();

        if ($validData['status'] == 'FAR_MOVING_DECISION') {
            $agent = auth()->user();
            $today = date('Y-m-d');
            $afterTreeMonths = date('Y-m-d', strtotime('+3 months', strtotime($today)));
            $reminderData = [
                'title' => 'Far moving decision',
                'text' => 'Far moving date on ' . $agent->name . ' by ' . $today,
                'priority' => 'low',
                'due_date' => $afterTreeMonths,
                'reminder_email' => false,
                'reminder_email_date' => null,
                'reminder_type' => $nextLeadStatus->name,
            ];
            $this->notesService->createReminderForLead($lead, $reminderData);
        }

        if (
            $validData['status'] == 'VIEWING_SCHEDULED' ||
            $validData['status'] == 'MEETING_SCHEDULED' ||
            $validData['status'] == 'FOLLOW_UP' ||
            $validData['status'] == 'OFFER_NEGOTIATION'
        ) {
            // reminder
            if (isset($validData['reminderFormData'])) {
                // dd($validData['reminderFormData']);
                $reminderData = [
                    'title' => $validData['reminderFormData']['title'],
                    'text' => $validData['reminderFormData']['content'],
                    'priority' => $validData['reminderFormData']['priority'],
                    'due_date' => $validData['reminderFormData']['dueDate'],
                    'reminder_email' => $validData['reminderFormData']['sendEmail'],
                    'reminder_email_date' => $validData['reminderFormData']['sendReminderDate'],
                    'reminder_type' => $nextLeadStatus->name,
                ];

                $shouldSetTask = $validData['status'] == 'VIEWING_SCHEDULED' || $validData['status'] == 'MEETING_SCHEDULED';
                $taskData = null;
                if ($shouldSetTask) {
                    $taskData = [
                        'status' => $validData['status'],
                        'listingSelection' => $validData['listingSelection'],
                    ];
                }
                $this->notesService->createReminderForLead(
                    $lead,
                    $reminderData,
                    $taskData
                );
            }
            // proposals
            foreach ($validData['listingSelection'] as $listingId => $listingRefNo) {
                $listingAlreadyProposed = false;
                foreach ($lead->proposals as $proposal) {
                    if ($proposal->property->id == $listingId) {
                        $listingAlreadyProposed = true;
                    }
                }
                if (!$listingAlreadyProposed) {
                    $newProposal = new LeadProposal();
                    $newProposal->lead_id = $lead->id;
                    $newProposal->property_id = $listingId;
                    $newProposal->save();
                }
            }
        }
        if ($nextLeadStatus->id != $lead->lead_status_id) {
            $this->leadsService->trackLeadStatusChange($lead, $nextLeadStatus);
            $lead->lead_status_id = $nextLeadStatus->id;
            $lead->save();

            $currentAssignments = LeadAssignment::where('lead_id', $lead->id)->orderBy('created_at', 'DESC')->get();
            $currentAssignment = $currentAssignments->first();
            if ($nextLeadStatus->name == "NOT_QUALIFIED") {
                $currentAssignments = LeadAssignment::where('lead_id', $lead->id)->orderBy('created_at', 'DESC')->get();
                if ($currentAssignments->count()) {
                    foreach ($currentAssignments as $old) {
                        $old->delete();
                        // $this->operationHistoryService->addOperationHistory($lead, 'The lead was unassigned from [' . $old->user->name . ']', auth()->user());
                    }
                }
            } else if ($nextLeadStatus->name == "GHOSTED") {
                $assignment = new LeadAssignment();
                $assignment->user_id = $validData['agent'];
                $assignment->lead_id = $lead->id;
                $assignment->save();
            } else {
                $currentUser = auth()->user();
                $currentUserIsAgent = $currentUser->hasRole(RolesDef::AGENT);
                if ($currentUserIsAgent) {
                    if (!is_null($currentAssignment) && $currentAssignment->user_id == $currentUser->id) {
                        // do noting
                    } else {
                        // if (!is_null($currentAssignment) && $currentAssignment->user_id != $currentUser->id) {
                        foreach ($currentAssignments as $old) {
                            $old->delete();
                            $this->operationHistoryService->addOperationHistory($lead, 'The lead was unassigned from [' . $old->user->name . ']', $currentUser);
                        }
                        // }
                        $assignment = new LeadAssignment();
                        $assignment->user_id = $currentUser->id;
                        $assignment->lead_id = $lead->id;
                        $assignment->save();
                    }

                    // $this->operationHistoryService->addOperationHistory($lead, 'The lead was assigned to [' . $currentUser->name . ']', $currentUser);
                }
            }

            // $this->operationHistoryService->addOperationHistory($lead, 'Lead status changed to [' . $nextLeadStatus->name . ']', auth()->user());
        }
        if (!empty($validData['remarks'])) {
            $this->operationHistoryService->addOperationHistory($lead, $validData['remarks'], auth()->user());
        }

        if (isset($validData['listingSelection']) && isset($validData['reminderFormData']) && $validData['status'] == 'VIEWING_SCHEDULED') {
            $properties = Property::with('contact', 'author')
                ->whereIn('ref_no', $validData['listingSelection'])
                ->get();

            foreach ($properties as $property) {
                if (!is_null($property->contact->email_1) || !is_null($property->contact->email_2)) {
                    $this->emailService->sendPropertyVisitOwnerNotification($property, $lead, $reminderData);
                }

                if (!is_null($lead->contact->email_1) || !is_null($lead->contact->email_2)) {
                    $this->emailService->sendPropertyVisitClientNotification($property, $lead, $reminderData);
                }
            }
        }

        return response(['msg' => 'Success']);
    }

    public function updateOldLeadStatuses()
    {
        try {
            $oldStatusIds = [1, 5, 10];
            $leads = Lead::with('leadStatus')->whereIn('lead_status_id', $oldStatusIds)->get();
            $userSystem = User::where('id', 1)->first();
            foreach ($leads as $lead) {
                $oldStatus = $lead->leadStatus->name;
                $newStatus = 'NEW';
                $lead->lead_status_id = '11';
                $lead->save();

                $remarksBody = "Lead status updated from [" . $oldStatus . "] to [" . $newStatus . "] ";
                $this->operationHistoryService->addOperationHistory($lead, $remarksBody, $userSystem);
            }

            return [
                "msg" => 'Ok'
            ];
        } catch (\Exception $Ex) {
            return response([
                'message' => $Ex->getMessage()
            ], 500);
        }
    }

    public function leadByQnbFunding()
    {
        $validData = request()->validate([
            'name' => 'required',
            'email' => 'required',
            'phone' => 'required',
            'phoneCountry' => 'required',
            'usedNationality' => 'required',
            'usedPeriod' => '',
            'companyName' => '',
            'jobPosition' => '',
            'amount' => '',
            'terms' => '',
            'language' => ''
        ]);

        Log::info("New QNB Request - valid data", $validData);

        $existingContactByEmail = $this->contactsService->getContactsByEmail($validData['email']);
        $existingContactByPhone = $this->contactsService->getContactsByPhoneNumber($validData['phone']);

        $existingMasterContact = $existingContactByEmail->whereNull('master_contact_id')->first();
        if (is_null($existingMasterContact)) {
            $existingContact = $existingContactByEmail->first();
            Log::info("New QNB Request - existing contact - branch 1");
        } else {
            $existingContact = $existingMasterContact;
            Log::info("New QNB Request - existing contact - branch 2");
        }

        if (is_null($existingContact)) {
            $existingMasterContact = $existingContactByPhone->whereNull('master_contact_id')->first();
            if (is_null($existingMasterContact)) {
                $existingContact = $existingContactByPhone->first();
                Log::info("New QNB Request - existing contact - branch 3");
            } else {
                $existingContact = $existingMasterContact;
                Log::info("New QNB Request - existing contact - branch 4");
            }
        }

        $qnbFinanceTag = ContactsListTag::where('label', 'QNB Finance')->first();
        if (is_null($existingContact)) {
            Log::info("Existing contact is null");
            $contact = new Contact();
            $contact->name = $validData['name'];
            $contact->email_1 = $validData['email'];
            $contact->nationality_id = $validData['usedNationality']['id'];
            $contact->prefix_mobile_1 = $validData['phoneCountry']['phone_prefix'];
            $contact->mobile_1 = $validData['phone'];
            $contact->master_contact_id = null;
            $contact->is_master_contact = true;
            $contact->save();
            $existingContact = $contact;

            if (!is_null($qnbFinanceTag)) {
                $contact->tags()->attach($qnbFinanceTag->id);
            }
            Log::info('QNB: Created contact ' . $contact->id);
        } else {
            Log::info("Existing contact is not null");
            if (!$existingContact instanceof Contact) {
                $existingContact = Contact::with(['tags'])->where('id', $existingContact->id)->first();
            }

            $QNBFinanceTagInTags = $existingContact->tags->filter(fn($tag) => strpos($tag->label, 'QNB Finance') > -1)->first();
            if (is_null($QNBFinanceTagInTags)) {
                if (!is_null($qnbFinanceTag)) {
                    $existingContact->tags()->attach($qnbFinanceTag->id);
                    Log::info('Attached QNB Tag to contact ' . $existingContact->id);
                }
            }
            $this->operationHistoryService->addOperationHistory($existingContact, "Existing contact found when submiting QNB Qatar form.");
        }

        $requirements = "
Amount: " . ($validData['amount'] ?? '-') . "
Company Name: " . ($validData['companyName'] ?? '-') . "
Email: " . ($validData['email'] ?? '-') . "
Job Position: " . ($validData['jobPosition']) . "
Name: " . ($validData['name']) . "
Phone: " . ($validData['phoneCountry']["phone_prefix"]) . " " . ($validData['phone']) . "
Nationality: " . ($validData['usedNationality']["label"]) . "
Been in Qatar: " . ($validData['usedPeriod']['label'] ?? '-') . "
Form language: " . ($validData['language'] ?? '-') . "
        ";

        $lead = new Lead();
        if ($existingContact) {
            $lead->contact_id = $existingContact->id;
        } else if ($contact) {
            $lead->contact_id = $contact->id;
        }

        $platform = LeadSource::where('name', 'QNB Finance Form')->first();
        if (!is_null($platform)) {
            $lead->platform_from = $platform->id;
        }
        $lead->requirements = $requirements;
        $lead->save();

        $this->emailService->sendEmailToClientForQNBFunding($validData['email'], $validData['name']);
        $this->emailService->sendEmailToAdminForQNBRequest($validData);

        return response([], 200);
    }

    public function viewV2()
    {
        return view("crm.leads.v2.container");
    }
}
