<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\CrmBaseController;
use App\Models\LeadStatus;
use App\Models\Geography;
use App\Models\Lead;
use App\Models\User;
use App\Models\LeadReassignation;
use App\Services\BrokerLandlordsService;
use App\Services\InventoryService;
use App\Services\EmailService;
use App\Services\LeadsService;
use App\Services\LeadStatusService;
use App\Services\OperationHistoryService;
use App\Services\UserService;
use Log;

class LeadDashboardNextController extends CrmBaseController
{
    private $inventoryService;
    private $emailService;
    private $operationHistoryService;
    private $userService;
    private $brokerLandlordsService;
    private $leadsService;
    private $leadStatusService;

    public function __construct(
        InventoryService $inventoryService,
        EmailService $emailService,
        OperationHistoryService $operationHistoryService,
        UserService $userService,
        BrokerLandlordsService $brokerLandlordsService,
        LeadsService $leadsService,
        LeadStatusService $leadStatusService
    ) {
        $this->inventoryService = $inventoryService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->userService = $userService;
        $this->brokerLandlordsService = $brokerLandlordsService;
        $this->leadsService = $leadsService;
        $this->leadStatusService = $leadStatusService;
    }

    protected $views = [
        'index' => 'crm.lead-dashboard.index',
    ];

    public function viewVars($viewType)
    {
        if ($viewType == 'index') {
            return $this->getIndexViewVars();
        }
    }

    public function getIndexViewVars()
    {
        $geography = Geography::select(['id', 'name', 'type', 'slug', 'parent_id', 'visible', 'avg_price', 'created_at', 'updated_at', 'deleted_at'])->orderBy('name', 'ASC')->get()->mapWithKeys(function ($item) {
            return [$item->id => $item];
        });

        foreach ($geography as $g) {
            $g->content = "";
        }

        $cities = $geography->filter(function ($item) {
            return $item->type == 'city';
        });

        $regions = $geography->filter(function ($item) {
            return $item->type == 'region';
        });

        $areas = $geography->filter(function ($item) {
            return $item->type == 'area';
        });

        $prmin = $this->inventoryService->minRentPrices;
        $prmax = $this->inventoryService->maxRentPrices;
        $psmin = $this->inventoryService->maxSalePrices;
        $psmax = $this->inventoryService->maxSalePrices;

        $operationTypeOptions = array_merge([['value' => '', 'label' => 'All']], $this->inventoryService->operationTypeOptions);
        $agents = $this->userService->getPossibleListingOwners();
        $agentsForReassign = $agents->where('id', '!=', auth()->user()->id);
        $sources = $this->leadsService->getCachedSources();
        $isCorporateOptions = $this->brokerLandlordsService->landlordCategory;
        $statusOptions = $this->leadStatusService->getLeadStatuses();

        $availableViewTypes = [];
        if (auth()->user()->hasAnyRole(\App\Models\Crm\RolesDef::OFFICE_MANAGER)) {
            $availableViewTypes[] = 'master';
        }
        if (auth()->user()->hasAnyRole(\App\Models\Crm\RolesDef::TEAM_LEADER)) {
            $availableViewTypes[] = 'team_list';
        }

        return compact([
            'geography',
            'cities',
            'regions',
            'areas',
            'prmin',
            'prmax',
            'psmin',
            'psmax',
            'operationTypeOptions',
            'agents',
            'agentsForReassign',
            'sources',
            'isCorporateOptions',
            'availableViewTypes',
            'statusOptions'
        ]);
    }

    public function createLead()
    {
        $validData = request()->validate([
            'contact_id' => '',
            'filter_operation_type' => '',
            'remarks' => '',
        ]);

        try {
            $leadRequest = (object)[];
            $leadRequest->ot = $validData['filter_operation_type'];
            $leadRequest->created_by = auth()->user()->id;
            $validData['leads_request'] = json_encode($leadRequest);
            $validData['lead_status_id'] = '11';
            $lead = Lead::create($validData);

            $this->operationHistoryService->addOperationHistory($lead, 'Lead status changed to NEW', auth()->user());
            if (!empty($validData['remarks'])) {
                // $this->emailService->sendEmailOnAddingOperationHistoryForLeads($lead, auth()->user(), $validData['remarks']);
                // am pus ambele emailuri - nu stiu care ar fi bun aici in cazul nostru
                $this->operationHistoryService->addOperationHistory($lead, 'New remarks added: [' . $validData['remarks'] . ']', auth()->user());
            }

            $this->emailService->createLeadEmail($lead);
            return response($lead, 201);
        } catch (\Exception $Ex) {
            return response($Ex->getMessage(), 500);
        }
    }

    public function reassignLead() {
        $validData = request()->validate([
            'lead_id' => 'required|exists:leads,id',
            'agent_id' => 'required|exists:users,id',
            // 'remarks' => ''
        ]);

        $existingAssignation = LeadReassignation::where([
            'lead_id' => $validData['lead_id'],
            'user_id' => auth()->user()->id
        ])->first();
        

        if(is_null($existingAssignation)) {
            try {
                $lead = Lead::where('id', '=', $validData['lead_id'])->firstOrFail();
                Log::info('lead found: '.(!is_null($lead)));
                $agent = User::where('id', '=', $validData['agent_id'])->firstOrFail();
                Log::info('agent found: '.(!is_null($agent)));
                $lra = LeadReassignation::create([
                    'lead_id' => $validData['lead_id'],
                    'user_id' => auth()->user()->id
                ]);
                Log::info('lra created: '.($lra->id));

                $this->leadsService->assignLeadToUser($lead, $agent, true);
                Log::info('After assignment');
                $this->operationHistoryService->addOperationHistory($lead, "The lead has been reassigned by [".auth()->user()->name."] to [".$agent->name."]");
            } catch(\Exception $Ex) {
                Log::error($Ex->getMessage());
            }
            
            return response(['msg' => 'will assign'], 200);
        }

        return response(['error' => 'existingReassignation'], 422);
    }
}
