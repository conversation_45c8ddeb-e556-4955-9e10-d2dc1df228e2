<?php

namespace App\Http\Controllers\Crm;

use App\Models\LeadAssignment;
use App\Services\AuthorizationService;
use App\Services\GeographyService;
use App\Services\LeadsExportService;
use App\Services\PropertiesService;
use Illuminate\Http\Request;

class LeadsController extends FormController
//class LeadsController extends CrmBaseController
{
    public $prices;

    protected $modelClass = 'App\Models\Lead';
    protected $formView = 'crm.forms.lead';
    protected $indexView = "crm.leads-old.index";
    protected $hasEnvelope = true;

    protected $ajaxListMapper;
    protected $listRoute = 'leads.index';
    protected $createPostRoute = 'lead-create-post';
    protected $updatePostRoute = 'lead-update-post';
    protected $deletePostRoute = 'lead-delete-post';

    protected $deleteFunction;

    protected $afterItemUpdate;

    protected $itemFetcher;

    protected $createValidator;
    protected $updateValidator;

    private $geographyService;
    private $propertiesService;

    public function __construct(
        AuthorizationService $authoServ,
        LeadsExportService $leServ,
        Request $request,
        GeographyService $geographyService,
        PropertiesService $propertiesService
    ) {
        $this->geographyService = $geographyService;
        $this->propertiesService = $propertiesService;
        $extraConfig = $leServ->getExtraConfig($request);
        $this->itemFetcher = function ($offset = 0, $limit = 10) use ($authoServ, $extraConfig) {
            return $authoServ->getVisibleItems(AuthorizationService::CATEG_LEADS, null, $offset, $limit, $extraConfig);
        };

        $this->ajaxListMapper = $leServ->ajaxListMapper;
        $this->afterItemUpdate = function ($request, $item) {
            $assignedTo = $request->input("assigned-to");

            if (!Auth::user()->hasRole(RolesDef::OFFICE_MANAGER)) {
                return;
            }

            //Delete old assignments
            $oldAssignments = LeadAssignment::where('lead_id', $item->id)->get();


            if ($assignedTo && $assignedTo != "") {
                $user = User::find($assignedTo);

                $found = false;
                foreach ($oldAssignments as $old) {
                    if ($old->user_id == $user->id) {
                        $found = true;
                    }
                }

                if (!$found) {
                    foreach ($oldAssignments as $old) {
                        $old->forceDelete();
                    }

                    if ($user) {
                        $assignment = new LeadAssignment();
                        $assignment->user_id = $user->id;
                        $assignment->lead_id = $item->id;
                        $assignment->save();
                    }
                } else {
                    //Do nothing
                }
            } else {
                //Delete ald assignment(s) - paranoid mode,
                //there should always be at most one assignment
                //but we're deleting all we can find
                foreach ($oldAssignments as $old) {
                    $old->forceDelete();
                }
            }
        };

        $this->afterItemCreate = function ($request, $item) {
            $assignedTo = $request->input("assigned-to");

            if ($assignedTo && $assignedTo != "") {
                $user = User::find($assignedTo);

                if ($user) {
                    $assignment = LeadAssignment::firstOrCreate(['user_id' => $user->id, 'lead_id' => $item->id]);
                    $assignment->save();
                }
            }
        };

        $this->createValidator = function ($controller, $request) {
            $controller->validate(
                $request,
                ['contact_id' => 'required'],
                ['contact_id.required' => 'The :attribute field is required. Either select an existing contact or create a new one.'],
                ['contact_id' => 'Client']
            );
        };

        $this->updateValidator = function ($controller, $request) {
            $controller->validate(
                $request,
                ['contact_id' => 'required'],
                ['contact_id.required' => 'The :attribute field is required. Either select an existing contact or create a new one.'],
                ['contact_id' => 'Client']
            );
        };

        $propertyPrices = $this->propertiesService->getMinMaxPrices();
        $prices = ["min" => [], "max" => []];

        foreach ($propertyPrices as $vals) {
            foreach ($vals as $k => $arrPrices) {
                foreach ($arrPrices as $priceK => $priceLabel) {
                    $prices[$k][$priceK] = $priceLabel;
                }
            }
        };

        $this->prices = $prices;
        $this->areaValues = $this->propertiesService->areas;
    }
}
