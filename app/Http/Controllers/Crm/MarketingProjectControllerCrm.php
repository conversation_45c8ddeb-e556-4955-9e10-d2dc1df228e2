<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\CrmBaseController;
use App\Models\MarketingCampaign;
use App\Models\MarketingProject;
use App\Services\MarketingProjectService;

class MarketingProjectControllerCrm extends CrmBaseController
{
    protected $views = [
        'index' => 'crm.project.index',
        'create' => 'crm.project.form.form',
        'edit' => 'crm.project.form.form',
    ];

    protected $routes = [
        'index' => 'crm.projects.index',
        'edit' => 'crm.projects.edit'
    ];

    protected $messages = [
        'edit.success' => 'The item was successfully saved'
    ];

    protected $validationMessages = [
        'name.required' => 'This field is required',
    ];

    protected $fieldsToValidate = [
        'name' => 'required',
    ];

    private $projectService;

    public function __construct(MarketingProjectService $projectService)
    {
        $this->projectService = $projectService;
    }

    public function siteContent()
    {
        return view('crm.forms.site-content.magazine.site-content');
    }

    protected function getNewItem()
    {
        $project = new MarketingProject;
        $project->created_by = auth()->user()->id;

        return $project;
    }

    protected function updateObjectFieldsFromRequest($project, $validFields)
    {
        foreach ($validFields as $requestFieldName => $validValue) {
            if (in_array($requestFieldName, ['name'])) {
                $project->$requestFieldName = $validValue;
            }
        }
        $project->created_by = auth()->user()->id;
    }

    protected function viewVars(string $viewName, $assetId = null)
    {

        if ($viewName == 'edit' || $viewName == 'create') {
            return $this->getViewVars($assetId);
        }

        return compact([]);
    }

    private function getViewVars($id = null)
    {
        $empty = false;
        $delete = false;
        $readonly = false;

        $item = is_null($id) ? new MarketingProject() : $this->getDbItem($id);
        $formAction = is_null($id) ? route('crm.projects.create.post') : route('crm.projects.update', ['id' => $id]);
        return compact([
            'empty',
            'delete',
            'readonly',
            'id',
            'item',
            'formAction'
        ]);
    }

    protected function executePostUpdate($item, $validFields) {}

    protected function executePostCreate($item, $validFields) {}

    protected function getDbItem($id)
    {
        return MarketingProject::find($id);
    }

    protected function ajaxRequestIndex()
    {
        $items = $this->projectService->getTableItems(request())->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'campaigns_count' => $item->campaigns_count == 1 ? $item->campaigns_count.' campaign' : $item->campaigns_count.' campaigns',
                'created_at' => (new \DateTime($item->created_at))->format("d/m/Y"),
                'created_by' => $item->createdBy,
                'actions' => ''
            ];
        });

        $count = $this->projectService->getTableItemsCount(request());

        return [
            'data' => $items,
            'recordsTotal' => $count,
            'recordsFiltered' => $count
        ];
    }

    protected function destroy($id)
    {
        $this->projectService->destroyProject($id);
        return $this->redirectToList('The project was successfully removed');
    }

    public function campaign_create($itemId)
    {
        $project = $this->getDbItem($itemId);

        $validData = request()->validate([
            'campaigns' => 'required|array',
            'campaigns.*.name' => 'required|string|max:255',
            'campaigns.*.amount' => 'required|numeric|min:0',
        ]);

        foreach ($validData['campaigns'] as $campaignData) {
            $project->campaigns()->create([
                'name' => $campaignData['name'],
                'amount' => $campaignData['amount'],
            ]);
        }

        return redirect()->route($this->routes['edit'], ['id' => $itemId])->with('message.success', $this->messages['edit.success']);
    }

    public function campaign_destroy($itemId, $campaigneId)
    {
        $this->projectService->destroyCampaign($campaigneId);
        return redirect()->route($this->routes['edit'], ['id' => $itemId])->with('message.success', 'The campaign was successfully removed');
    }

    public function campaign_update($itemId, $campaigneId)
    {
        $validData = request()->validate([
            'name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
        ]);
        $campaign = MarketingCampaign::findOrFail($campaigneId);
        $campaign->update($validData);

        return redirect()->route($this->routes['edit'], ['id' => $itemId])->with('message.success', $this->messages['edit.success']);
    }
}
