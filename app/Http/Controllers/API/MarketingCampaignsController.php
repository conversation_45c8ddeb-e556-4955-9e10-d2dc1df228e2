<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\MarketingCampaign;
use App\Models\MarketingProject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MarketingCampaignsController extends Controller
{
    /**
     * Get all marketing campaigns with their project names
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $campaigns = MarketingCampaign::select(
            'marketing__campaigns.id',
            'marketing__campaigns.name',
            'marketing__campaigns.marketing_project_id',
            'marketing__campaigns.amount',
            'marketing__campaigns.hash',
            'marketing__projects.name as project_name'
        )
        ->leftJoin('marketing__projects', 'marketing__campaigns.marketing_project_id', '=', 'marketing__projects.id')
        ->orderBy('marketing__projects.name')
        ->orderBy('marketing__campaigns.name')
        ->get();

        return response()->json($campaigns);
    }

    /**
     * Get a specific marketing campaign
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $campaign = MarketingCampaign::with('project')->findOrFail($id);
        return response()->json($campaign);
    }
}
