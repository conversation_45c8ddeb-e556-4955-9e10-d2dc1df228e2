<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Language;
use App\Models\QueryParamsDef;
use App\Models\Room;
use App\Models\SearchDefinition;
use App\Models\SEO;
use App\Services\AttachmentsService;
use App\Services\AttributesService;
use App\Services\GeographyService;
use App\Services\PropertiesService;
use App\Models\User;
use App\Services\DOMParserService;
use App\Services\MenuHelperService;
use App\Services\SEOContentService;
use Illuminate\Support\Facades\Auth;
use Log;
use DB;
use Share;

class ListingController extends Controller
{
    private $propertiesService;
    private $attributesService;
    private $geographyService;
    private $DOMParserService;
    private $attachmentsService;
    private $userCurrency;
    private $seoContentService;

    public function __construct(
        PropertiesService $propertiesService,
        AttachmentsService $attachmentsService,
        AttributesService $attributesService,
        GeographyService $geographyService,
        DOMParserService $DOMParserService,
        SEOContentService $seoContentService
    ) {
        $this->propertiesService = $propertiesService;
        $this->attachmentsService = $attachmentsService;
        $this->attributesService = $attributesService;
        $this->geographyService = $geographyService;
        $this->DOMParserService = $DOMParserService;
        $this->seoContentService = $seoContentService;
        $this->userCurrency = "QAR";
    }

    /**
     * @OA\Get(
     *     path="/listing",
     *     tags={"listing"},
     *     summary="Get listings - lite version",
     *     description="The list of the listings - lite version",
     *     operationId="getListingsLite",
     *     @OA\Parameter(
     *         name="ot",
     *         in="query",
     *         description="Listing Ad Type - rent | buy",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="t",
     *         in="query",
     *         description="Listing Property Type - apartments | villas | offices | land | penthouses | townhouses | challets | hotel-apart | villa-compound | retail",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="pf",
     *         in="query",
     *         description="Price from",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="number"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="pt",
     *         in="query",
     *         description="Price to",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="number"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="af",
     *         in="query",
     *         description="Area from",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="number"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="at",
     *         in="query",
     *         description="Area to",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="number"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="be",
     *         in="query",
     *         description="Bedrooms no",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="ba",
     *         in="query",
     *         description="Bathrooms no",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="fu",
     *         in="query",
     *         description="Furnishings - can be unfurnished, semi-furnished, fully-furnished, fully-equiped",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sort",
     *         in="query",
     *         description="Sort column.",
     *         example="Newest: sort=date&sort-dir=desc, Oldest: sort=date&sort-dir=asc, Price(low) sort=price&sort-dir=asc, Price(high) sort=price&sort-dir=desc",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sort-dir",
     *         in="query",
     *         description="Sort direction asc | desc",
     *         required=false,
     *         explode=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="pp",
     *         in="query",
     *         description="Records per page - default 12. If this is > 50, will return only 50",
     *         required=false,
     *         explode=true,
     *         @OA\Schema(
     *             type="number"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page no",
     *         required=false,
     *         @OA\Schema(
     *             type="number"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="mb",
     *         in="query",
     *         description="Map bounds. The `mb` parameter should contain the `northeast` and `southweast` geocoords of a rectangle selection on the map. If this is present, the query should return listings placed on the selection",
     *         required=false,
     *         @OA\JsonContent(ref="#/components/schemas/MapBounds")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/ListingLiteGetResponse"),
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid status value"
     *     )
     * )
     */
    public function index(GeographyService $geographyService)
    {
        $queryParams = request()->all();
        $qpLocale = isset($queryParams['locale']) ? $queryParams['locale'] : 'en';
        Log::info("ListingController.index ", ['ip' => request()->ip(), 'queryParams' => $queryParams]);

        $recPerPage = 24;
        if (isset($queryParams[QueryParamsDef::PER_PAGE]) && is_numeric($queryParams[QueryParamsDef::PER_PAGE])) {
            if (0 < $queryParams[QueryParamsDef::PER_PAGE] && $queryParams[QueryParamsDef::PER_PAGE] < 151) {
                $recPerPage = $queryParams[QueryParamsDef::PER_PAGE];
            }
        }

        $isShortStay = false;
        $shortStayRange = [];
        if (isset($queryParams['isShortStay']) && isset($queryParams['dateRange'])) {
            $rangeDates = explode(",", $queryParams['dateRange']);
            if (count($rangeDates) == 2) {
                $isShortStay = true;
                $shortStayRange = $rangeDates;
            }
        }

        $page = request()->get('page', 1);
        if ($isShortStay) {
            $startDate = $shortStayRange[0];
            $endDate = $shortStayRange[1];

            $availableRooms = Room::with(['snapshot'])
                ->where('isActive', 1)
                ->where('availabilityStartDate', '<=', $startDate)
                ->where('availabilityEndDate', '>=', $endDate)
                ->whereDoesntHave('bookings', function ($query) use ($startDate, $endDate) {
                    $query->where(function ($query) use ($startDate, $endDate) {
                        $query->where('startDate', '<=', $endDate)
                            ->where('endDate', '>=', $startDate);
                    });
                })
                ->has('snapshot')
                ->paginate($recPerPage, ['*'], 'page', $page); // Paginate the results


            $listingIds = [];
            $rooms = $availableRooms->items();
            $roomsCount = $availableRooms->total();
            foreach ($rooms as $item) {
                if (!in_array($item->snapshot->listing_id, $listingIds)) {
                    $listingIds[] = $item->snapshot->listing_id;
                }
            }

            $snapshotData = [];
            if (count($listingIds) > 0) {
                $snapshotData = $this->propertiesService->searchLite(
                    ['ids' => $listingIds],
                    null,
                    ['p.updated_at', 'DESC'],
                    $recPerPage * 100,
                    $qpLocale
                );

                $listItems = $snapshotData->items();
                $listItemsCount = $snapshotData->total();

                $imageTemplateName = 'list-item';
                if (request()->query->has('imageTemplate')) {
                    $imageTemplateName = request()->query->get('imageTemplate');
                }

                $preparedItems = [];
                foreach ($listItems as $item) {
                    $mappedItem = $this->propertiesService->mapSnapshotModelToListItem($item, $imageTemplateName, $qpLocale);
                    $preparedItems[] = $mappedItem;
                }

                $listingsWithRooms = [];
                foreach ($rooms as $availableRoom) {
                    $mappedListingArr = array_filter($preparedItems, function ($preparedItem) use ($availableRoom) {
                        return $preparedItem['id'] == $availableRoom->snapshot->listing_id;
                    });

                    $mappedListing = array_values($mappedListingArr)[0];
                    if (!is_null($mappedListing)) {
                        $mappedListing['available_room'] = $availableRoom->toArray();
                        $mappedListing['available_room']['roomPrice'] = $availableRoom->getPriceAsString();
                        $mappedListing['is_short_stay'] = true;
                    }
                    $listingsWithRooms[] = $mappedListing;
                }


                return response([
                    'data' => $listingsWithRooms ?? [],
                    'count' => $roomsCount
                ])
                    ->header('Access-Control-Allow-Origin', '*');
                // $snapshotData = $this->propertiesService->getSearchLiteQ(['ids' => $listingIds])->get()->toArray();
            }

            $availableRooms->getCollection()->each(function ($item) use ($snapshotData) {
                $snapshots = array_filter($snapshotData, function ($snapshotItem) use ($item) {
                    return $snapshotItem->asset_id == $item->propertyAssetId;
                });
                // // if(!count($snapshots)) {
                //     dd($snapshotData, $snapshots, $item);
                // // }

                $item->snapshot = count($snapshots) > 0 ? $this->propertiesService->mapSnapshotModelToListItem(reset($snapshots), 'list-item') : null;
            });
            return response()->json($availableRooms);
        }

        $paginator = $this->propertiesService->searchLite(
            $queryParams,
            null,
            ['p.updated_at', 'DESC'],
            $recPerPage,
            $qpLocale
        );

        $listItems = $paginator->items();
        $listItemsCount = $paginator->total();
        if (request()->has('listingId')) {
            $qpListing = array_filter($listItems, function ($item) {
                return $item->listing_id == request()->get('listingId');
            });

            if (!count($qpListing)) {
                $listingPaginator = $this->propertiesService->searchLite(
                    ['ids' => [request()->get('listingId')]]
                );

                $listItems = array_merge($listingPaginator->items(), $listItems);
                $listItemsCount++;
            }
        }
        $imageTemplateName = 'list-item';
        if (request()->query->has('imageTemplate')) {
            $imageTemplateName = request()->query->get('imageTemplate');
        }

        $preparedItems = [];
        foreach ($listItems as $item) {
            $preparedItems[] = $this->propertiesService->mapSnapshotModelToListItem($item, $imageTemplateName, $qpLocale);
        }

        $response = [
            'data' => $preparedItems ?? [],
            'count' => $listItemsCount
        ];

        if (request()->get('listingContent') == 'html') {
            $sortValue = '';
            if (!empty($queryParams['sort']) && !empty($queryParams['dir'])) {
                $sort = $queryParams['sort'];
                $dir = $queryParams['dir'];
                if ($sort == 'date') {
                    if ($dir == 'asc') {
                        $sortValue = 'oldest';
                    } else {
                        $sortValue = 'newest';
                    }
                } else {
                    if ($dir == 'asc') {
                        $sortValue = 'pricelow';
                    } else {
                        $sortValue = 'pricehigh';
                    }
                }
            }
            $routeData = $this->seoContentService->getRouteDataContent($queryParams);
            $isExclusiveSearch = isset($queryParams['is_exclusive']) && $queryParams['is_exclusive'] == 1;
            $resultsStr = $this->seoContentService->metaBuilderService->getListingSearchResultsString(null, $queryParams, $page, request()->get('locale'), true);
            $htmlContent = view('front.properties.list', ['paginator' => $paginator, 'resultsStr' => $resultsStr[0], 'resultsNo' => $listItemsCount, 'pageData' => ['listItems' => $response['data'], 'count' => $response['count']], 'breadcrumbs' => $routeData['breadcrumbs'], 'sortValue' => $sortValue, 'isExclusiveSearch' => $isExclusiveSearch]);
            $response['listingContent'] = "{$htmlContent}";
            $response['routeData'] = $routeData;
        }

        return response($response)
            ->header('Access-Control-Allow-Origin', '*');
    }

    public function indexMobile()
    {
        $queryParams = request()->all();
        $authorizationHeader = request()->header('Authorization');
        Log::info("ListingController.indexMobile ", ['ip' => request()->ip(), 'queryParams' => $queryParams]);
        if (!empty($authorizationHeader) && count(explode(" ", $authorizationHeader)) === 2) {
            $authToken = explode(" ", $authorizationHeader)[1];
            $user = User::where('api_token', $authToken)->first();
            if (!is_null($user)) {
                Auth::login($user);
            }
        }
        $qpLocale = isset($queryParams['locale']) ? $queryParams['locale'] : 'en';

        $search = SearchDefinition::where('a', json_encode($queryParams['a'] ?? []))
            ->when(isset($queryParams['offplan']), fn($query) => $query->where('offplan', $queryParams['offplan']))
            ->when(isset($queryParams['ot']), fn($query) => $query->where('ot', $queryParams['ot']))
            ->when(isset($queryParams['t']), fn($query) => $query->where('t', $queryParams['t']))
            ->when(isset($queryParams['pf']), fn($query) => $query->where('pf', $queryParams['pf']))
            ->when(isset($queryParams['pt']), fn($query) => $query->where('pt', $queryParams['pt']))
            ->when(isset($queryParams['fu']), fn($query) => $query->where('fu', $queryParams['fu']))
            ->when(isset($queryParams['ba']), fn($query) => $query->where('ba', $queryParams['ba']))
            ->when(isset($queryParams['be']), fn($query) => $query->where('be', $queryParams['be']))
            ->when(isset($queryParams['af']), fn($query) => $query->where('af', $queryParams['af']))
            ->when(isset($queryParams['at']), fn($query) => $query->where('at', $queryParams['at']))
            ->when(isset($queryParams['mb']), fn($query) => $query->where('mb', $queryParams['mb']))
            ->when(isset($queryParams['loc']), fn($query) => $query->where('loc', $queryParams['loc']))
        ->first();

        Log::info(json_encode($queryParams));

        $recPerPage = 12;
        if (isset($queryParams[QueryParamsDef::PER_PAGE]) && is_numeric($queryParams[QueryParamsDef::PER_PAGE])) {
            if (0 < $queryParams[QueryParamsDef::PER_PAGE] && $queryParams[QueryParamsDef::PER_PAGE] < 151) {
                $recPerPage = $queryParams[QueryParamsDef::PER_PAGE];
            }
        }

        $paginator = $this->propertiesService->searchLiteMobile(
            $queryParams,
            null,
            ['p.updated_at', 'DESC'],
            $recPerPage,
            $qpLocale
        );

        $listItems = $paginator->items();
        $listItemsCount = $paginator->total();
        if (request()->has('listingId')) {
            $qpListing = array_filter($listItems, function ($item) {
                return $item->listing_id == request()->get('listingId');
            });

            if (!count($qpListing)) {
                $listingPaginator = $this->propertiesService->searchLite(
                    ['ids' => [request()->get('listingId')]]
                );

                $listItems = array_merge($listingPaginator->items(), $listItems);
                $listItemsCount++;
            }
        }
        $imageTemplateName = 'mobile-list-item-small';
        if (request()->query->has('imageTemplate')) {
            $imageTemplateName = request()->query->get('imageTemplate');
        }

        $preparedItems = [];
        foreach ($listItems as $item) {
            $preparedItems[] = $this->propertiesService->mapSnapshotModelToMobileListItem($item, $imageTemplateName, $qpLocale);
        }

        return response([
            'data' => $preparedItems ?? [],
            'count' => $listItemsCount,
            'search_id' => $search ? $search->id : null,
        ])
            ->header('Access-Control-Allow-Origin', '*');
    }

    /**
     * @OA\Get(
     *     path="/listing/{id}",
     *     tags={"listing"},
     *     summary="Get listing by id",
     *     description="The listing details",
     *     operationId="getListing",
     *     @OA\Response(
     *         response=200,
     *         description="successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/ListingDetailsGetResponse"),
     *     ),
     *     security={
     *         {"auth": {"write:listing", "read:listing"}}
     *     }
     * )
     */
    public function showLite($propertyId)
    {
        try {
            $authorizationHeader = request()->header('Authorization');
            if (!empty($authorizationHeader) && count(explode(" ", $authorizationHeader)) === 2) {
                $authToken = explode(" ", $authorizationHeader)[1];
                $user = User::where('api_token', $authToken)->first();
                if (!is_null($user)) {
                    Auth::login($user);
                }
            }
            $snapshot = $this->propertiesService->getSnapshot($propertyId);

            if (is_null($snapshot)) {
                return response(null, 404);
            }

            $images = array_map(function ($imageName) use ($snapshot) {
                return [
                    'image_url' => imageRoute('mobile-listing-big', $imageName),
                    'is_primary' => $imageName === $snapshot->primary_image
                ];
            }, empty($snapshot->images) ? [] : json_decode($snapshot->images));

            $attributes = $this->attributesService->getSnapshotAttributes($snapshot);

            $filteredAttributes = $attributes->filter(function ($attribute) {
                if (!$attribute->attribute_definition_name) {
                    return false;
                }
                return in_array($attribute->attribute_definition_name, ['property-features-furnishings', 'property-features-build-up-area', 'property-features-bedrooms', 'property-features-bathrooms']);
            });

            $amenities = $attributes->filter(function ($attribute) {
                return $attribute->attribute_group === 'Amenities';
            })->map(function ($attribute) {
                return (object)['id' => $attribute->attribute_definition_id, 'value' => $attribute->attribute_definition];
            })->values()->toArray();

            $descriptionAttribute = $attributes->filter(function ($attribute) {
                if (!$attribute->attribute_definition_name) {
                    return false;
                }
                return $attribute->attribute_definition_name === 'description';
            })->first();

            $location = $this->geographyService->getById($snapshot->location_id);

            $locationObject = empty($location) ? null : [
                'id' => $location->id,
                'name' => $location->name,
                'path' => $location->path(),
                'description' => ''
            ];

            $author = User::with('brokerageLicenseUser')->where('id', $snapshot->created_by)->first();
            $agentObject = null;

            if ($author) {
                $broker = $author->brokerageLicenseUser;

                $agentObject = [
                    'id' => $author->id,
                    'email' => $broker ? $broker->email : $author->email,
                    'name' => $author->name,
                    'position' => $author->position,
                    'phone' => $broker ? $broker->phone : $author->phone,
                    'prefix_phone' => $broker ? $broker->prefix_phone : $author->prefix_phone,
                    'whatsapp_no' => $broker ? $broker->getCompletePhoneNo() : $author->getCompletePhoneNo(),
                    'profile_image' => !empty($author->profile_image) ? imageRoute('square-150', $author->profile_image) : null,
                    'uniq_id' => $author->uniq_id,
                ];
            }
            $localPropertyAttributes = $this->propertiesService->propertyAttributes;
            $locale = app()->getLocale() ?? 'en';

            $isSavedByUser = 0;
            if (!is_null(auth()->user())) {
                $dbRec = DB::table('user_x_saved_properties')->where('user_id', auth()->user()->id)->where('property_id', $snapshot->listing_id)->first();
                if (!is_null($dbRec)) {
                    $isSavedByUser = 1;
                }
            }

            $snapshotURL = MenuHelperService::createURLForSnapshot($snapshot);

            return [
                'id' => $snapshot->id,
                'listing_id' => $snapshot->listing_id,
                'ref_no' => $snapshot->ref_no,
                'title' => $snapshot->title,
                'ad_type' => $snapshot->ad_type,
                'property_type' => $snapshot->property_type,
                'property_type_url_value' => $snapshot->property_type_url_value,
                'location' => $locationObject,
                'unit_no' => $snapshot->unit_no,
                'currency' => strtoupper($this->userCurrency),

                // this is not calculated yet
                'currency_price' => (float)$snapshot->price,
                'price' => (float)$snapshot->price,
                'best_price' => (float)$snapshot->best_price,
                'geo_lat' => $snapshot->geo_lat,
                'geo_lon' => $snapshot->geo_lon,
                'images' => $images,
                'brochure' => empty($snapshot->brochure_path) ? null : [
                    'url' => route('properties.brochure.download', ['locale' => $locale, 'propertyWord' => __('property'), 'id' => $snapshot->listing_id, 'refNo' => $snapshot->ref_no]),
                    'title' => $snapshot->brochure_title
                ],
                'attributes' => array_values($filteredAttributes->map(function ($attribute) use ($localPropertyAttributes) {
                    $attributeValue = $this->getParsedAttributeValue($attribute);
                    return ['key' => $localPropertyAttributes[$attribute->attribute_definition_name], 'value' => $attributeValue];
                })->toArray()),
                'description' => is_null($descriptionAttribute) ? null : $this->DOMParserService->parseContent($descriptionAttribute->value_large),
                'agent' => $agentObject,
                'amenities' => $amenities,
                'saved_by_user' => $isSavedByUser,
                'whatsapp_message' => __("Hi, I want to know more details about this listing: listingURL", ['listingURL' => $snapshotURL]),
                'email_message' => __("Hi, I want to know more details about this listing: listingURL", ['listingURL' => $snapshotURL])
            ];
        } catch (\Exception $Ex) {
            return response($Ex->getMessage(), 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/listing/{id}/related",
     *     tags={"listing"},
     *     summary="Get related listings",
     *     description="The list of the related listings",
     *     operationId="getListingsRelatedLite",
     *     @OA\Response(
     *         response=200,
     *         description="successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/ListingLiteGetResponse"),
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid status value"
     *     )
     * )
     */
    public function related($propertyId)
    {
        $snapshot = $this->propertiesService->getSnapshot($propertyId);

        if (is_null($snapshot)) {
            return response(null, 404);
        }

        $params = [
            QueryParamsDef::OPERATION_TYPE => $snapshot->ad_type,
            QueryParamsDef::PROPERTY_TYPE => $snapshot->property_type_url_value,
            QueryParamsDef::BEDROOMS => $snapshot->bedrooms,
            QueryParamsDef::BATHROOMS => $snapshot->bathrooms,
        ];

        $paginator = $this->propertiesService->searchLite(
            $params,
            null,
            ['p.updated_at', 'DESC'],
            isset($queryParams[QueryParamsDef::PER_PAGE]) ? $queryParams[QueryParamsDef::PER_PAGE] : 6
        );

        $listItems = $paginator->items();
        $listItemsCount = $paginator->total();

        $preparedItems = [];
        foreach ($listItems as $item) {
            $preparedItems[] = $this->propertiesService->mapSnapshotModelToListItem($item);
        }

        return response([
            'data' => $preparedItems ?? [],
            'count' => $listItemsCount
        ])->header('Access-Control-Allow-Origin', '*');
    }

    public function sharePayload($listingId)
    {
        $validData = request()->validate([
            'listingURL' => 'required'
        ]);
        
        $socialMediaLinks = Share::page($validData['listingURL'], __("Hi, I found this listing. Please have a look and let me khow what you think!"))
            ->facebook()
            ->twitter()
            ->whatsapp()
            ->getRawLinks();


        return $socialMediaLinks;
    }

    public function seoContent($snapshotId)
    {
        $snapshot = $this->propertiesService->getSnapshot($snapshotId);

        if (is_null($snapshot)) {
            return response(null, 404);
        }

        $seoRecord = SEO::where('related_type', 'property')
            ->where('related_id', $snapshot->listing_id)
            ->first();

        if (is_null($seoRecord)) {
            return response(null, 204);
        }

        return $seoRecord->only(['id', 'title', 'keywords', 'description']);
    }

    public function seoObject($snapshotId)
    {
        $snapshot = $this->propertiesService->getSnapshot($snapshotId);

        if (is_null($snapshot)) {
            return response(null, 404);
        }

        $enSEORecord = SEO::where('related_type', 'property')
            ->where('related_id', $snapshot->listing_id)
            ->where(function ($qb) {
                $qb->where('language', '')
                    ->orWhereNull('language');
            })
            ->first();

        $arSEORecord = SEO::where('related_type', 'property')
            ->where('related_id', $snapshot->listing_id)
            ->where(function ($qb) {
                $qb->where('language', 'ar');
            })
            ->first();

        $enListingURL = MenuHelperService::createURLForSnapshot($snapshot);
        $arListingURL = MenuHelperService::createURLForSnapshot($snapshot, Language::AR);

        $seoRecord = [
            "en" => [
                "id" => null,
                "title" => "",
                "keywords" => "",
                "description" => "",
                "url" => $enListingURL,
                "listing_id" => $snapshot->listing_id
            ],
            "ar" => [
                "id" => null,
                "title" => "",
                "keywords" => "",
                "description" => "",
                "url" => $arListingURL,
                "listing_id" => $snapshot->listing_id
            ],
        ];

        if (!is_null($enSEORecord)) {
            $seoRecord["en"] = [
                "id" => $enSEORecord->id,
                "title" => $enSEORecord->title,
                "keywords" => $enSEORecord->keywords,
                "description" => $enSEORecord->description,
                "url" => $seoRecord["en"]["url"],
                "listing_id" => $snapshot->listing_id
            ];
        }
        if (!is_null($arSEORecord)) {
            $seoRecord["ar"] = [
                "id" => $arSEORecord->id,
                "title" => $arSEORecord->title,
                "keywords" => $arSEORecord->keywords,
                "description" => $arSEORecord->description,
                "url" => $seoRecord["ar"]["url"],
                "listing_id" => $snapshot->listing_id
            ];
        }
        return response($seoRecord);
    }

    public function postSeoObject($listingId)
    {
        $validData = request()->validate([
            'ar' => [
                'description' => 'nullable',
                'keywords' => 'nullable',
                'title' => 'nullable',
            ],
            'en' => [
                'description' => 'nullable',
                'keywords' => 'nullable',
                'title' => 'nullable',
            ]
        ]);

        $statusCode = 200;
        foreach (['en', 'ar'] as $language) {
            $dbRecord = SEO::where('related_type', 'property')
                ->where('related_id', $listingId)
                ->where(function ($qb) use ($language) {
                    if ($language == "en") {
                        $qb->where('language', '')
                            ->orWhereNull('language');
                    } else if ($language == "ar") {
                        $qb->where('language', 'ar');
                    }
                })
                ->first();

            if (!is_null($dbRecord)) {
                $dbRecord->title = $validData[$language]['title'];
                $dbRecord->description = $validData[$language]['description'];
                $dbRecord->keywords = $validData[$language]['keywords'];
            } else {
                $dbRecord = new SEO();
                $dbRecord->title = $validData[$language]['title'];
                $dbRecord->description = $validData[$language]['description'];
                $dbRecord->keywords = $validData[$language]['keywords'];
                $dbRecord->related_id = $listingId;
                $dbRecord->related_type = 'property';
                $dbRecord->language = $language == 'en' ? '' : $language;
                $statusCode = 201;
            }
            $dbRecord->save();
        }

        return response([], $statusCode);
    }

    /**
     * @OA\Get(
     *     path="/profile/listing-saved",
     *     tags={"profile"},
     *     summary="Get user saved listings",
     *     description="Get user saved listings",
     *     operationId="getUserSavedListing",
     *     @OA\Response(
     *         response=200,
     *         description="successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/ListingLiteGetResponse"),
     *     )
     * )
     */
    public function getProfileSavedListings()
    {
        $profile = auth()->user();

        $paginator = $this->propertiesService->getProfileSavedProperties($profile);
        $listItems = $paginator->items();
        $listItemsCount = $paginator->total();

        $preparedItems = [];
        foreach ($listItems as $item) {
            $preparedItems[] = $this->propertiesService->mapSnapshotModelToMobileListItem($item);
        }

        return [
            'data' => $preparedItems ?? [],
            'count' => $listItemsCount
        ];
    }

    /**
     * Add new listing to saved listings for the current user
     *
     * @OA\Post(
     *     path="/profile/listing-saved",
     *     tags={"profile"},
     *     operationId="addSavedListing",
     *     @OA\Response(
     *         response=200,
     *         description="Success"
     *     ),
     *     @OA\RequestBody(
     *         description="PropertyRequestPost",
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ListingSavedAddRequest")
     *     )
     * )
     */
    public function addProfileSavedListing()
    {
        $validData = request()->validate([
            'property_id' => 'required|exists:properties,id'
        ]);

        try {
            $existedBefore = $this->propertiesService->addProfileSavedProperties(auth()->user(), $validData['property_id']);
            return response(null, $existedBefore ? 200 : 201);
        } catch (\Exception $Ex) {
            return response($Ex->getMessage(), 500);
        }
    }

    /**
     * Remove a listing from saved listings for the current user
     *
     * @OA\Delete(
     *     path="/profile/listing-saved/{id}",
     *     tags={"profile"},
     *     operationId="deleteSavedListing",
     *     @OA\Response(
     *         response=404,
     *         description="Not Found"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server Error"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success"
     *     )
     * )
     */
    public function deleteProfileSavedListing($id)
    {
        try {
            $existedBefore = $this->propertiesService->deleteProfileSavedProperties(auth()->user(), $id);
            return response(null, $existedBefore ? 204 : 404);
        } catch (\Exception $Ex) {
            return response($Ex->getMessage(), 500);
        }
    }

    private function getParsedAttributeValue($attribute)
    {
        if (!empty($attribute->value_large)) {
            if (!empty($attribute->attribute_definition) && $attribute->attribute_definition == 'Description') {
                return $this->DOMParserService->parseContent($attribute->value_large);
            }
        }
        return $attribute->value;
    }
}
