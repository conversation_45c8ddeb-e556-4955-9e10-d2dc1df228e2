<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\LeadSource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Models\CacheKeys;

class LeadSourceController extends Controller
{
    /**
     * Get a specific lead source
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $leadSource = LeadSource::findOrFail($id);
        return response()->json($leadSource);
    }

    /**
     * Update a lead source
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $leadSource = LeadSource::findOrFail($id);
        
        $validData = $request->validate([
            'name' => 'required|unique:lead_sources,name,' . $id,
            'marketing_campaign_id' => 'nullable|exists:marketing__campaigns,id'
        ]);
        
        $leadSource->update($validData);
        
        // Clear cache
        Cache::forget(CacheKeys::LEAD_SOURCES);
        
        return response()->json($leadSource);
    }
}
