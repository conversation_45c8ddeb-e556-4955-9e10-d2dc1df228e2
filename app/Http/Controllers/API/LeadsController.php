<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\RecordLeadTask;
use App\Models\Crm\RolesDef;
use App\Models\Lead;
use App\Models\LeadActivity;
use App\Models\LeadActivityCallLog;
use App\Models\LeadActivityMeeting;
use App\Models\LeadAssignment;
use App\Models\Task;
use App\Models\User;
use App\Services\APILeadsService;
use App\Services\AuthorizationService;
use App\Services\EmailService;
use App\Services\LeadsExportService;
use App\Services\LeadsService;
use App\Services\LeadStatusService;
use App\Services\NotesService;
use App\Services\OperationHistoryService;
use App\Models\LeadInteractionTracking;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Session;
use Log;
use DB;
use Carbon\Carbon;

class LeadsController extends Controller
{

    private $APILeadsService;
    private $authorizationService;
    private $emailService;
    private $operationHistoryService;
    private $leadsService;
    private $leadStatusService;
    private $notesService;

    public function __construct(
        APILeadsService $APILeadsService,
        EmailService $emailService,
        AuthorizationService $authorizationService,
        OperationHistoryService $operationHistoryService,
        LeadsService $leadsService,
        LeadStatusService $leadStatusService,
        NotesService $notesService
    ) {
        $this->APILeadsService = $APILeadsService;
        $this->authorizationService = $authorizationService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->leadsService = $leadsService;
        $this->leadStatusService = $leadStatusService;
        $this->notesService = $notesService;
    }

    /**
     * @OA\Get(
     *     path="/leads",
     *     tags={"leads"},
     *     summary="Get leads",
     *     description="The leads of the current user",
     *     operationId="getLeads",
     *     @OA\Response(
     *         response=200,
     *         description="successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/LeadsGetResponse"),
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid status value"
     *     ),
     *     security={
     *         {"auth": {"write:leads", "read:leads"}}
     *     }
     * )
     */
    public function index(
        LeadsExportService $leServ
    ) {
        Log::info("API Get Leads request vars: ", request()->all());
        $offset = request()->query->get('start', 0);
        $limit = request()->query->get('length', 10);
        $extraConfig = $leServ->getExtraConfig(request());
        $items = $this->authorizationService->getVisibleItems(AuthorizationService::CATEG_LEADS, null, $offset, $limit, $extraConfig);

        $mappedItems = array_map(function ($item) {
            $completePhoneNo = null;
            if (!empty($item->contact_mobile_1)) {
                $completePhoneNo = getCompletePhoneNo($item->contact_prefix_mobile_1, $item->contact_mobile_1);
            } else if (!empty($item->contact_mobile_2)) {
                $completePhoneNo = getCompletePhoneNo($item->contact_prefix_mobile_2, $item->contact_mobile_2);
            }
            $item->complete_phone_no = $completePhoneNo;
            return $item;
        }, $items['items']);

        return [
            'data' => $mappedItems ?? [],
            'count' => $items['count']
        ];
        // $paginator = $this->APILeadsService->getPaginated($params);

        // $data = $paginator->map(function ($item) {
        //     return [
        //         'id' => $item->id,
        //         'email' => $item->email,
        //         'request_action' => $item->request_action,
        //         'ad_type' => $item->ad_type,
        //         'budget' => $item->budget,
        //         'platform' => $item->platform,
        //         'contact' => [
        //             'id' => $item->contact->id,
        //             'label' => $item->contact->label()
        //         ],
        //         'nationality' => is_null($item->nationality) ? null : [
        //             'id' => $item->nationality->id,
        //             'name' => $item->nationality->name
        //         ],
        //         'location' => is_null($item->location) ? null : [
        //             'id' => $item->location->id,
        //             'name' => $item->location->name,
        //             'path' => $item->location->path()
        //         ]
        //     ];
        // });


        // return [
        //     'data' => $data,
        //     'total' => $paginator->total()
        // ];
    }


    /**
     * Create new Lead
     *
     * @OA\Post(
     *     path="/leads",
     *     tags={"leads"},
     *     operationId="storeLeads",
     *     @OA\Response(
     *         response=422,
     *         description="Invalid input"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success"
     *     )
     * )
     */
    public function store(Request $request, EmailService $emailService)
    {
        $validData = $request->validate([
            "contact_id" => 'required|exists:contacts,id',
            'ad_type' => 'nullable',
            'budget' => 'nullable',
            'agent_id' => 'nullable|exists:users,id',
            'platform_from' => 'nullable',
            'status_id' => 'exists:lead_status,id',
            'remarks' => 'required_unless:status,14,15,23',
            'request.propertyTypes' => 'required|array',
            'request.amenities' => 'array',
            'request.balcony' => '',
            'request.bathrooms' => '',
            'request.bedrooms' => '',
            'request.isExclusiveOrHotDeal' => '',
            'request.kitchen' => '',
            'request.listingViews' => '',
            'request.marketingPlatforms' => '',
            'request.maxArea' => '',
            'request.maxPrice' => '',
            'request.minArea' => '',
            'request.minPrice' => '',
            'request.pantry' => '',
            'request.parking' => '',
            'request.operationType' => [
                'required',
                Rule::in('rent', 'sale')
            ],
            'request.requirements' => '',
        ]);

        Log::info('API Lead store data', $validData);

        try {
            $assignUser = null;
            if (!empty($validData['agent_id'])) {
                $assignUser = User::firstWhere('id', $validData['agent_id']);
            }
            $lead = new Lead();
            $lead->contact_id = $validData['contact_id'];
            $lead->requirements = $validData['request']['requirements'];

            $processableFilterMap = [
                'filter_operation_type' => 'operationType',
                'filter_bedrooms' => 'bedrooms',
                'filter_bathrooms' => 'bathrooms',
                'filter_min_area' => 'minArea',
                'filter_max_area' => 'maxArea',
                'filter_budget_min' => 'minPrice',
                'filter_budget_max' => 'maxPrice'
            ];

            $lead->filter_property_type = $validData['request']['propertyTypes'][0];
            foreach ($processableFilterMap as $k => $v) {
                if (array_key_exists($v, $validData['request']) && !empty($validData['request'][$v])) {
                    if (is_array($validData['request'][$v])) {
                        if (!empty($validData['request'][$v][0])) {
                            $lead->$k = $validData['request'][$v][0];
                        }
                    } else {
                        $lead->$k = $validData['request'][$v];
                    }
                }
            };

            $lead->created_by = auth()->user()->id;
            $lead->ad_type = $validData['request']['operationType'];
            $lead->property_type_id = $validData['request']['propertyTypes'][0];
            $lead->lead_status_id = $validData['status_id'];
            $leadRequestArr = [
                // 'be' => empty($validData['request']['bedrooms']) || count($validData['request']['bedrooms']) < 1 ? null : $validData['request']['bedrooms'][0],
                'be' => $validData['request']['bedrooms'],
            ];

            $processableArr = [
                'ot' => 'operationType',
                't' => 'propertyTypes',
                'a' => 'amenities',
                'ba' => 'bathrooms',
                'view' => 'listingViews',
                'mpt' => 'marketingPlatforms',
                'af' => 'minArea',
                'at' => 'maxArea',
                'par' => 'parking',
                'bal' => 'balcony',
                'pf' => 'minPrice',
                'pt' => 'maxPrice',
                'ki' => 'kitchen',
                'pantry' => 'pantry',
                'is_exclusive' => 'isExclusiveOrHotDeal',
            ];

            foreach ($processableArr as $k => $v) {
                if (array_key_exists($v, $validData['request'])) {
                    $leadRequestArr[$k] = $validData['request'][$v];
                }
            };

            $lead->leads_request = json_encode((object) $leadRequestArr);
            $lead->platform_from = $validData['platform_from'] ?? '';

            $lead->save();

            if (!empty($validData['agent_id']) && !is_null($assignUser)) {
                $this->leadsService->assignLeadToUser($lead, $assignUser);
            }

            $this->operationHistoryService->addOperationHistory($lead, "Created from mobile application");

            $emailService->createLeadEmail($lead);
            if (!empty($validData['remarks'])) {
                $this->operationHistoryService->addOperationHistory($lead, $validData['remarks']);
            }

            return response($lead, 201);
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage(), $Ex->getTrace());
            return response($Ex->getMessage(), 500);
        }
    }

    public function edit(Request $request, $leadId)
    {
        $validData = $request->validate([
            "contact_id" => 'required|exists:contacts,id',
            'ad_type' => 'nullable',
            'budget' => 'nullable',
            'agent_id' => 'nullable|exists:users,id',
            'platform' => 'nullable',
            'status_id' => 'exists:lead_status,id',
            'remarks' => 'required_unless:status,14,15,23',
            'request.propertyTypes' => 'required|array',
            'request.amenities' => 'array',
            'request.balcony' => '',
            'request.bathrooms' => '',
            'request.bedrooms' => '',
            'request.isExclusiveOrHotDeal' => '',
            'request.kitchen' => '',
            'request.listingViews' => '',
            'request.marketingPlatforms' => '',
            'request.maxArea' => '',
            'request.maxPrice' => '',
            'request.minArea' => '',
            'request.minPrice' => '',
            'request.pantry' => '',
            'request.parking' => '',
            'request.operationType' => [
                'required',
                Rule::in('rent', 'sale')
            ],
            'request.requirements' => '',
        ]);

        Log::info('API Lead edit data', $validData);

        $lead = Lead::find($leadId);
        $lead->contact_id = $validData['contact_id'];

        $processableFilterMap = [
            'filter_operation_type' => 'operationType',
            'filter_bedrooms' => 'bedrooms',
            'filter_bathrooms' => 'bathrooms',
            'filter_min_area' => 'minArea',
            'filter_max_area' => 'maxArea',
            'filter_budget_min' => 'minPrice',
            'filter_budget_max' => 'maxPrice',
            'requirements' => 'requirements'
        ];

        $lead->filter_property_type = $validData['request']['propertyTypes'][0];
        foreach ($processableFilterMap as $k => $v) {
            if (array_key_exists($v, $validData['request']) && !empty($validData['request'][$v])) {
                if (is_array($validData['request'][$v])) {
                    if (!empty($validData['request'][$v][0])) {
                        $lead->$k = $validData['request'][$v][0];
                    }
                } else {
                    $lead->$k = $validData['request'][$v];
                }
            }
        };

        $lead->created_by = auth()->user()->id;
        $lead->ad_type = $validData['request']['operationType'];
        $lead->property_type_id = $validData['request']['propertyTypes'][0];
        $lead->lead_status_id = $validData['status_id'];
        $leadRequestArr = [
            'ba' => empty($validData['request']['bathrooms']) || count($validData['request']['bathrooms']) < 1 ? null : $validData['request']['bathrooms'][0],
        ];

        $processableArr = [
            'ot' => 'operationType',
            't' => 'propertyTypes',
            'a' => 'amenities',
            'be' => 'bedrooms',
            'view' => 'listingViews',
            'mpt' => 'marketingPlatforms',
            'af' => 'minArea',
            'at' => 'maxArea',
            'par' => 'parking',
            'bal' => 'balcony',
            'pf' => 'minPrice',
            'pt' => 'maxPrice',
            'ki' => 'kitchen',
            'pantry' => 'pantry',
            'is_exclusive' => 'isExclusiveOrHotDeal',
        ];

        foreach ($processableArr as $k => $v) {
            if (array_key_exists($v, $validData['request'])) {
                $leadRequestArr[$k] = $validData['request'][$v];
            }
        };

        $lead->leads_request = json_encode((object) $leadRequestArr);

        // Check if there were actual changes to the lead fields before save
        $hasChanges = $lead->isDirty();

        $lead->save();

        // if ( $validData['agent_id'] != $lead-> !empty($validData['agent_id']) && !is_null($assignUser)) {
        //     $this->leadsService->assignLeadToUser($lead, $assignUser);
        // }

        $this->operationHistoryService->addOperationHistory($lead, "Updated via mobile application");

        if (!empty($validData['remarks'])) {
            $this->operationHistoryService->addOperationHistory($lead, $validData['remarks']);
        }

        // Track EDIT only if there were actual changes to the lead fields
        if ($hasChanges) {
            $action = LeadInteractionTracking::EDIT;
            $this->authorizationService->leadInteractionsTracking($lead->id, $action, null);
        }

        return response($lead, 200);
    }

    /**
     * @OA\Get(
     *     path="/leads/{id}",
     *     tags={"leads"},
     *     summary="Get a lead",
     *     description="Lead by id",
     *     operationId="getLead",
     *     @OA\Response(
     *         response=200,
     *         description="successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/LeadRead"),
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid status value"
     *     ),
     *     security={
     *         {"auth": {"write:leads", "read:leads"}}
     *     }
     * )
     */
    public function getOne($leadId)
    {
        $lead = Lead::with("operationHistory", "author", "latestAssignment", "latestAssignment.user", "contact", "leadStatus")->findOrFail($leadId);
        if ($lead->latestAssignment && $lead->latestAssignment->user) {
            $lead->latestAssignment->user->profile_image = $lead->latestAssignment->user->profile_image
                ? imageRoute('agent-mobile-big', $lead->latestAssignment->user->profile_image)
                : null;
            $lead->resolved_platform = $lead->leadPlatform();
            // $lead->lead_status = $lead->leadStatus();
        }
        return $lead;
    }


    /**
     * Get Lead Suggestions
     *
     * @OA\Get(
     *     path="/leads/{id}/suggestions",
     *     tags={"leads"},
     *     operationId="getLeadSuggestions",
     *     @OA\Response(
     *         response=422,
     *         description="Invalid input"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success"
     *     ),
     *     requestBody={"$ref": "#/components/requestBodies/LeadsSuggestions"},
     * )
     */
    public function leadSuggestions($id)
    {
        $suggestions = $this->APILeadsService->getSuggestions($id);
        return array_map(function ($s) {
            return [
                'id' => $s->id,
                'asset_id' => $s->asset_id,
                'ref_no' => $s->ref_no,
                'title' => $s->title,
                'price' => $s->price,
                'best_price' => $s->best_price,
                'bathrooms' => $s->bathrooms,
                'bedrooms' => $s->bedrooms,
                'built_up_area' => $s->built_up_area,
                //                'url' => route('inventory.update', ['id' => $s->asset_id]),
                'url' => "",
                "image_url" => ""
                //                'image_url' => Image::url('/images_cache/snapshots/' . $s->asset_id . '/' . $s->attachment_name, 200, 200, ['crop'])
            ];
        }, $suggestions);
    }

    /**
     * Delete a Lead
     *
     * @OA\Delete(
     *     path="/leads/{id}",
     *     tags={"leads"},
     *     operationId="deleteLead",
     *     @OA\Response(
     *         response=422,
     *         description="Invalid input"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success"
     *     ),
     *     requestBody=null,
     * )
     */
    public function delete($leadId)
    {
        return Lead::destroy($leadId);
    }

    public function deleteSoft($leadId)
    {
        $lead = Lead::where('id', $leadId)->firstOrFail();
        $this->operationHistoryService->addOperationHistory($lead, "Deleted by " . auth()->user()->name, auth()->user());
        $lead->delete();
        $action = LeadInteractionTracking::DELETED;
        $this->authorizationService->leadInteractionsTracking($leadId, $action, null);
        return response([], 200);
        // return Lead::delete($leadId);
    }

    /**
     * Assign a lead to an user
     *
     * @OA\Post(
     *     path="/leads/{id}/assign",
     *     tags={"leads"},
     *     operationId="assignLeads",
     *     @OA\Response(
     *         response=422,
     *         description="Invalid input"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success"
     *     ),
     *     requestBody={"$ref": "#/components/requestBodies/LeadsAssign"},
     * )
     */

    public function assignUserToLead($leadId)
    {
        $validData = request()->validate([
            'lead_id' => 'exists:leads,id',
        ]);

        $user = auth()->user();

        $leadAssignment = LeadAssignment::create([
            'user_id' => $user->id,
            'lead_id' => $leadId
        ]);

        $lead = Lead::where('id', $leadId)->first();
        $lead->status = 'hot';
        $lead->save();

        Session::flash('message.success', 'The lead was successfully assigned!');

        $destinationEmail = $user->email;
        $this->emailService->leadAssignedEmail($leadAssignment, $destinationEmail);
        return response()->json();
    }

    /**
     * @OA\Get(
     *     path="/leads/{id}/entries",
     *     tags={"leads"},
     *     summary="Get similar leads",
     *     description="Same leads by contact_id and email",
     *     operationId="entries",
     *     @OA\Response(
     *         response=200,
     *         description="successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/LeadRead"),
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid status value"
     *     ),
     *     security={
     *         {"auth": {"write:leads", "read:leads"}}
     *     }
     * )
     */
    public function entries($leadId)
    {
        $similarEntries = $this->APILeadsService->getSimilarEntries($leadId);
        return $similarEntries;
    }

    public function deleteReassignation($leadId)
    {
        $lead = Lead::with(['latestAssignment', 'reassignedByAgentRelation'])->firstWhere('id', $leadId);
        $user = auth()->user();
        $userIsOM = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $userIsReassignTarget = false;
        if (!$userIsOM) {
            $userIsReassignTarget = (!is_null($lead->latestAssignment) && $lead->latestAssignment->for_reassignment && $lead->latestAssignment->user_id == auth()->user()->id) || $lead->reassignedByAgentRelation->user_id == auth()->user()->id;
        }

        if ($userIsOM || $userIsReassignTarget) {
            // cancel reassignment
            $originalUserId = $lead->reassignedByAgentRelation->user_id;
            $lead->reassignedByAgentRelation->delete();
            $this->leadsService->assignLeadToUser($lead, User::where('id', $originalUserId)->first());
            $this->operationHistoryService->addOperationHistory($lead, "Lead reassignment was cancelled by [" . $user->name . "]", $user);
            return response([], 200);
        }

        return response([], 409);
    }

    public function recordCallLog($leadId)
    {
        $validData = request()->validate([
            'interactionType' => 'required',
            'callResponse' => 'required',
            'notes' => ''
        ]);

        try {
            DB::beginTransaction();
            $leadActivity = LeadActivity::create([
                "lead_id" => $leadId,
                "activity_type" => $validData['interactionType'],
                'created_by' => auth()->user()->id,
                'status' => 'completed'
            ]);

            if ($leadActivity->activity_type == 'call_log') {
                $callLog = LeadActivityCallLog::create([
                    "lead_activity_id" => $leadActivity->id,
                    "call_response" => $validData['callResponse'],
                    "notes" => $validData['notes']
                ]);

                if ($callLog->call_response == 'answered' || $callLog->call_response == 'not_answered') {
                    $callLog->load('leadActivityEntry', 'leadActivityEntry.lead');

                    $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'CONTINUING_DISCUSSION');
                    if ($callLog->call_response == 'not_answered') {
                        $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'NOT_ANSWERED');
                    }

                    $lead = $callLog->leadActivityEntry->lead;
                    $previousStatus = $lead->leadStatus;
                    $oldStatusLabel = 'NO STATUS';
                    if (!is_null($previousStatus)) {
                        $oldStatusLabel = $previousStatus->name;
                    }

                    if (!is_null($newStatus)) {
                        $newStatusLabel = $newStatus->name;
                    }

                    $this->leadsService->trackLeadStatusChange($lead, $newStatus);
                    $lead->lead_status_id = $newStatus->id;
                    $lead->save();
                    $remarksBody = "Call log: " . $callLog->call_response . ". Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "] ";
                    $lead->load('leadStatus');
                    $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());
                }
            }
            DB::commit();
            return response($callLog, 201);
        } catch (\Exception $Ex) {
            DB::rollBack();
            throw $Ex;
        }

        return $validData;
    }

    // public function recordMeeting($leadId)
    // {
    //     $validData = request()->validate([
    //         'interactionType' => 'required',
    //         'subject' => 'required',
    //         'meetingType' => ['sometimes', Rule::in(['', 'online', 'offline', 'office'])],
    //         'status' => ['required', Rule::in(['not_started', 'completed', 'in_progress'])],
    //         'notes' => '',
    //         'locationId' => '',
    //         'propertyId' => 'nullable',
    //         'reminderSet' => '',
    //         'startDate' => '',
    //         'startTime' => '',
    //         'endDate' => '',
    //         'endTime' => '',
    //     ]);

    //     try {
    //         DB::beginTransaction();
    //         $leadActivity = LeadActivity::create([
    //             "lead_id" => $leadId,
    //             "activity_type" => $validData['interactionType'],
    //             'created_by' => auth()->user()->id,
    //             'completion_date' => $validData['status'] == 'completed' ? now() : null,
    //             'reminder_set' => $validData['reminderSet'],
    //             'status' => $validData['status'],
    //         ]);

    //         $meetingData = [
    //             "lead_activity_id" => $leadActivity->id,
    //             'subject' => $validData['subject'],
    //             'meeting_type' => empty($validData['meetingType']) ? null : $validData['meetingType'],
    //             'notes' => $validData['notes'],
    //             'location_id' => $validData['locationId'],
    //             'property_id' => $validData['propertyId'],
    //             'start_date' => $validData['startDate'],
    //             'start_time' => $validData['startTime'],
    //             'end_date' => $validData['endDate'],
    //             'end_time' => $validData['endTime']
    //         ];
    //         $activityMeeting = LeadActivityMeeting::create($meetingData);

    //         if ($activityMeeting) {
    //             $activityMeeting->load('leadActivityEntry', 'leadActivityEntry.lead');
    //             $lead = $activityMeeting->leadActivityEntry->lead;
    //             $nextStatusLabel = !empty($validData['propertyId']) ? 'VIEWING_SCHEDULED' : 'MEETING_SCHEDULED';
    //             $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', $nextStatusLabel);

    //             $reminderData = [
    //                 'title' => 'Meeting with ' . $lead->contact->name,
    //                 'text' => $activityMeeting->notes,
    //                 'priority' => 'MEDIUM',
    //                 'reminder_email' => false,
    //                 'reminder_email_date' => null,
    //                 'reminder_type' => $newStatus->name
    //             ];

    //             if (!empty($activityMeeting->start_date)) {
    //                 $startDateStr = $activityMeeting->start_date;
    //                 if (!empty($activityMeeting->start_time)) {
    //                     $startDateStr .= ' ' . $activityMeeting->start_time;
    //                 }
    //                 $dueDate = Carbon::parse($startDateStr);

    //                 $reminderData['due_date'] = $dueDate;

    //                 if ($leadActivity->reminder_set) {
    //                     $reminderData['reminder_email'] = true;

    //                     $dateData = [
    //                         'start_date' => $activityMeeting->start_date,
    //                         'start_time' => $activityMeeting->start_time,
    //                     ];
    //                     $i5MinutesBeforeData = $this->calculate15MinutesBefore($dateData);

    //                     if (!is_null($i5MinutesBeforeData)) {
    //                         $reminderData['reminder_email_date'] = $i5MinutesBeforeData->format("Y-m-d H:i");
    //                     }
    //                 }
    //             } else {
    //                 $reminderData['due_date'] = now();
    //             }
    //             $this->notesService->createReminderForLead($lead, $reminderData);


    //             $previousStatus = $lead->leadStatus;
    //             $oldStatusLabel = 'NO STATUS';
    //             if (!is_null($previousStatus)) {
    //                 $oldStatusLabel = $previousStatus->name;
    //             }

    //             if (!is_null($newStatus)) {
    //                 $newStatusLabel = $newStatus->name;
    //             }

    //             $this->leadsService->trackLeadStatusChange($lead, $newStatus);
    //             $lead->lead_status_id = $newStatus->id;
    //             $lead->save();
    //             $remarksBody = "Meeting scheduled. Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "] ";
    //             $lead->load('leadStatus');
    //             $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());
    //         }

    //         DB::commit();
    //         return response($activityMeeting, 201);
    //     } catch (\Exception $Ex) {
    //         DB::rollBack();
    //         throw $Ex;
    //     }
    // }

    // public function patchCallLog($leadId, $callLogId)
    // {
    //     $validData = request()->validate([
    //         'callResponse' => 'required',
    //         'notes' => ''
    //     ]);
    //     $mappedData = [
    //         'call_response' => $validData['callResponse'],
    //         'notes' => $validData['notes']
    //     ];
    //     $callLog = LeadActivityCallLog::where('id', $callLogId)->firstOrFail();
    //     $callLog->update($mappedData);

    //     return $callLog;
    // }

    // public function patchMeeting($leadId, $meetingId)
    // {
    //     $validData = request()->validate([
    //         'subject' => 'required',
    //         'meetingType' => ['sometimes', Rule::in(['', 'online', 'offline', 'office'])],
    //         'status' => ['required', Rule::in(['not_started', 'completed', 'in_progress'])],
    //         'notes' => 'nullable',
    //         'locationId' => 'nullable',
    //         'propertyId' => 'nullable',
    //         'reminderSet' => 'nullable',
    //         'startDate' => 'nullable',
    //         'startTime' => 'nullable',
    //         'endDate' => 'nullable',
    //         'endTime' => 'nullable',
    //     ]);

    //     $activityMeeting = LeadActivityMeeting::with(['leadActivityEntry'])->where('id', $meetingId)->firstOrFail();

    //     try {
    //         DB::beginTransaction();
    //         $meetingPatchData = [
    //             'subject' => $validData['subject'],
    //             'meeting_type' => empty($validData['meetingType']) ? null : $validData['meetingType'],
    //             'notes' => $validData['notes'],
    //             'location_id' => $validData['locationId'],
    //             'property_id' => $validData['propertyId'],
    //             'start_date' => $validData['startDate'],
    //             'start_time' => $validData['startTime'],
    //             'end_date' => $validData['endDate'],
    //             'end_time' => $validData['endTime']
    //         ];

    //         $activityMeeting->leadActivityEntry->status = $validData['status'];
    //         $activityMeeting->leadActivityEntry->reminder_set = $validData['reminderSet'];

    //         if (empty($activityMeeting->leadActivityEntry->completion_date) && $validData['status'] == 'completed') {
    //             $activityMeeting->leadActivityEntry->completion_date = now();
    //         }
    //         $activityMeeting->leadActivityEntry->save();

    //         $activityMeeting->update($meetingPatchData);
    //         DB::commit();
    //         return response($activityMeeting, 200);
    //     } catch (\Exception $Ex) {
    //         DB::rollBack();
    //         throw $Ex;
    //     }
    // }

    public function recordTask(RecordLeadTask $request, $leadId)
    {
        $validData = $request->validated();

        try {
            DB::beginTransaction();
            $dueDate = null;
            if ($validData['type'] == 'Call Log') {
                $dueDate = now();
            }
            $task = Task::create([
                "object_id" => $leadId,
                "object_type" => 'lead',
                "meeting_type" => $validData['meeting_type'],
                "type" => $validData['type'],
                "call_response" => $validData['call_response'],
                "call_notes" => $validData['call_notes'],
                'created_by' => auth()->user()->id,
                'property_id' => $validData['property_id'] ?? null,
                'location_id' => $validData['location_id'] ?? null,
                'subject' => ($validData['type'] == 'Call log' ? 'Call log' : $validData['subject']) ?? null,
                'status' => $validData['status'] ?? 'completed',
                'notes' => $validData['notes'] ?? null,
                'start_date' => $validData['start_date'] ?? null,
                'start_time' => $validData['start_time'] ?? null,
                'end_date' => $validData['end_date'] ?? null,
                'end_time' => $validData['end_time'] ?? null,
                'reminder_set' => $validData['reminder_set'] ?? null,
                'due_date' => null,
            ]);

            $taskCount = Task::where('object_id', $leadId)
                ->where('object_type', 'lead')
                ->count();
            $lead = Lead::where('id', $leadId)->first();
            if (($taskCount <= 1) && ($task->call_response == 'answered' || $task->call_response == 'not_answered' || $task->call_response == 'inactive' || $task->call_response == 'not_interested')) {

                $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'CONTINUING_DISCUSSION');
                if ($task->call_response == 'not_answered') {
                    $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'NOT_ANSWERED');
                }
                if ($task->call_response == 'inactive') {
                    $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'INACTIVE');
                }
                if ($task->call_response == 'not_interested') {
                    $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'NOT_INTERESTED');
                }
                $previousStatus = $lead->leadStatus;
                $oldStatusLabel = 'NO STATUS';
                if (!is_null($previousStatus)) {
                    $oldStatusLabel = $previousStatus->name;
                }

                $newStatusLabel = '';
                if (!is_null($newStatus)) {
                    $newStatusLabel = $newStatus->name;
                }

                $this->leadsService->trackLeadStatusChange($lead, $newStatus);
                $lead->lead_status_id = !is_null($newStatus) ? $newStatus->id : null;
                $lead->last_contact_date = new \DateTime();
                $lead->save();
                $remarksBody = "Call log: " . $task->call_response . ". Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "].
                                NOTES: " . ($task->call_notes ? $task->call_notes : 'N/A');
                $lead->load('leadStatus');
                $task->lead = $lead;
                $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());
            } else {
                $lead->last_contact_date = new \DateTime();
                $lead->save();
            }

            $leadNotes = isset($validData['notes']) ? 'Notes: ' . $validData['notes'] : '';
            $leadNotes .= isset($validData['call_notes']) ? 'Call Notes: ' . $validData['call_notes'] : '';
            $this->operationHistoryService->addOperationHistory($lead, $leadNotes, auth()->user());
            $action = LeadInteractionTracking::CALL_LOG;
            $this->authorizationService->leadInteractionsTracking($lead->id, $action, null);

            //     $lead = $task->lead;
            //     $dueDate = null;
            //     $nextStatusLabel = $task->type == 'Viewing' ? 'VIEWING_SCHEDULED' : 'MEETING_SCHEDULED';
            //     $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', $nextStatusLabel);

            //     $eventData = [
            //         'title' => $task->subject . ' (' . $lead->contact->name . ')',
            //         'text' => $task->notes,
            //         'priority' => 'MEDIUM',
            //         'reminder_email' => false,
            //         'reminder_email_date' => null,
            //         'reminder_type' => $task->type === 'Meeting' ? 'MEETING_SCHEDULED' : 'VIEWING_SCHEDULED'
            //     ];

            //     if (!empty($task->start_date)) {
            //         $startDateStr = $task->start_date;
            //         if (!empty($task->start_time)) {
            //             $startDateStr .= ' ' . $task->start_time;
            //         }
            //         $dueDate = Carbon::parse($startDateStr);
            //         $eventData['due_date'] = $dueDate;

            //         if ($task->reminder_set) {
            //             $eventData['reminder_email'] = true;

            //             $dateData = [
            //                 'start_date' => $task->start_date,
            //                 'start_time' => $task->start_time,
            //             ];
            //             $i5MinutesBeforeData = $this->calculate15MinutesBefore($dateData);

            //             if (!is_null($i5MinutesBeforeData)) {
            //                 $eventData['reminder_email_date'] = $i5MinutesBeforeData->format("Y-m-d H:i");
            //             }
            //         }
            //     } else {
            //         $eventData['due_date'] = now();
            //     }
            //     if (!is_null($dueDate)) {
            //         $task->due_date = $dueDate;
            //         $task->save();
            //     }
            //     if ($task->status != 'completed') {
            //         $this->notesService->createReminderForLead($lead, $eventData);
            //     }

            //     $previousStatus = $lead->leadStatus;
            //     $oldStatusLabel = 'NO STATUS';
            //     if (!is_null($previousStatus)) {
            //         $oldStatusLabel = $previousStatus->name;
            //     }

            //     if (!is_null($newStatus)) {
            //         $newStatusLabel = $newStatus->name;
            //     }

            //     $this->leadsService->trackLeadStatusChange($lead, $newStatus);
            //     $lead->lead_status_id = $newStatus->id;
            //     $lead->save();
            //     $remarksBody = "Meeting scheduled. Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "] ";
            //     $lead->load('leadStatus');
            //     $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());
            //     $task->load('lead', 'lead.leadStatus');
            // }
            DB::commit();
            return response($task, 201);
        } catch (\Exception $Ex) {
            DB::rollBack();
            throw $Ex;
        }
    }

    public function updateTask(RecordLeadTask $request, $leadId)
    {
        $validData = $request->validated();
        $task = Task::where('id', $validData['id'])->firstOrFail();

        $dataToUpdate = [
            "action_type" => $validData['action_type'],
            "call_response" => $validData['call_response'],
            "call_notes" => $validData['call_notes'],
            'property_id' => $validData['property_id'] ?? null,
            'location_id' => $validData['location_id'] ?? null,
            'subject' => $validData['subject'] ?? null,
            'status' => $validData['status'] ?? 'completed',
            'notes' => $validData['notes'] ?? null,
            'start_date' => $validData['start_date'] ?? null,
            'start_time' => $validData['start_time'] ?? null,
            'end_date' => $validData['end_date'] ?? null,
            'end_time' => $validData['end_time'] ?? null,
            'reminder_set' => $validData['reminder_set'] ?? null,
        ];

        if (in_array($task->type, ['Meeting', 'Viewing'])) {
            $dueDate = null;
            if (!empty($validData['start_date'])) {
                $startDateStr = $validData['start_date'];
                if (!empty($validData['start_time'])) {
                    $startDateStr .= ' ' . $validData['start_time'];
                }
                $dueDate = Carbon::parse($startDateStr);
            }

            $dataToUpdate['due_date'] = $dueDate;
        }

        $task->update($dataToUpdate);

        $targetObject = $task->object_type == 'contact' ? $task->contact : $task->lead;
        if (!is_null($targetObject)) {
            $notes = isset($validData['notes']) ? 'Notes: ' . $validData['notes'] : '';
            $notes .= isset($validData['call_notes']) ? 'Call Notes: ' . $validData['call_notes'] : '';
            $this->operationHistoryService->addOperationHistory($targetObject, $notes, auth()->user());
        }

        return response(['msg' => 'Ok']);
    }

    public function leadTasks($leadId)
    {
        $tasks = Task::with(['property:id,ref_no', 'lead', 'lead.contact:id,name', 'lead.leadStatus:id,name', 'author:id,name'])->orderBy('updated_at', 'DESC')->where(['object_type' => 'lead', 'object_id' => $leadId])->get();
        return $tasks;
    }

    public function loadLeadActivity($leadId)
    {
        $activity = LeadActivity::with(['leadActivityCallLog', 'leadActivityMeeting', 'leadActivityMeeting.property', 'author', 'lead.contact'])->where('lead_id', $leadId)->orderBy('updated_at', 'DESC')->limit(100)->get();

        return $activity;
    }

    // private function calculate15MinutesBefore($data)
    // {
    //     // Ensure the 'start_date' is provided
    //     if (empty($data['start_date'])) {
    //         return null;
    //     }

    //     // Set the base date
    //     $dateString = $data['start_date'];

    //     // Check if 'start_time' is provided and append it to the date
    //     if (!empty($data['start_time'])) {
    //         $dateString .= ' ' . $data['start_time'];
    //     }

    //     // Create a Carbon instance from the combined date and time string
    //     $carbonDate = Carbon::parse($dateString);

    //     // Subtract 15 minutes
    //     $resultDate = $carbonDate->subMinutes(15);

    //     // Return or format the result
    //     return $resultDate;
    // }
}
