<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\AgentContact;
use App\Models\Contact;
use App\Models\Lead;
use App\Models\LeadAssignment;
use App\Models\QueryParamsDef;
use App\Models\Property;
use App\Models\User;
use App\Models\MarketingParams;
use App\Services\ContactsService;
use App\Services\EmailService;
use App\Services\OperationHistoryService;
use App\Services\PropertiesService;

class APIPropertiesController extends Controller
{
    private $propertiesService;
    private $emailService;
    private $operationHistoryService;
    private $contactsService;

    public function __construct(
        PropertiesService $propertiesService,
        EmailService $emailService,
        OperationHistoryService $operationHistoryService,
        ContactsService $contactsService
    ) {
        $this->propertiesService = $propertiesService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->contactsService = $contactsService;
    }

    public function properties()
    {
        $searchParams = request()->query();
        $orderParams = [];
        $perPage = request()->get(QueryParamsDef::PER_PAGE, 10);
        return $this->propertiesService->search($searchParams, 0, $orderParams, $perPage);
    }

    public function property($id)
    {
        return Property::where('id', $id)->firstOrFail();
    }

    public function agentContact()
    {
        // Create validation rules with whitelisted marketing parameters
        $validationRules = [
            'name' => 'required',
            'email' => 'required',
            'phone' => '',
            'prefix' => '',
            'message' => 'required',
            'propertyId' => 'required',
            'agentId' => 'required'
        ];

        // Add all whitelisted marketing parameters to validation rules
        $marketingParams = array_fill_keys(MarketingParams::WHITELISTED_URL_PARAMS, '');
        $validationRules = array_merge($validationRules, $marketingParams);

        $validData = request()->validate($validationRules);
        $snapshot = $this->propertiesService->getSnapshot($validData['propertyId']);
        if (!is_null($snapshot)) {
            $broker = User::where('id', $validData['agentId'])->firstOrFail();

            $agentContactEntry = AgentContact::create([
                'name' => $validData['name'],
                'email' => $validData['email'],
                'phone' => $validData['phone'],
                'prefix' => $validData['prefix'],
                'message' => $validData['message'],
                'listing_id' => $snapshot->listing_id,
                'remote_addr' => getenv('REMOTE_ADDR')
            ]);

            $destinationEmail = $validData['email'];
            $this->emailService->sendEmailToBroker($broker, $snapshot, $validData);
            $this->emailService->sendEmailToMessageAgentContactSender($destinationEmail, $snapshot);

            $existingContactByEmail = $this->contactsService->getContactsByEmail($validData['email']);

            $existingContactByPhone = collect([]);
            if (!empty($validData['phone'])) {
                $existingContactByPhone = $this->contactsService->getContactsByPhoneNumber($validData['phone']);
            }

            $existingMasterContact = $existingContactByEmail->whereNull('master_contact_id')->first();
            if (is_null($existingMasterContact)) {
                $existingContact = $existingContactByEmail->first();
            } else {
                $existingContact = $existingMasterContact;
            }

            if (is_null($existingContact)) {
                $existingMasterContact = $existingContactByPhone->whereNull('master_contact_id')->first();
                if (is_null($existingMasterContact)) {
                    $existingContact = $existingContactByPhone->first();
                } else {
                    $existingContact = $existingMasterContact;
                }
            }

            // try to save also this contact, even if is duplicate, because it may contain some data that is not present in main contact
            $contact = Contact::create([
                'name' => $validData['name'],
                'email_1' => $validData['email'],
                'prefix_mobile_1' => $validData['prefix'],
                'mobile_1' => $validData['phone'],
                'master_contact_id' => is_null($existingContact) ? null : $existingContact->id,
                'is_master_contact' => is_null($existingContact) ? true : null
            ]);

            $leadsRequestObj = (object)[];

            if ($snapshot->ad_type) {
                $leadsRequestObj->ot =  $snapshot->ad_type;
            }
            if ($snapshot->property_type_id) {
                $leadsRequestObj->t =  $snapshot->property_type_id;
            }
            if ($snapshot->bedrooms) {
                $leadsRequestObj->be =  $snapshot->bedrooms;
            }
            if ($snapshot->bathrooms) {
                $leadsRequestObj->ba =  $snapshot->bathrooms;
            }
            if ($snapshot->location_id) {
                $leadsRequestObj->loc =  $snapshot->location_id;
            }

            // Collect marketing parameters from the request
            $leadMetadata = [];
            foreach (MarketingParams::WHITELISTED_URL_PARAMS as $param) {
                if (isset($validData[$param]) && !empty($validData[$param])) {
                    $leadMetadata[$param] = $validData[$param];
                }
            }

            $lead = Lead::create([
                'contact_id' => $contact->id,
                'agent_contact_entry_id' => $agentContactEntry->id,
                'platform_from' => '1',
                'lead_status_id' => '11',
                'filter_operation_type' => $snapshot->ad_type,
                'filter_property_type' => $snapshot->property_type_id,
                'filter_bedrooms' => $snapshot->bedrooms,
                'filter_bathrooms' => $snapshot->bathrooms,
                'location_id' => $snapshot->location_id,
                'inquired_ref_no' => $snapshot->ref_no,
                'leads_request' => json_encode($leadsRequestObj),
                'lead_metadata' => $leadMetadata, // Add marketing parameters to lead metadata
                'utm_source' => isset($validData['utm_source']) ? $validData['utm_source'] : null,
                'utm_medium' => isset($validData['utm_medium']) ? $validData['utm_medium'] : null,
                'utm_campaign' => isset($validData['utm_campaign']) ? $validData['utm_campaign'] : null,
                'utm_term' => isset($validData['utm_term']) ? $validData['utm_term'] : null,
                'utm_content' => isset($validData['utm_content']) ? $validData['utm_content'] : null,
            ]);

            LeadAssignment::create([
                'user_id' => $broker->id,
                'lead_id' => $lead->id,
            ]);

            $this->operationHistoryService->addOperationHistory($lead, 'The lead came from the website', $broker);
            $this->operationHistoryService->addOperationHistory($lead, 'The lead is assigned to ' .  $broker->name, $broker);

            return response(null, 200);
        }

        return response(null, 412);
    }
}
