<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeadSource extends Model
{
    use HasFactory;
    protected $fillable = ["name", "marketing_campaign_id"];

    /**
     * Get the marketing campaign associated with the lead source
     */
    public function marketingCampaign()
    {
        return $this->belongsTo(MarketingCampaign::class);
    }
}
