<?php

namespace App\Models\Crm;

use App\Models\Prototype\Model;
use App\Models\Contact;
use App\Models\Invoice;
use App\Models\Lead;
use App\Models\OperationHistory;
use App\Models\Property;
use App\Models\User;
use Illuminate\Database\Eloquent\SoftDeletes;

class Deal extends Model
{
    const STATUS_PENDING = 'pending';
    const STATUS_REJECTED = 'rejected';
    const STATUS_APPROVED = 'approved';
    const STATUS_FINALIZED = 'finalized';

    protected $fillable = [
        "type",
        "property_id",
        "unit_number",
        "price",
        "deposit",
        "commission_landlord",
        "commission_client",
        "start_date",
        "end_date",
        "status",
        "comments",
        "owner_invoice_no",
        "client_invoice_no",
        "shared_deals",
        "listing_agent_id",
        "referred_listing_agent_id",
        "closing_agent_id",
        "referred_closing_agent_id",
        "listing_agent_shared_commission",
        "referred_listing_agent_shared_commission",
        "closing_agent_shared_commission",
        "referred_closing_agent_shared_commission",
        "listing_agent_cash_in",
        "referred_listing_agent_cash_in",
        "closing_agent_cash_in",
        "referred_closing_agent_cash_in",
        "listing_agent_month",
        "referred_listing_agent_month",
        "closing_agent_month",
        "referred_closing_agent_month"
    ];

    protected $dates = ['deleted_at'];

    use SoftDeletes;

    public function getRefNo() {
        return "D" . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    public function property()
    {
        return $this->hasOne(Property::class, "id", "property_id")->withTrashed();
    }

    public function lead()
    {
        return $this->hasOne(Lead::class, "id", "lead_id");
    }

    public function client()
    {
        return $this->hasOne(Contact::class, "id", "client_id");
    }

    public function owner()
    {
        return $this->hasOne(Contact::class, "id", "owner_id");
    }

    public function author()
    {
        return $this->hasOne(User::class, "id", "created_by");
    }

    public function closingAgent()
    {
        return $this->hasOne(User::class, "id", "closing_agent_id");
    }

    public function closingReferralAgent()
    {
        return $this->hasOne(User::class, "id", "closing_referral_agent_id");
    }

    public function listingAgent()
    {
        return $this->hasOne(User::class, "id", "listing_agent_id");
    }

    public function referredListingAgent()
    {
        return $this->hasOne(User::class, "id", "referred_listing_agent_id");
    }

    public function agentCommission()
    {
        return $this->belongsToMany(User::class, 'deals_agents', 'deal_id', 'agent_id')
            ->withPivot('agent_type')
            ->withTimestamps();
    }

    public function getFormsAttribute($value)
    {
        return is_array($value) ? $value : json_decode($value ?? "[]");
    }

    public function getEmailClosingAgent()
    {
        if ($this->shared_deals && !is_null($this->closingAgent)) {
            return $this->closingAgent;
        }
        return $this->author;
    }

    public function getEmailListingAgent()
    {
        if ($this->shared_deals && !is_null($this->listingAgent)) {
            return $this->listingAgent;
        }
        if (!is_null($this->property) && !is_null($this->property->author)) {
            return $this->property->author;
        }
        return null;
    }

    public function operationHistory()
    {
        return $this->hasMany(OperationHistory::class, 'model_id')->where('model_type', Deal::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'deal_id', 'id');
    }
}
