<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LeadStatus extends Model
{
    const STATUS_COLD_ID = 1;
    const STATUS_WARM_ID = 5;
    const STATUS_HOT_ID = 10;

    const STATUS_COLD_LABEL = 'COLD';
    const STATUS_WARM_LABEL = 'WARM';
    const STATUS_HOT_LABEL = 'HOT';

    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';

    const STATUS_COLD = 'COLD';
    const STATUS_WARM = "WARM";
    const STATUS_HOT = "HOT";
    const STATUS_NEW = "NEW";
    const STATUS_INITIAL_CONTACT = "INITIAL_CONTACT";
    const STATUS_NOT_ANSWERED = "NOT_ANSWERED";
    const STATUS_CONTINUING_DISCUSSION = "CONTINUING_DISCUSSION";
    const STATUS_OFFER_SENT = "OFFER_SENT";
    const STATUS_MEETING_SCHEDULED = "MEETING_SCHEDULED";
    const STATUS_VIEWING_SCHEDULED = "VIEWING_SCHEDULED";
    const STATUS_OTHER_OPTIONS = "OTHER_OPTIONS";
    const STATUS_FOLLOW_UP = "FOLLOW_UP";
    const STATUS_OFFER_NEGOTIATION = "OFFER_NEGOTIATION";
    const STATUS_CONTRACT_PENDING = "CONTRACT_PENDING";
    const STATUS_CLOSED_WON = "CLOSED_WON";
    const STATUS_POST_CLOSURE = "POST_CLOSURE";
    const STATUS_NOT_QUALIFIED = "NOT_QUALIFIED";
    const STATUS_GHOSTED = "GHOSTED";
    const STATUS_CLOSED_LOSE = "CLOSED_LOSE";
    const STATUS_NEW_R = "NEW_R";
    const STATUS_FAR_MOVING_DECISION = "FAR_MOVING_DECISION";

    protected $table = 'lead_status';
}
