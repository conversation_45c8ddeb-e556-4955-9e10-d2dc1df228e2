<?php

namespace App\Models;

use App\Models\Prototype\Model;
use App\Models\Crm\Deal;

class InvoicePayment extends Model
{
    protected $table = 'invoice_payments';

    protected $fillable = [
        'invoice_id',
        'invoice_number',
        'invoice_date',
        'documents',
        'proof_no',
        'payment_documents',
        'document_type',
        'amount',
        'payment_date',
        'payment_type',
        'is_client',
        'cashed_in'
    ];

    protected $casts = [
        'documents' => 'array',
        'payment_documents' => 'array',
        'is_client' => 'boolean',
        'cashed_in' => 'boolean'
    ];

    public function invoice() {
        return $this->belongsTo(Invoice::class, 'invoice_id', 'id');
    }

    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }
}
