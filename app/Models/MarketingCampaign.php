<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MarketingCampaign extends Model{
    protected $table = 'marketing__campaigns';
    protected $fillable = ['name', 'marketing_project_id', 'created_by', 'amount', 'hash'];
    
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($campaign) {
            $campaign->hash = Str::random(16);
        });
    }
    
    public function project()
    {
        return $this->belongsTo(MarketingProject::class);
    }
}
