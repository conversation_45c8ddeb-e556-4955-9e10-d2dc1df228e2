<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeadInteractionTracking extends Model
{
    const CREATED = 'created';
    const UPDATED = 'updated';
    const DELETED = 'deleted';
    const ASSIGNED = 'assigned';
    const UNASSIGNED = 'unassigned';
    const UPDATE_STATUS = 'update_status';
    const REMARKS_ADDED = 'remarks_added';
    const ADD_REMINDER = 'add_reminder';
    const CALL_LOG = 'call_log';
    const CREATE_DEAL = 'create_deal';
    const NEW_NOT_UPDATED_STATUS = 'new_not_updated_status';
    const MOVED_TO_MASTERLIST_7DAYS = 'moved_to_masterlist_7days';

    use HasFactory;

    protected $table = 'lead_interactions_tracking';

    protected $fillable = [
        'lead_id',
        'user_id',
        'action',
        'created_by',
    ];
    public $timestamps = true;


    public function lead()
    {
        return $this->belongsTo(Lead::class);
    }
}
