<?php

namespace App\Models;

use App\Models\Crm\ContactsListTag;
use App\Models\DTO\GenericGlobalSearchResult;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'client',
        'email',
        'position',
        'phone',
        'password',
        'api_token',
        'notification_token',
        'profile_image',
        //'position',
        'languages',
        'short_bio',
        'mobile_app_settings',
        'notification_tokens',
        'brokerage_license_account_id',
        'license',
        'license-remove',
        'license_id',
        'is_active_for_competition'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'mobile_app_settings' => 'array',
        'notification_tokens' => 'json',
    ];

    public static function getSearchableFields()
    {
        return ['name', 'email', 'position', 'phone', 'username', 'short_bio'];
    }

    public static function getSearchableRelations()
    {
        return [];
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function teamLeader()
    {
        return $this->belongsTo(User::class, 'team_leader_id');
    }

    public function brokerageLicenseUser()
    {
        return $this->belongsTo(User::class, 'brokerage_license_account_id');
    }

    public function approvedRatingReviews()
    {
        return $this->hasMany(UserRatingEntry::class, 'user_id')->orderBy('created_at', 'DESC')->whereNotNull('approved_at');
    }

    public function leads()
    {
        return $this->hasMany(Lead::class, 'created_by');
    }

    public function nationality()
    {
        return $this->belongsTo(Nationality::class);
    }

    public function contactListTags()
    {
        return $this->belongsToMany(
            ContactsListTag::class,    // Related model
            'user_x_contact_list_tag', // Pivot table
            'user_id',                 // Foreign key on the pivot table for the User model
            'contact_list_tag_id'     // Foreign key on the pivot table for the ContactsListTag model
        );
    }

    public function getNameInitials()
    {
        $nameParts = explode(" ", $this->name);
        return join("", array_map(function ($namePart) {
            return strtoupper($namePart[0]);
        }, $nameParts));
    }

    public function getNameParts()
    {
        $nameParts = explode(" ", $this->name);
        return $nameParts;
    }

    public function getFirstNamePart()
    {
        $nameParts = $this->getNameParts();
        return isset($nameParts[0]) ? $nameParts[0] : "";
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'user_id');
    }

    public function getCompletePhoneNo()
    {
        $mobilePhone = $this->phone;
        $mobilePhonePrefix = $this->prefix_phone;

        if (!empty($mobilePhonePrefix) && !\Str::startsWith($mobilePhone, "+") && !\Str::startsWith($mobilePhone, "00")) {
            $mobilePhone = $mobilePhonePrefix . $mobilePhone;
        }
        $mobilePhone = str_replace(' ', '', $mobilePhone);
        return $mobilePhone;
    }

    public function performanceDocuments()
    {
        return $this->hasMany(UserPerformanceDocs::class)->where('tag', 'performance');
    }

    public function payslipsDocuments()
    {
        return $this->hasMany(UserPerformanceDocs::class)->where('tag', 'payslips');
    }

    public function userLogins()
    {
        return $this->hasMany(UserLogin::class);
    }

    public function userSearches()
    {
        return $this->belongsToMany(SearchDefinition::class, 'users_searches_subscriptions', 'user_id', 'search_id');
    }

    public function checkIns() {
        return $this->hasMany(UserCheckIn::class);
    }

    public function toGlobalSearchResult()
    {
        $imgUrl = 'bi-person-badge';
        if(!empty($this->profile_image)) {
            $imgUrl = imageRoute('icon', $this->profile_image);
        }
        return new GenericGlobalSearchResult(
            $this->id,
            'User',
            $this->name,
            $this->company->name ?? null,
            route('admin.users.edit', ['id' => $this->id]),
            $imgUrl,
            ['email' => $this->email]
        );
    }
}
