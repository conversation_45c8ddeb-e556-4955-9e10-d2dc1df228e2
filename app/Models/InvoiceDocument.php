<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceDocument extends Model
{
    use HasFactory;

    protected $table = 'invoice_documents';

    protected $fillable = [
        'invoice_id',
        'file_name',
        'file_path',
        'document_type',
        'receipt_no',
        'cheque_number',
        'observations',
        'upload_date',
    ];

    protected $casts = [
        'upload_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Document type constants
    const TYPE_PROOF_OF_PAYMENT = 'proof_of_payment';
    const TYPE_RECEIPT = 'receipt';
    const TYPE_CHEQUE = 'cheque';

    public static function getDocumentTypes()
    {
        return [
            self::TYPE_PROOF_OF_PAYMENT => 'Proof of payment',
            self::TYPE_RECEIPT => 'Receipt',
            self::TYPE_CHEQUE => 'Cheque',
        ];
    }

    public static function getDocumentOptions()
    {
        return [
            ['value' => 'INVOICE', 'text' => 'Invoice'],
            ['value' => self::TYPE_PROOF_OF_PAYMENT, 'text' => 'Proof of payment'],
            ['value' => self::TYPE_RECEIPT, 'text' => 'Receipt'],
            ['value' => self::TYPE_CHEQUE, 'text' => 'Cheque'],
        ];
    }

    /**
     * Get the invoice that owns the document
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the appropriate number field based on document type
     */
    public function getDocumentNumberAttribute()
    {
        return $this->document_type === self::TYPE_RECEIPT 
            ? $this->receipt_no 
            : $this->cheque_number;
    }

    /**
     * Scope to filter by document type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Scope to filter by invoice
     */
    public function scopeForInvoice($query, $invoiceId)
    {
        return $query->where('invoice_id', $invoiceId);
    }
}
