<?php

namespace App\Models;

class QueryParamsDef
{
    const AMENITIES = 'a';

    const PROPERTY_TYPE = 't';
    const PRICE_FROM = 'pf';
    const PRICE_TO = 'pt';
    const SQRFT_FROM = 'af';
    const SQRFT_TO = 'at';
    const AREA_MAX = 'areaMax';
    const BEDROOMS = 'be';
    const BATHROOMS = 'ba';
    const FURNISHINGS = 'fu';
    const FURNISHINGS_OFFICE = 'fuOffice';
    const DATE_POSTED = 'd';
    const LOCATION_DATA = 'loc';
    const PER_PAGE = 'pp';
    const IS_EXCLUSIVE = 'is_exclusive';
    const OFFPLAN = 'offplan';
    const IS_HOMEPAGE_PROMOTED = 'is_homepage_promoted';
    const IS_INVESTMENT_OPPORTUNITY = 'is_investment_opportunity';
    const IS_NEW_DEVELOPMENT = 'new_development';
    const OPERATION_TYPE = 'ot';
    const LOCATION_SLUG = 'ls';
    const AVAILABILITY = 'av';
    const TAGS = 'tags';

    const COUNTRY_ID = 'countryId';
    const CITY_ID = 'cityId';
    const REGION_ID = 'regionId';
    const AREA_ID = 'areaId';
    const UNIT_NO = 'unitNo';
    const TOWER = 'tower';
    const BILLS = 'bills';
    const STUDIO = 'studio';

    // added form NEXT filters - used mostly at CRM level
    const LANDLORD_CATEGORY = 'llc';
    const BEDROOMS_NEXT = 'ben';
    const BATHROOMS_NEXT = 'ban';
    const PARKING = 'par';
    const BALCONY = 'bal';
    const VIEW = 'view';
    const KITCHEN = 'ki';
    const PANTRY = 'pantry';

    const LISTED_PRICE = 'lip';
    const BEST_PRICE = 'bep';
    const OFFERS = 'ofe';

    const COMMISSION_LL = 'com-ll';
    const COMMISSION_BUYER = 'com-bu';
    const COMMISSION_TENANT = 'com-te';
    const STATUS = 'status';
    const TITLE_DEED = 'tid';
    const PUBLISHING_STATUS = 'ps';

    const CREATED_BY = 'createdBy';
    const SERVICE_CHARGE = 'serviceCharge';

    // Leads - filteres
    const VIEW_TYPE = 'vt';
    const AGENT = 'agent';
    const MAP_BOUNDS = 'mb';

    const CONTACT_ID = 'contact_id';
    const EXCEPT_ID = 'except_id';
    const PRORATED_RATA = 'prr';
    const MINIMUM_CONTRACT = 'mc';
    const PROPERTY_CREATED_BY = 'created_by';
    const LAST_CONTACT_DATE_FROM = 'lastContactDateFrom';
    const LAST_CONTACT_DATE_TO = 'lastContactDateTo';
    const LEAD_CREATION_DATE_FROM = 'leadCreationDateFrom';
    const LEAD_CREATION_DATE_TO = 'leadCreationDateTo';
    const LEAD_DECISION_DATE_FROM = 'leadDecisionDateFrom';
    const LEAD_DECISION_DATE_TO = 'leadDecisionDateTo';
    const LEAD_SOURCE = 'leadSources';
    const MARKETING_PLATFORMS = 'mpt';
    const LEADS_CREATED_BY_AGENT = 'createdByMe';
    const LEADS_CREATED_BY_SOMEONE_ELSE = 'createdBySomeoneElse';
    const IS_SHORT_STAY = 'isShortStay';
    const IS_STARED = 'is_stared';
    const IS_VERIFIED = 'is_verified';
    const RATING = 'rating';
    const REFFERED_BY = 'refferedBy';
    const LAST_CALL = 'callResponse';
    const CREATED_AT_FROM = 'createdAtFrom';
    const CREATED_AT_TO = 'createdAtTo';

    const UTM_SOURCES = 'utmSources';
    const UTM_CAMPAIGNS = 'utmCampaigns';
    const LEAD_STATUSES = 'leadStatuses';
    const HAS_DEALS = 'hasDeals';

    static function mapToQueryParam($value) {
        switch($value) {
            case 'bedrooms':
                return self::BEDROOMS;
            case 'bathrooms':
                return self::BATHROOMS;
            case 'min_area':
            case 'minArea':
                return self::SQRFT_FROM;
            case 'max_area':
            case 'maxArea':
                return self::SQRFT_TO;
            case 'property_type':
                return self::PROPERTY_TYPE;
            case 'operation_type':
                return self::OPERATION_TYPE;
            case 'budget_min':
            case 'budgetMin':
                return self::PRICE_FROM;
            case 'budget_max':
            case 'budgetMax':
                return self::PRICE_TO;
            case 'furnishings':
                return self::FURNISHINGS;
            default:
                return $value;
        }
    }
}
