<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class MarketingProject extends Model
{
    protected $table = 'marketing__projects';

    /**
     * Get the campaigns for the marketing project
     */
    public function campaigns()
    {
        return $this->hasMany(MarketingCampaign::class, 'marketing_project_id');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // When a project is deleted, delete all associated campaigns
        static::deleting(function($project) {
            $project->campaigns()->delete();
        });
    }
}