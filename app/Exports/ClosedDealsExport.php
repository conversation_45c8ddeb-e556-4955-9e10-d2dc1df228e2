<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use DB;

class ClosedDealsExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $qb = DB::table('deals as d')
        ->select([
            'c.name',
            DB::raw("CONCAT(COALESCE(c.prefix_mobile_1, ''), c.mobile_1) as mobile_1"),
            'c.mobile_2',
            'n.name as nationality',
            'ls.name as lead_source',
            'd.price',
            'd.created_at',
            'd.type',
            'la.name as listing_agent',
            'ca.name as closing_agent'
        ])
        ->join('leads as l', 'l.id', '=', 'd.lead_id')
        ->join('contacts as c', 'c.id', '=', 'l.contact_id')
        ->leftJoin('lead_sources as ls', 'ls.id', 'LIKE', 'l.platform_from')
        ->leftJoin('nationalities as n', 'n.id', '=', 'c.nationality_id')
        ->leftJoin('users as la', 'd.listing_agent_id', '=', 'la.id')
        ->leftJoin('users as ca', 'd.closing_agent_id', '=', 'ca.id')
        // ->where('d.type', 'LIKE', 'SALE')
        ->where(function($qb) {
            $qb->whereNull('d.deal_status')->orWhere('d.deal_status', '=', 'approved');
        })
        ->orderBy('d.created_at', 'DESC')
        ->where('d.created_at', '>=', '2024-12-16 00:00:00');

        // echo $qb->toSql();
        // die;

        return $qb->get();
    }

    public function headings(): array
    {
        return ["Name", "Mobile 1", "Mobile 1", "Nationality", "Lead Source", "Price", "Date", "Deal Type", "Listing Agent", "Closing Agent"];
    }
}
