<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Log;

class PropertyFinderAuthService
{
    protected $apiKey;
    protected $apiSecret;
    protected $tokenUrl;

    public function __construct()
    {
        $this->apiKey = env('PF_API_KEY');
        $this->apiSecret = env('PF_API_SECRET');
        $this->tokenUrl = env('PF_TOKEN_URL');
    }

    public function getAccessToken($forceRefresh = false)
    {
        // If force refresh is requested or token does not exist in cache, get a new one
        if ($forceRefresh || !Cache::has('pf_access_token')) {
            return $this->requestNewToken();
        }

        return Cache::get('pf_access_token');
    }

    private function requestNewToken()
    {
        // Prepare Basic Auth Header
        $credentials = base64_encode("{$this->apiKey}:{$this->apiSecret}");

        // Make API request to get token
        $response = Http::withHeaders([
            'Authorization' => "Basic {$credentials}",
            'Content-Type'  => 'application/json',
        ])->post($this->tokenUrl, [
            'scope'      => 'openid',
            'grant_type' => 'client_credentials',
        ]);

        if ($response->successful()) {
            $data = $response->json();
            $accessToken = $data['access_token'];
            $expiresIn = $data['expires_in'] ?? 1800;

            // Cache token
            Cache::put('pf_access_token', $accessToken, $expiresIn - 60);

            return $accessToken;
        }

        throw new \Exception('Failed to retrieve access token: ' . $response->body());
    }
}
