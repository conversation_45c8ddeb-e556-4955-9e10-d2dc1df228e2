<?php

namespace App\Services;

use App\Models\Crm\RolesDef;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use DB;

class UserService extends GenericService
{
    function getToken()
    {
        return hash('sha256', Str::random(60));
    }

    function getPossibleListingOwners($onlyUsersInTeam = false)
    {
        $user = auth()->user();
        $userIsMatrixAgent = $user->hasRole(RolesDef::MATRIX_AGENT);
        $userIsMatrixAgentManager = $user->hasRole(RolesDef::MATRIX_AGENT_MANAGER);
        $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);

        if ($userIsMatrixAgent || $userIsMatrixAgentManager) {
            $managerId = $userIsMatrixAgent ? $user->created_by : $user->id;
            $users = User::orderBy('name', 'asc')->where(function ($qb) use ($user, $managerId) {
                $qb->where('created_by', $managerId)
                    ->orWhere('id', $managerId);
            })->get();
        } else if ($userIsTeamLeader && $onlyUsersInTeam) {
            $users = User::orderBy('name', 'asc')->where(function ($qb) use ($user) {
                $qb->where('team_leader_id', $user->id)
                    ->orWhere('id', $user->id);
            })->get();
            foreach ($users as $user) {
                $user->selectName = $user->name;
            }
        } else {
            $users = User::get()->filter(function ($innerUser) {
                return $innerUser->hasAnyRole([
                    RolesDef::MASTER_BROKER,
                    RolesDef::AGENT,
                    RolesDef::OFFICE_MANAGER,
                    RolesDef::MATRIX_AGENT,
                    RolesDef::MATRIX_AGENT_MANAGER,
                    RolesDef::TEAM_LEADER,
                ]);
            })->each(function ($item) {
                $item->selectName = $item->name;
                if ($item->hasRole(RolesDef::MATRIX_AGENT)) {
                    $item->selectName = '[M Ag] ' . $item->name;
                }
                if ($item->hasRole(RolesDef::MATRIX_AGENT_MANAGER)) {

                    $item->selectName = '[M Mg] ' . $item->name;
                }
            })
                ->sortBy('selectName');
        }
        return $users;
    }

    public function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $extraClause = $this->getRequestParams($request);

        $q = DB::table('users')
            ->leftJoin('model_has_roles as mhr', function ($qb) {
                $qb->on('mhr.model_id', '=', 'users.id')
                    ->where('mhr.model_type', User::class);
            })
            ->leftJoin('roles as r', 'r.id', '=', 'mhr.role_id')
            ->leftJoin('users as blu', 'blu.id', '=', 'users.brokerage_license_account_id');

        if (!empty($extraClause['sort'])) {
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($extraClause['sort'], $extraClause['dir']);
            } else {
                $q = $q->orderBy($extraClause['sort']);
            }
        } else {
            $q = $q->orderBy('users.name', 'asc');
        }

        if (!empty($extraClause['q'])) {
            $q->where(function ($sql) use ($extraClause) {
                $term = trim($extraClause['q']) . '%';
                $sql->where('users.name', 'like', $term)
                    ->orWhere('users.username', 'like', $term)
                    ->orWhere('users.email', 'like', $term)
                    ->orWhere('users.position', 'like', $term);
            });
        }

        $q->whereNull('users.deleted_at');
        if (isset($extraClauses['created_by'])) {
            $q->where('users.created_by', $extraClauses['created_by']);
        }

        $selectableFields = $this->getSelectableFields();
        $q->select($selectableFields);
        $q->groupBy('users.id');

        return $q;
    }

    private function getSelectableFields()
    {
        return [
            'users.id',
            'users.name',
            'users.rating',
            'users.email',
            'users.position',
            DB::raw('blu.name as brokerage_license_agent'),
            'users.profile_image',
            'users.public_profile',
            DB::raw('GROUP_CONCAT(r.name SEPARATOR ", ") as roles')
        ];
    }

    public function getUsersWithRoles()
    {
        $users = User::orderBy('name', 'asc')
            ->whereHas('roles', function ($q) {
                $q->whereIn('name', [
                    RolesDef::AGENT,
                    RolesDef::OFFICE_MANAGER,
                    RolesDef::MASTER_BROKER,
                    RolesDef::CALL_CENTER_AGENT,
                ]);
            })->get();

        return $users;
    }

    public function getUsersWithRolesGrouped()
    {
        // Get regular users (agents, office managers, etc.) - excluding team leaders to avoid duplicates
        $regularUsers = User::orderBy('name', 'asc')
            ->whereHas('roles', function ($q) {
                $q->whereIn('name', [
                    RolesDef::AGENT,
                    RolesDef::OFFICE_MANAGER,
                    RolesDef::MASTER_BROKER,
                    RolesDef::CALL_CENTER_AGENT,
                ]);
            })
            ->whereDoesntHave('roles', function ($q) {
                $q->where('name', RolesDef::TEAM_LEADER);
            })
            ->get();

        // Get team leaders
        $teamLeaders = User::orderBy('name', 'asc')
            ->whereHas('roles', function ($q) {
                $q->where('name', RolesDef::TEAM_LEADER);
            })->get();

        // Create a grouped structure
        $groupedUsers = collect();

        // Add regular users first
        foreach ($regularUsers as $user) {
            $groupedUsers->push($user);
        }

        // Add a separator object for team leaders
        if ($teamLeaders->count() > 0) {
            $groupedUsers->push((object)[
                'id' => 'team-leaders-separator',
                'name' => '--- Team Leaders ---',
                'is_separator' => true
            ]);

            // Add team leaders
            foreach ($teamLeaders as $teamLeader) {
                $groupedUsers->push($teamLeader);
            }
        }

        return $groupedUsers;
    }

    public function getTodaysUserBirthday() {
        return User::whereRaw("DATE_FORMAT(date_of_birth,'%m-%d') = DATE_FORMAT(NOW(),'%m-%d')")->get();
    }
}
