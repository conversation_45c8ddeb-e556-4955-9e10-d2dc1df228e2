<?php

namespace App\Services;

use CURLFile;
use Illuminate\Support\Facades\Http;
use Log;
use Str;
use Curl\Curl;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class PropertyFinderService
{
    const ENDPOINTS = [
        'AUTH' => 'https://api-v2.mycrm.com/token',
        'LISTINGS' => 'https://api-v2.mycrm.com/properties',
        'UPLOAD' => 'https://api-upload.mycrm.com/upload',
        'LISTING_DELETE' => 'https://api-v2.mycrm.com/properties/{propertyfinderListingId}'
    ];
    const PF_STATES = [
        "DRAFT" => "draft",
        "PENDING" => "pending",
        "APPROVED" => "approved",
        "REFUSED" => "refused",
        "ARCHIVED" => "archived",
    ];
    const PF_STATUSES = [
        "DRAFT" => "draft",
        "AVAILABLE" => "available",
        "RENTED" => "rented",
        "UNDER_OFFER" => "under offer",
        "RESERVED" => "reserved",
        "SOLD" => "sold"
    ];
    const PF_FURNISHINGS = [
        "FURNISHED" => "furnished",
        "UNFURNISHED" => "unfurnished",
        "SEMI_FURNISHED" => "semi-furnished"
    ];
    const PF_FREEHOLD = [
        "FREE_HOLD" => "Free-hold",
        "NON_FREEH_OLD" => "Non Free-hold",
        "LEASE_HOLD" => "Lease hold"
    ];
    const PF_OFFERING_TYPE = [
        "RENT" => "rent",
        "SALE" => "sale"
    ];
    const PF_PROJECT_STATUS = [
        'COMPLETED' => 'completed',
        'OFFPLAN' => 'off_plan'
    ];
    const PROPERTY_TYPES = [
        [
            "id" => 1,
            "name" => "Apartment",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 2,
            "name" => "Bungalow",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => true
        ],
        [
            "id" => 3,
            "name" => "Compound",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 4,
            "name" => "Duplex",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 5,
            "name" => "Full floor",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => true
        ],
        [
            "id" => 6,
            "name" => "Half floor",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => true
        ],
        [
            "id" => 7,
            "name" => "Land",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => true
        ],
        [
            "id" => 8,
            "name" => "Penthouse",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 9,
            "name" => "Townhouse",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 10,
            "name" => "Villa",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 11,
            "name" => "Whole Building",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 12,
            "name" => "Bulk Units",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 13,
            "name" => "Full floor",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 14,
            "name" => "Half floor",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => true
        ],
        [
            "id" => 15,
            "name" => "Labor Camp",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 16,
            "name" => "Office Space",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 17,
            "name" => "Plot",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 18,
            "name" => "Retail",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 19,
            "name" => "Shop",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 20,
            "name" => "Show Room",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 21,
            "name" => "Staff Accommodation",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 22,
            "name" => "Villa",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 23,
            "name" => "Warehouse",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 24,
            "name" => "Whole Building",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 25,
            "name" => "Farm",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 26,
            "name" => "Rest house",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 27,
            "name" => "Factory",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 28,
            "name" => "Rest house",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 30,
            "name" => "Farm",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 31,
            "name" => "Short Term / Hotel Apartment",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 32,
            "name" => "Chalet",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 33,
            "name" => "Block",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 34,
            "name" => "Land",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 35,
            "name" => "Labor Camp",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 36,
            "name" => "Bulk Units",
            "category" => "residential",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 37,
            "name" => "Twin House",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 38,
            "name" => "Triplex",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 39,
            "name" => "Loft",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 40,
            "name" => "Riad",
            "category" => "residential",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 41,
            "name" => "Short Term / Hotel Apartment",
            "category" => "commercial",
            "for_rent" => true,
            "for_sale" => true
        ],
        [
            "id" => 42,
            "name" => "Medical facility",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 44,
            "name" => "Theatre",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 45,
            "name" => "Coffee Shop",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 46,
            "name" => "Store",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 47,
            "name" => "Garage",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ],
        [
            "id" => 48,
            "name" => "Restaurant",
            "category" => "commercial",
            "for_rent" => false,
            "for_sale" => false
        ]
    ];

    const FGR_TO_PF_TYPES_MAP = [
        "1" => 1, // apartment
        "2" => 10, // villa,
        "3" => 16, // office,
        "4" => 22, // commercial villa,
        "5" => 19, // retail shop
        "7" => 22, // commercial villa -> commercial villa
        "8" => 19, // shop
        "11" => 7, // residential land
        "14" => 8, // penthouse
        "15" => 9, // townhouse
        "16" => 9, // chalet -> townhouse
        "17" => 31, // hotel apart residential
        "18" => 10, // compound villa -> residential villa
        "20" => 11, // whole building
        "21" => 15, // labor camp
        "22" => 22, // warehouse
        "23" => 23, // factory -> warehouse
        "24" => 17, // industrial land -> plot
        "25" => 4, // duplex -> duplex
    ];
    const FGR_TO_PF_STATUSES_MAP = [
        'occupied' => self::PF_STATUSES['RENTED'],
        'rented' => self::PF_STATUSES['RENTED'],
        'available' => self::PF_STATUSES['AVAILABLE'],
        'sold' => self::PF_STATUSES['SOLD'],
        'booked-by-fg' => self::PF_STATUSES['RESERVED'],
        'not-available' => self::PF_STATUSES['DRAFT'],
        'to-be-available' => self::PF_STATUSES['DRAFT'],
    ];
    const FGR_TO_PF_AMENITIES_MAP = [
        99 => '', // dedicated parking slot
        104 => '', // tennis court
        122 => 9,
        123 => 16,
        160 => 24,
        164 => 23,
        176 => 91,
        177 => 14,
        184 => 68,
        191 => 1,
        207 => 28,
        208 => 27,
        209 => 30,
        211 => '', // movie room
        212 => '', // outdoor lounge
        213 => '', // clubhouse
        214 => 25,
        215 => '', // massage room

        219 => 22,
        221 => 3,
        222 => 43,
        226 => 19,
        227 => 20,
        228 => 18,
        233 => 61,
        237 => 42,
        238 => 2,
        239 => 60,

        240 => 35,

        //central air conditioning
        241 => 7,
        242 => 72,
        243 => 62,
        244 => 65,
        245 => 74,
        246 => 69,
        248 => 34,
        252 => 73,
    ];
    const FGR_TO_PF_FURNISHING_MAP = [
        'unfurnished' => self::PF_FURNISHINGS["UNFURNISHED"],
        'semi-furnished' => self::PF_FURNISHINGS["SEMI_FURNISHED"],
        'fully-furnished' => self::PF_FURNISHINGS["FURNISHED"],
        'partially-furnished' => self::PF_FURNISHINGS["SEMI_FURNISHED"],
    ];

    private $accessToken = '';
    private $operationHistoryService;
    private $propertyFinderAuthService;

    public function __construct(OperationHistoryService $operationHistoryService, PropertyFinderAuthService $propertyFinderAuthService)
    {
        $this->operationHistoryService = $operationHistoryService;
        $this->propertyFinderAuthService = $propertyFinderAuthService;
    }

    public function syncRemoteData($snapshot, $attributes, $noUser = false)
    {
        $responseData = null;
        $errorResponseText = null;
        try {
            $this->handleAccessToken($snapshot, $noUser);
            Log::info("[PF Snapshot Sync] Retrieved access token: " . (empty($this->accessToken) ? "FAIL" : "SUCCESS"));
            if (!empty($this->accessToken)) {
                // images
                $imagesPath = storage_path(env('IMAGES_DIR_LISTING'));
                $imagesToSave = [];
                if (!empty($snapshot->images) && is_array($snapshot->images)) {
                    foreach ($snapshot->images as $imageToUpload) {
                        $imgPath = $imagesPath . "/" . $imageToUpload;
                        $originalImgPath = $imageToUpload;
                        if (file_exists($imgPath)) {
                            $uploaded = false;
                            foreach ([".jpg", ".jpeg"] as $fileExt) {
                                if (!$uploaded) {
                                    $imageToUploadJpg = Str::replaceLast(".webp", $fileExt, $imageToUpload);
                                    $imgPath = $imagesPath . "/" . $imageToUploadJpg;
                                    Log::info("Checking PF upload image file: " . $imgPath);
                                    if (file_exists($imgPath)) {
                                        Log::info("Exists ...");
                                        $uploadRes = $this->uploadImage($imageToUploadJpg);
                                        Log::info("[PF Snapshot Sync]1. Uploaded image to PF [" . $imgPath . "]");
                                        if (!is_null($uploadRes) && isset($uploadRes->upload) && isset($uploadRes->upload->token)) {
                                            $imagesToSave[] = $uploadRes->upload->token;
                                            $uploaded = true;
                                            Log::info("[PF Snapshot Sync] Image token [" . $uploadRes->upload->token . "]");
                                        }
                                        break;
                                    } else {
                                        Log::info("The img path doesn't exist: " . $imgPath . " for snapshot: [". $snapshot->ref_no ."]");
                                    }
                                }
                            }
                            if (!$uploaded) {
                                $uploadRes = $this->uploadImage($imageToUpload);
                                Log::info("[PF Snapshot Sync]2. Uploaded image to PF [" . $imageToUpload . "]");
                                if (!is_null($uploadRes) && isset($uploadRes->upload) && isset($uploadRes->upload->token)) {
                                    $imagesToSave[] = $uploadRes->upload->token;
                                    $uploaded = true;
                                    Log::info("[PF Snapshot Sync] Image token [" . $uploadRes->upload->token . "]");
                                } 
                                if(!$uploaded) {
                                    Log::info("Not uploaded. Try to upload: " . $originalImgPath);
                                    $image = imagecreatefromwebp($imagesPath . "/" . $originalImgPath);
                                    ob_start();
                                    imagejpeg($image, null, 90); 
                                    $imageContents = ob_get_clean();
                                    imagedestroy($image);

                                    $filename = 'converted_' . uniqid() . '.jpg';
                                    $savePath = storage_path('app/public/listings/' . $filename);
                                    file_put_contents($savePath, $imageContents);

                                    $uploadRes = $this->uploadImage($filename);
                                    if (!is_null($uploadRes) && isset($uploadRes->upload) && isset($uploadRes->upload->token)) {
                                        $imagesToSave[] = $uploadRes->upload->token;
                                        Log::info("Webp image was converted and uploaded with succes - [PF Snapshot Sync] Image token [" . $uploadRes->upload->token . "]");
                                    }
                                }
                            } 
                        } else{
                            Log::info("The image path doesn't exist [". $imgPath ."] for snapshot: [". $snapshot->ref_no ."]");
                        }
                    }
                }

                $apiPayload = $this->getJSONPayloadFromSnapshot($snapshot, $attributes, $imagesToSave);
                Log::info("[PF Snapshot Sync] Sync with PF: Generated API Payload: ", $apiPayload);
                $remoteURL = self::ENDPOINTS['LISTINGS'];
                $callMethod = 'post';
                if (!empty($snapshot->remote_propertyfinder_id)) {
                    $remoteURL = self::ENDPOINTS['LISTINGS'] . '/' . $snapshot->remote_propertyfinder_id;
                    $callMethod = 'put';
                }
                // echo $remoteURL;
                // die;
                $response = $this->callAPI($remoteURL, $callMethod, $apiPayload);
                $jsonData = $response->json();
                if ($response->status() == 201) {
                    if (isset($jsonData['property']) && isset($jsonData['property']['id'])) {
                        // update the property finder id in DB
                        $responseData = $jsonData;
                        $this->operationHistoryService->addForSnapshot($snapshot, "Data published in PROPERTYFINDER with reference: " . $jsonData['property']['reference'], auth()->user());
                    }
                } elseif ($response->status() == 200) {
                    // nothing to do
                    $responseData = $jsonData;
                    $this->operationHistoryService->addForSnapshot($snapshot, "Data updated in PROPERTYFINDER", auth()->user());
                } else {
                    Log::info("[PF Snapshot Sync] PF sync fail " . $response->status());
                    Log::info("[PF Snapshot Sync] PF sync fail response: " . json_encode($jsonData));
                    $errorResponseText = $jsonData;
                    $this->operationHistoryService->addForSnapshot($snapshot, "PROPERTYFINDER sync fail", auth()->user());
                }
            }
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
        }
        return ['success' => $responseData, 'error' => $errorResponseText];
    }

    private function uploadImage($snapshotFileName)
    {
        $imagesPath = storage_path(env('IMAGES_DIR_LISTING'));
        $firstImagePath = $imagesPath . "/" . $snapshotFileName;
        if (file_exists($firstImagePath)) {
            $curlFile = new CURLFILE($firstImagePath);
            $curlFile->setMimeType("image/jpeg");
            $curl = new Curl();
            $curl->setHeader('Authorization', 'Bearer ' . $this->accessToken);
            $curl->post(self::ENDPOINTS['UPLOAD'] . "?type=property_image", [
                'file' => $curlFile
            ]);
            if ($curl->error) {
                Log::error("Error on uploading image to PF [ ". $snapshotFileName ."]: " . $curl->errorMessage);
            } else {
                return $curl->response;
            }
        }
        return null;
    }

    // temporary implementation
    private function handleAccessToken($snapshot, $noUser)
    {
        // $propertyfinderToken = getCachedSetting('propertyfinderApiKey');

        // if (is_null($propertyfinderToken)) {
        //     throw ("No PF Token found");
        // }

        $propertyfinderToken = $this->propertyFinderAuthService->getAccessToken();

        $this->accessToken = $propertyfinderToken;
        // $csrEmail = env('EMAIL_CSR');
        // $csrPass = env('PROPERTYFINDER_CSR_PASS');
        // Log::info("User / pass ".$csrEmail." - ".$csrPass);
        // if ($noUser) {
        //     $authData = [
        //         "grant_type" => "password",
        //         "domain" => "findgreat",
        //         "username" => $csrEmail,
        //         "password" => $csrPass,
        //         "scope" => "offline"
        //     ];
        // } else {
        //     $user = $snapshot->author;
        //     $pfPass = $user->pf_expert_password;
        //     $authData = null;
        //     if (empty($pfPass)) {
        //         if ($user->email === '<EMAIL>' || $user->email === '<EMAIL>') {
        //             $authData = [
        //                 "grant_type" => "password",
        //                 "domain" => "findgreat",
        //                 "username" => $csrEmail,
        //                 "password" => $csrPass,
        //                 "scope" => "offline"
        //             ];
        //         }
        //     } else {
        //         $authData = [
        //             "grant_type" => "password",
        //             "domain" => "findgreat",
        //             "username" => $user->email,
        //             "password" => $user->pf_expert_password,
        //             "scope" => "offline"
        //         ];
        //     }
        // }


        // if (!is_null($authData)) {
        //     $request = Http::withHeaders([
        //         'Content-Type' => 'application/json',
        //     ]);
        //     $response = $request->post(self::ENDPOINTS['AUTH'], $authData);
        //     Log::info("[PF Snapshot Sync] Access token retrieval response status: " . $response->status());

        //     if ($response->status() == 201) {
        //         $jsonData = $response->json();
        //         if (isset($jsonData["access_token"])) {
        //             $this->accessToken = $jsonData['access_token'];
        //         }
        //     }
        // }
    }

    private function callAPI(string $url, string $method = 'post', array $data = [])
    {
        $accessToken = $this->propertyFinderAuthService->getAccessToken();
        $request = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $accessToken
        ]);
        Log::info("[PF Snapshot Sync] callAPI method: [" . $method . "], url: [" . $url . "]");
        if ($method == "post" || $method == 'patch' || $method == 'put') {
            $response = $request->{$method}($url, $data);
        } else {
            $response = $request->{$method}($url);
        }

        return $response;
    }

    private function getJSONPayloadFromSnapshot($snapshot, $attributes, $imagesToSave)
    {
        $payloadNeededAttributes = [
            'BATHROOMS' => 0,
            'BEDROOMS' => 0,
            'SIZE' => 0,
            'DESCRIPTION' => "",
            'PARKING' => 0,
            'FURNISHING' => "",
            'CONSTRUCTION_YEAR' => "",
        ];
        $amenitiesIds = [];
        $amenitiesKeyIds = array_keys(self::FGR_TO_PF_AMENITIES_MAP);

        $attributes->each(function ($attr) use (&$payloadNeededAttributes, $amenitiesKeyIds, &$amenitiesIds, $snapshot) {
            if ($attr->attribute_definition_id == '23') { // description
                $payloadNeededAttributes['DESCRIPTION'] = $attr->value_large;
            }
            if ($attr->attribute_definition_id == '196') { // bedrooms
                $payloadNeededAttributes['BEDROOMS'] = $attr->value;
            }
            if ($attr->attribute_definition_id == '197') { // bathrooms
                $payloadNeededAttributes['BATHROOMS'] = $attr->value;
            }
            if ($attr->attribute_definition_id == '71') { // size - area
                $newSize = (string)$attr->value;
                $newSize = str_replace(',', '', $newSize);
                $payloadNeededAttributes['SIZE'] = (int)$newSize;
            }
            if ($attr->attribute_definition_id == '199') { // parking info
                $payloadNeededAttributes['PARKING'] = $attr->value;
            }
            if (in_array($snapshot->property_type_id, [3])) { // office
                if ($attr->attribute_definition_id == '206') { // Office furnishings
                    $payloadNeededAttributes['FURNISHING'] = $attr->value;
                }
            } else {
                if ($attr->attribute_definition_id == '200') { // furnishings
                    $payloadNeededAttributes['FURNISHING'] = $attr->value;
                }
            }
            if ($attr->attribute_definition_id == '198') { // build year
                $payloadNeededAttributes['CONSTRUCTION_YEAR'] = $attr->value;
            }
            foreach ($amenitiesKeyIds as $fgrId) {
                if ($attr->attribute_definition_id == $fgrId && !empty(self::FGR_TO_PF_AMENITIES_MAP[$fgrId])) {
                    $amenitiesIds[] = self::FGR_TO_PF_AMENITIES_MAP[$fgrId];
                }
            }
        });

        $remotePropertyTypeId = 0;
        if (isset(self::FGR_TO_PF_TYPES_MAP[$snapshot->property_type_id])) {
            $remotePropertyTypeId = self::FGR_TO_PF_TYPES_MAP[$snapshot->property_type_id];
        } else {
            $this->operationHistoryService->addForSnapshot($snapshot, "Propertyfinder sync issue: Remote property type not found [" . $snapshot->property_type_id . "]", auth()->user());
            throw new \Exception("Remote property type not found [" . $snapshot->property_type_id . "]");
        }
        Log::info("[PF Snapshot Sync] Found remote property type: " . $remotePropertyTypeId);

        $remoteStatus = self::PF_STATUSES['AVAILABLE'];
        if (isset(self::FGR_TO_PF_STATUSES_MAP[$snapshot->status])) {
            // $remoteStatus = self::FGR_TO_PF_STATUSES_MAP[$snapshot->status];
        } else {
            $this->operationHistoryService->addForSnapshot($snapshot, "Propertyfinder sync issue: Remote status not found [" . $snapshot->status . "]", auth()->user());
            Log::info("[PF Snapshot Sync] Remote status not found: [" . $snapshot->status . "]");
            throw new \Exception("Remote status not found [" . $snapshot->status . "]");
        }

        $remoteLocationId = null;
        if (!is_null($snapshot->location) && !empty($snapshot->location->remote_propertyfinder_id)) {
            $remoteLocationId = $snapshot->location->remote_propertyfinder_id;
        }
        if (is_null($remoteLocationId)) {
            $this->operationHistoryService->addForSnapshot($snapshot, "Propertyfinder sync issue: Could not map to a specific remote location [" . $snapshot->location_id . "]", auth()->user());
            throw new \Exception("Could not map to a specific remote location [" . $snapshot->location_id . "]");
        }
        $payloadImages = [];
        if (is_array($imagesToSave) && count($imagesToSave)) {
            foreach ($imagesToSave as $index => $imageToken) {
                $payloadImages[] = (object)[
                    'image' => [
                        'token' => $imageToken,
                        'order' => $index + 1
                    ]
                ];
            }
        }
        $priceArr = ["offering_type" => $snapshot->ad_type];
        if ($snapshot->ad_type == 'rent') {
            $priceArr["default_period"] = "month";
            $priceArr["prices"] = [
                ["period" => "month", "value" => (float)$snapshot->price]
            ];
        } else {
            $priceArr["value"] = (float)$snapshot->price;
        }

        $views = [];
        if (!is_null($snapshot->views)) {
            $views = $snapshot->views->map(function ($item) {
                return $item->name;
            })->toArray();
        }

        $mappedFurnishing = "";
        if (!empty($payloadNeededAttributes['FURNISHING'])) {
            if (isset(self::FGR_TO_PF_FURNISHING_MAP[$payloadNeededAttributes['FURNISHING']])) {
                $mappedFurnishing = self::FGR_TO_PF_FURNISHING_MAP[$payloadNeededAttributes['FURNISHING']];
            }
        }

        $retObj = [
            "property" => [
                "draft" => false,
                "amenities" => $amenitiesIds,
                "type" => $remotePropertyTypeId,
                "bathrooms" => intval($payloadNeededAttributes['BATHROOMS']),
                "bedrooms" => intval($payloadNeededAttributes['BEDROOMS']),
                "size" => $payloadNeededAttributes['SIZE'],
                "built_up_area" => $payloadNeededAttributes['SIZE'],
                "status" => $remoteStatus,
                "state" => "approved",
                "location" => ["id" => $remoteLocationId],
                "price" => $priceArr,
                "price_offering_type" => $snapshot->ad_type,
                "languages" => [
                    "en" => [
                        "title" => !empty($snapshot->propertyfinder_title) ? $snapshot->propertyfinder_title : $snapshot->title,
                        "description" => strip_tags($payloadNeededAttributes['DESCRIPTION'])
                    ]
                ],
                "images" => $payloadImages,
                "views" => $views,
                "parking" => intval($payloadNeededAttributes['PARKING']),

                "project_status" => $snapshot->off_plan == 1 ? self::PF_PROJECT_STATUS['OFFPLAN'] : self::PF_PROJECT_STATUS['COMPLETED'],
                "developer" => $snapshot->developer,
            ]
        ];

        if (empty($snapshot->remote_propertyfinder_id)) {
            $retObj["property"]["reference"] = $snapshot->ref_no;
        }

        if (!empty($mappedFurnishing)) {
            $retObj["property"]["furnished"] = $mappedFurnishing;
        }
        if (!empty($payloadNeededAttributes['CONSTRUCTION_YEAR'])) {
            $retObj["property"]["build_year"] = intval($payloadNeededAttributes['CONSTRUCTION_YEAR']);
        }

        return $retObj;
    }

    public function tryToNavigateToPFURL(int $pfPropertyId)
    {
        return redirect()->away('https://propertyfinder.qa/to/' . $pfPropertyId);
    }

    private function handlePublishedListingsForPage($page = 1)
    {
        $remoteURL = self::ENDPOINTS['LISTINGS'] . '?filters[portal]=propertyfinder&filters[has_publication]=1&page=' . $page;
        $response = $this->callAPI($remoteURL, "GET");
        if ($response->status() == 200) {
            $jsonResponse = $response->json();
            if (isset($jsonResponse['properties']) && is_array($jsonResponse['properties'])) {
                $propertiesArr = $jsonResponse['properties'];
                foreach ($propertiesArr as $pfProperty) {
                    if (isset($pfProperty['import']) && isset($pfProperty['import']['propertyfinder']) && isset($pfProperty['import']['propertyfinder']['id'])) {
                        $pfImportId = $pfProperty['import']['propertyfinder']['id'];
                        $idParts = explode("-", $pfImportId);
                        if (count($idParts) === 2) {
                            $idPart = $idParts[1];
                            if (!empty($idPart)) {
                                DB::table('properties')->where('ref_no', $pfProperty['reference'])->update(['remote_propertyfinder_public_id' => $idPart]);
                            }
                        }
                    }
                }
            }
        }
        return $response;
    }

    public function syncPublishedListings()
    {
        // reset already published listings
        DB::table('properties')->update(['remote_propertyfinder_public_id' => null]);
        $this->handleAccessToken(null, true);
        $response = $this->handlePublishedListingsForPage();
        $jsonResponse = $response->json();
        if (!is_null($jsonResponse) && isset($jsonResponse['count'])) {
            $pagesNo = ceil($jsonResponse['count'] / $jsonResponse['per_page']);
            Log::info("Propertyfinder listings to sync: " . $jsonResponse['count']);
            if ($pagesNo > 1) {
                $currPage = 2;
                while ($currPage <= $pagesNo) {
                    Log::info("Try to handle listings for page" . $currPage);
                    $this->handlePublishedListingsForPage($currPage);
                    $currPage++;
                    Log::info("Handled Listings for" . $currPage);
                }
            }
        }
    }

    public function deleteListing($propertyfinderListingId) {
        if(empty($propertyfinderListingId)) {
            Log::info('[PF Listing] no propertyfinderListingId to DELETLE');
            return false;
        }

        $this->handleAccessToken(null, true);
        Log::info("[PF Listing] Try to delete listing with id: ". $propertyfinderListingId);
        $remoteURL = str_replace('{propertyfinderListingId}', $propertyfinderListingId, self::ENDPOINTS['LISTING_DELETE']);
        Log::info('Remote URL:'. $remoteURL);
        $callMethod = 'delete';
        $response = $this->callAPI($remoteURL, $callMethod);
        
        if ($response->status() == 200 || $response->status() == 204) {
            return true;            
        } else {
            $jsonData = $response->json();
            Log::info("[PF Listing] Delete listing fail " . $response->status());
            Log::info("[PF Listing] Delete listing fail response: " . json_encode($jsonData));
            return false;
        }
    }
}
