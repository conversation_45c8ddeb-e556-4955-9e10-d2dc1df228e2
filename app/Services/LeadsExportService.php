<?php

namespace App\Services;

use App\Models\Crm\RolesDef;
use App\Models\User;
use App\Services\PropertyTypesService;
use Illuminate\Http\Request;

class LeadsExportService
{
    protected $propertyTypesService;

    public function __construct(
        AuthorizationService $authoServ,
        LeadsService $leadsService,
        PropertyTypesService $propertyTypesService
    ) {
        $this->leadsService = $leadsService;
        $this->propertyTypesService = $propertyTypesService;
        $this->ajaxListMapper = function ($item) use ($authoServ) {
            $allowedActions = $authoServ->getAllowedActions(AuthorizationService::CATEG_LEADS);
            $isAdmin = auth()->user()->hasRole(RolesDef::OFFICE_MANAGER);
            $isAdminOrAgent = auth()->user()->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::AGENT);
            $data_item = [];
            $leadsRequestObj = json_decode($item->leads_request);
            $propertyTypeNames = "-";
            if (!is_null($leadsRequestObj) && property_exists($leadsRequestObj, 't')) {
                $filterPropertyTypeValues = $leadsRequestObj->t;
                if (!is_array($filterPropertyTypeValues)) {
                    $filterPropertyTypeValues = [$filterPropertyTypeValues];
                }

                $propertyTypeNamesArray = $this->propertyTypesService
                    ->getCachedTypes()
                    ->whereIn('id', $filterPropertyTypeValues)
                    ->map(function ($pt) {
                        return $pt->label;
                    })->toArray();
                $propertyTypeNames = join(", ", $propertyTypeNamesArray);
            }

            if (!empty($item->contact_id)) {
                $mobile1 = !empty($item->contact_mobile_1) ? getCompletePhoneNo($item->contact_prefix_mobile_1, $item->contact_mobile_1) : "";
                $mobile2 = !empty($item->contact_mobile_2) ? getCompletePhoneNo($item->contact_prefix_mobile_2, $item->contact_mobile_2) : "";
                $row1ConcatValues = concatValues($mobile1, $mobile2);
                $data_item['full_name'] = $item->contact_name;
                $data_item['phone'] = $row1ConcatValues ?? 'N/A';
                $data_item['email'] = concatValues($item->contact_email_1, $item->contact_email_2);
            } else {
                $data_item['full_name'] = "N/A";
                $data_item['phone'] = "N/A";
                $data_item['email'] = "N/A";
            }

            if (isset($item->location_id)) {
                $data_item['location_id'] = $item->location_id;
            } else {
                $data_item['location_id'] = "-";
            }

            $data_item['budget'] = $item->filter_budget_max ? $item->filter_budget_max : "-";
            $data_item['move_in_date'] = !empty($item->move_in_date) ? $item->move_in_date : "N/A";
            $data_item['requirements'] = $item->requirements;
            $data_item['filter_operation_type'] = $item->filter_operation_type;
            $data_item['filter_property_type'] = $propertyTypeNames;
            if ($isAdmin) {
                if (!empty($item->assignment_id) && !empty($item->assignment_user_id) && empty($item->assignment_deleted_at) && empty($item->assignment_user_deleted_at)) {
                    $data_item['assigned_to'] = ["id" => $item->assignment_user_id, "name" => $item->assignment_user_name, "color" => $item->assignment_user_color];
                    $data_item['assigned_at'] = $item->assignment_created_at;
                } else {
                    $data_item['assigned_to'] = "N/A";
                }
            } else {
                if (!empty($item->assignment_id) && !empty($item->assignment_user_id) && empty($item->assignment_deleted_at) && empty($item->assignment_user_deleted_at)) {
                    $data_item['assigned_to'] = ["id" => $item->assignment_user_id, "name" => $item->assignment_user_name, "color" => $item->assignment_user_color];
                    $data_item['assigned_at'] = $item->assignment_created_at;
                } else {
                    $data_item['assigned_at'] = "N/A";
                }
                $data_item['platform_from'] = $item->platform_from;
            }

            $actions = [];
            if ($allowedActions['read']) {
                $actions['read'] = route('crm.leads.read', ['id' => $item->id]);
            }
            if ($allowedActions['update']) {
                $actions['update'] = route('crm.leads.edit', ['id' => $item->id]);
            }
            if ($allowedActions['delete']) {
                $actions['delete'] = route('crm.leads.delete', ['id' => $item->id]);
            }
            $createdAtDate = new \DateTime($item->created_at);
            $nowDate = new \DateTime();
            $isTodayDate = $createdAtDate->format('Y') == $nowDate->format("Y") && $createdAtDate->format('M') == $nowDate->format("M") && $createdAtDate->format('d') == $nowDate->format("d");
            $data_item['created_at'] = $isTodayDate ? $createdAtDate->format("H:i") : $createdAtDate->format("Y-m-d H:i");
            $data_item['created_at_date'] = $createdAtDate->format("Y-m-d H:i");

            $lastContactDate = empty($item->last_contact_date) ? '' : (new \DateTime($item->last_contact_date))->format("d/m/Y");
            $data_item['reminder'] = $item->reminder;
            $data_item['inquired_ref_no'] = $item->inquired_ref_no;
            $data_item['created_by'] = $item->created_by;
            $data_item['actions'] = $actions; //Actions
            $data_item['manage'] = ""; //Manage
            $data_item['DT_RowId'] = $item->id;
            $data_item['priority'] = strtoupper($item->priority);
            $data_item['platform_from'] = $item->platform_from;
            $data_item['status'] = $item->status;
            $data_item['rating'] = $item->rating;
            $data_item['client_status'] = $item->client_status;
            $data_item['is_reassigned'] = !empty($item->lead_reassignation_id);
            $data_item['reassign_agent_name'] = $item->user_reassignation_name ? $item->user_reassignation_name : '';
            $data_item['can_cancel_reassignment'] = !empty($item->lead_reassignation_id) && ($isAdmin || $item->assignment_user_id == auth()->user()->id);
            $data_item['is_lead_card_non_draggable'] = !empty($item->lead_reassignation_id) && $item->assignment_user_id != auth()->user()->id;
            // $data_item['last_contact_datetime'] = $lastContactDateFull;
            // $data_item['last_contact_date'] = empty($item->last_contact_date) ? '' : (new \DateTime($item->last_contact_date))->format("d/m/Y");
            $data_item['status'] = ["name" => $item->lead_status_name, "id" => $item->status, "background_color" => $item->lead_status_background_color];
            // $data_item['ref_no_ui'] = $this->getStatusUI($item->lead_status_name, $item->id, $lastContactDate);
            $data_item['last_contact_date'] = $lastContactDate;
            // dd($data_item);
            $data_item['can_edit'] = $isAdminOrAgent;
            $data_item['last_call'] = $item->last_call;
            $data_item['last_call_timing'] = $item->last_call_timing;
            $data_item['agent_name'] = $item->agent_name;
            $data_item['interactions_count'] = $item->interactions_count;
            $data_item['last_remark'] = $item->last_remark ?? 'N/A';
            $data_item['can_approve'] = $isAdmin;
            $data_item['duplicate_contact'] = isset($item->duplicate_contact_info) ? $item->duplicate_contact_info : '';
            $data_item['utm_source'] = $item->utm_source ?? 'N/A';
            $data_item['utm_medium'] = $item->utm_medium ?? 'N/A';
            $data_item['utm_campaign'] = $item->utm_campaign ?? 'N/A';
            $data_item['utm_term'] = $item->utm_term ?? 'N/A';
            $data_item['utm_content'] = $item->utm_content ?? 'N/A';
            return $data_item;
        };
    }

    public function getExtraConfig(Request $request)
    {
        $columns = $request->query('columns');
        $order = $request->query('order');
        $search = $request->query('search');

        $extraConfig = ['q' => ''];


        if (!empty($columns) && is_array($columns)) {
            //build the where clauses
            $wheres = [];
            foreach ($columns as $col) {
                if ($col['searchable'] && isset($col['search']) && isset($col['search']['value']) && !empty($col['search']['value'])) {
                    $wheres[$col['name']] = $col['search']['value'];
                }
            }

            $extraConfig['wheres'] = $wheres;
        }

        if (!empty($order) && is_array($order) && !empty($order[0])) {
            if (!empty($columns) && is_array($columns) && !empty($columns[$order[0]['column']])) {
                $sortBy = $columns[$order[0]['column']]['name'];
                $extraConfig['sort'] = $sortBy;
                $extraConfig['dir'] = $order[0]['dir'];
            }
        }

        if (!empty($order) && !is_array($order)) {
            $extraConfig['sort'] = $this->getMappedOrderColumn($order);
            $extraConfig['dir'] = $request->get('dir', 'ASC');
        }

        if (!empty($search) && is_array($search) && !empty($search['value'])) {
            $extraConfig['q'] = $search['value'];
        } else if (request()->has('q')) {
            $extraConfig['q'] = request()->get('q');
        }

        $extraConfig['vt'] = $request->get('vt');

        $columnsToConvertToArray = ["utmSources", "utmCampaigns", "leadStatuses"];

        foreach (
            [
                "llc",
                "ot",
                "loc",
                "t",
                "be",
                "status",
                "agent",
                "pantry",
                "pf",
                "pt",
                "lastContactDateFrom",
                "lastContactDateTo",
                "leadCreationDateFrom",
                "leadCreationDateTo",
                "leadDecisionDateFrom",
                "leadDecisionDateTo",
                "leadSources",
                "createdByMe",
                "createdBySomeoneElse",
                "priceFrom",
                "priceTo",
                "dashboardType",
                "callResponse",
                "utmSources",
                "utmCampaigns",
                "leadStatuses",
                "rating",
                "hasDeals"
            ] as $ef
        ) {
            if ($request->has($ef)) {
                $extraConfig[$ef] = $request->get($ef);
                if (in_array($ef, $columnsToConvertToArray) && !empty(trim($extraConfig[$ef]))) {
                    $extraConfig[$ef] = explode(",", $extraConfig[$ef]);
                }
            }
        }

        return $extraConfig;
    }

    public function getExtraConfigFromExportRequest($request)
    {
        $columnsToConvertToArray = ["utmSources", "utmCampaigns", "leadStatuses"];
        foreach (
            [
                "llc",
                "ot",
                "loc",
                "t",
                "status",
                "agent",
                "pantry",
                "pf",
                "pt",
                "lastContactDateFrom",
                "lastContactDateTo",
                "leadCreationDateFrom",
                "leadCreationDateTo",
                "leadDecisionDateFrom",
                "leadDecisionDateTo",
                "leadSources",
                "vt",
                "createdByMe",
                "createdBySomeoneElse",
                "priceFrom",
                "priceTo",
                "callResponse",
                "utmSources",
                "utmCampaigns",
                "leadStatuses",
                "rating",
                "hasDeals"
            ] as $ef
        ) {
            if ($request->has($ef)) {
                $extraConfig[$ef] = $request->get($ef);
                if (in_array($ef, $columnsToConvertToArray) && !empty(trim($extraConfig[$ef]))) {
                    $extraConfig[$ef] = explode(",", $extraConfig[$ef]);
                }
            }
        }

        return $extraConfig;
    }

    private function getMappedOrderColumn($orderColumn)
    {
        switch ($orderColumn) {
            case 'lastContact':
                return 'l.last_contact_date';
            case 'created':
                return 'l.created_at';
            default:
                return $orderColumn;
        }
    }
}
