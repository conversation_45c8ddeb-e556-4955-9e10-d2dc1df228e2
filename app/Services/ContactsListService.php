<?php

namespace App\Services;

use DB;
use App\Models\Crm\ContactsList;
use App\Models\Crm\RolesDef;
use App\Models\Lead;
use Illuminate\Http\Request;

class ContactsListService extends GenericService
{
    protected function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $user = auth()->user();
        $userTagIdsAccess = $user->contactListTags->map(function ($item) {
            return $item->id;
        })->toArray();
        $isOfficeManager = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $isCallCenterAgent = $user->hasRole(RolesDef::CALL_CENTER_AGENT);
        $isTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
        // $userCanManageDubaiInvestors = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_DUBAI_INVESTORS);
        // $userCanManagePearlOwners = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_PEARL_OWNERS);
        $params = $request->all();

        $contactDuplicationType = $request->get('duplicationType', 'master');

        $qb = DB::table('contacts as c')
            ->select([
                'c.id',
                'c.name',
                'c.gender',
                'c.nationality_id',
                'c.date_of_birth',
                'c.qatar_id_no',
                'c.occupation',
                'c.company_name',
                'c.position',
                'c.residency',
                'c.email_1',
                'c.prefix_mobile_1',
                'c.mobile_1',
                'c.mobile_2',
                'c.verified',
                'r.id as reminders',
                DB::raw("DATE_FORMAT(c.created_at, '%Y-%m-%d') as created_at"),
                'u.name as agent_name',
                DB::raw('GROUP_CONCAT(DISTINCT ct.id) as tags'),
                DB::raw('GROUP_CONCAT(DISTINCT ct.label SEPARATOR "||") as tag_labels'),
                'cl.id as landlord_id',
                DB::raw('GROUP_CONCAT(l.id) as leads_ids'),
                DB::raw('GROUP_CONCAT(d.id) as deals_ids'),
                'ctasks.call_response as last_call_response',
                'ctasks.call_notes as last_call_notes',
                'ctasks.created_at as last_call_timing',
            ])
            ->leftJoin('nationalities as n', 'c.nationality_id', '=', 'n.id')
            ->leftJoin('contacts_list_x_tags as cxt', 'cxt.contacts_list_id', '=', 'c.id')
            ->leftJoin('contacts_list_tags as ct', 'ct.id', '=', 'cxt.contacts_list_tag_id')
            ->leftJoin('contacts_landlords as cl', 'cl.contact_id', '=', 'c.id')
            ->leftJoin('leads as l', 'l.contact_id', '=', 'c.id')
            ->leftJoin('deals as d', 'd.lead_id', '=', 'l.id')
            ->leftJoin('contacts_agents_connection as cac', 'cac.contact_id', '=', 'c.id')
            ->leftJoin('users as u', 'u.id', '=', 'c.created_by')
            ->leftJoin('reminders as r', function ($qb) {
                $qb->on('c.id', '=', 'r.object_id')
                    ->where('r.object_type', '=', 'contacts_list');
            })
            ->leftJoin(DB::raw("(SELECT MAX(created_at) as max_created_at, object_id, call_response FROM tasks WHERE object_type LIKE 'contact' GROUP BY object_id) as tmd"), 'tmd.object_id', '=', 'c.id')
            ->leftJoin('tasks as ctasks', function ($qb) {
                $qb->on('ctasks.created_at', '=', 'tmd.max_created_at')->on('ctasks.object_id', '=', 'c.id')
                    ->where('ctasks.object_type', '=', 'contact');
            })
            ->offset(0)
            ->orderBy('c.updated_at', 'DESC')
            ->groupBy('c.id')
            ->limit($params['limit'] ?? 20);

        $term = '';
        if ($request->has('q')) {
            $term = strtolower(trim($request->get('q'))) ?? '';
        }

        $exportType = $request->get('exportType');
        if ($exportType == 'whatsapp') {
            $qb->whereRaw(DB::raw('((c.prefix_mobile_1 IS NULL AND c.mobile_1 LIKE "+%") OR (c.prefix_mobile_1 IS NOT NULL AND c.mobile_1 NOT LIKE "+%"))'));
        }
        if ($exportType == 'marketing_platform') {
            $qb->whereNotNull('c.email_1')->where('c.email_1', '!=', '');
            // $qb->whereRaw(DB::raw('(c.email_1 IS NOT NULL OR c.email_2 IS NOT NULL)'));
        }

        if (!empty($term)) {
            $percentTerm = "%" . $term . "%";
            $qb->where(function ($sql) use ($term, $percentTerm) {
                $sql->where('c.id', '=', $term)
                    ->orWhere('c.name', 'LIKE', $percentTerm)
                    ->orWhere('c.company_name', 'LIKE', $percentTerm)
                    ->orWhere('c.position', 'LIKE', $percentTerm)
                    ->orWhere('c.occupation', 'LIKE', $percentTerm)
                    ->orWhere('c.residency', 'LIKE', $percentTerm)
                    ->orWhere('c.qatar_id_no', 'LIKE', $percentTerm)
                    ->orWhere('c.mobile_1', 'LIKE', $percentTerm)
                    ->orWhere('c.mobile_2', 'LIKE', $percentTerm)
                    ->orWhere('c.email_1', 'LIKE', $percentTerm)
                    ->orWhere('n.name', 'LIKE', $percentTerm);
            });
        }
        if ($contactDuplicationType == 'duplicate') {
            $qb->whereNotNull('master_contact_id');
        } else {
            $qb->whereNull('master_contact_id');
        }
        if (isset($params['tags']) && !empty(trim($params['tags']))) {
            $tags = explode(',', $params['tags']);
            if (count($tags) > 0) {
                foreach ($tags as $tagIndex => $tagId) {
                    $qb->join('contacts_list_x_tags as cxt' . $tagIndex, 'cxt' . $tagIndex . '.contacts_list_id', '=', 'c.id');
                    $qb->where('cxt' . $tagIndex . '.contacts_list_tag_id', $tagId);
                }
                // $qb->whereIn('cxt.contacts_list_tag_id', $tags);
            }
        }
        if (isset($params['master_contact_id'])) {
            $qb->where('c.master_contact_id', $params['master_contact_id']);
        }
        if (isset($params['verified']) && $params['verified'] == 1) {
            $qb->where('c.verified', 1);
        }
        if (isset($params['isLandlord']) && $params['isLandlord'] == 1) {
            $qb->whereNotNull('cl.id');
        }
        if (isset($params['hasLeads']) && $params['hasLeads'] == 1) {
            $qb->whereNotNull('l.id');
        }
        if (isset($params['callLog'])) {
            $qb->where('ctasks.call_response', $params['callLog']);
        }

        // New filter parameters - join with leads table when needed
        $needsLeadsJoin = false;
        if (isset($params['operationType']) && !empty($params['operationType'])) {
            $needsLeadsJoin = true;
        }
        if (isset($params['propertyTypes']) && !empty($params['propertyTypes'])) {
            $needsLeadsJoin = true;
        }
        if (isset($params['budgetFrom']) || isset($params['budgetTo'])) {
            $needsLeadsJoin = true;
        }

        // Add additional leads join if needed for filtering
        if ($needsLeadsJoin) {
            // We already have a left join with leads, but we need to ensure we filter properly
            // When filtering by lead properties, we only want contacts that have leads
            $qb->whereNotNull('l.id');

            if (isset($params['operationType']) && !empty($params['operationType'])) {
                $qb->where('l.filter_operation_type', $params['operationType']);
            }

            if (isset($params['propertyTypes']) && !empty($params['propertyTypes'])) {
                $propertyTypes = explode(',', $params['propertyTypes']);
                $qb->whereIn('l.filter_property_type', $propertyTypes);
            }

            if (isset($params['budgetFrom']) && is_numeric($params['budgetFrom'])) {
                $qb->where(function($query) use ($params) {
                    $query->where('l.filter_budget_min', '>=', $params['budgetFrom'])
                          ->orWhere('l.filter_budget_max', '>=', $params['budgetFrom'])
                          ->orWhere('l.budget', '>=', $params['budgetFrom']);
                });
            }

            if (isset($params['budgetTo']) && is_numeric($params['budgetTo'])) {
                $qb->where(function($query) use ($params) {
                    $query->where('l.filter_budget_min', '<=', $params['budgetTo'])
                          ->orWhere('l.filter_budget_max', '<=', $params['budgetTo'])
                          ->orWhere('l.budget', '<=', $params['budgetTo']);
                });
            }
        }

        // Date created filters
        if (isset($params['dateCreatedFrom']) && !empty($params['dateCreatedFrom'])) {
            $qb->whereDate('c.created_at', '>=', $params['dateCreatedFrom']);
        }

        if (isset($params['dateCreatedTo']) && !empty($params['dateCreatedTo'])) {
            $qb->whereDate('c.created_at', '<=', $params['dateCreatedTo']);
        }

        if (!isset($params['master_contact_id'])) {
            if ((!$isOfficeManager && !$isCallCenterAgent) || !request()->has('vt') || (request()->has('vt') && request()->get('vt') != 'master')) {
                if ($isTeamLeader) {
                    $qb->where(function ($sqlqb) {
                        $sqlqb->where('c.created_by', auth()->user()->id)
                            ->orWhere('u.team_leader_id', auth()->user()->id)
                            ->orWhere(function ($sqlqb1) {
                                $sqlqb1->orWhereNotNull('cac.id')->where('cac.user_id', auth()->user()->id);
                            });
                    });
                } else {
                    $qb->where(function ($sqlqb) use ($user, $userTagIdsAccess) {
                        $sqlqb->where('c.created_by', $user->id)
                            ->orWhere(function ($sqlqb1) {
                                $sqlqb1->orWhereNotNull('cac.id')->where('cac.user_id', auth()->user()->id);
                            });
                        if (count($userTagIdsAccess)) {
                            $sqlqb->orWhereIn('ct.id', $userTagIdsAccess);
                        }
                        // if($userCanManageDubaiInvestors) {
                        //     $sqlqb->orWhere('ct.label', 'Dubai Investors');
                        // }
                        // if($userCanManagePearlOwners) {
                        //     $sqlqb->orWhere('ct.label', 'Pearl Owner');
                        // }
                    });
                }
                // else {
                //     $qb->where(function ($sqlqb) {
                //         $sqlqb->where('c.created_by', auth()->user()->id)
                //             ->orWhere(function ($sqlqb1) {
                //                 $sqlqb1->orWhereNotNull('cac.id')->where('cac.user_id', auth()->user()->id);
                //             });
                //     });
                // }
            }
        }

        if (isset($extraClauses['id'])) {
            $qb->where('c.id', $extraClauses['id']);
        }
        // dd($qb->toSql());
        // die;
        return $qb;
    }

    public function toggleStar($validFields)
    {
        $contact = ContactsList::where('id', $validFields['contact_id'])->first();
        if (!is_null($contact)) {
            $contact->verified = $validFields['verified'] == false ? false : true;
            $contact->save();
            return $contact->toArray();
        }
        return $contact;
    }

    protected function mapRecords($records)
    {
        return $records;
    }

    public function getSingleContact($id, $leadId)
    {
        $user = auth()->user();
        $userTagIdsAccess = $user->contactListTags->map(function ($item) {
            return $item->id;
        })->toArray();
        $isAgent = $user->hasRole(RolesDef::AGENT);
        $isOfficeManager = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $isCallCenterAgent = $user->hasRole(RolesDef::CALL_CENTER_AGENT);
        // $userCanManageDubaiInvestors = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_DUBAI_INVESTORS);
        // $userCanManagePearlOwners = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_PEARL_OWNERS);

        $isLeadAgent = false;

        if (!$isOfficeManager && !$isCallCenterAgent && $isAgent) {
            // check if the agent is assigned to one of the leads of the contact
            $leads = Lead::with('latestAssignment')->where('contact_id', $id)->get();
            foreach ($leads as $l) {
                if (!is_null($l->latestAssignment) && $l->latestAssignment->user_id == $user->id) {
                    $isLeadAgent = true;
                } else if ($l->id == $leadId) {
                    $isLeadAgent = true;
                }
            }
        }

        $qb = DB::table('contacts as c')
            ->select([
                'c.id',
                'c.name',
                'c.gender',
                'c.nationality_id',
                'c.date_of_birth',
                'c.qatar_id_no',
                'c.occupation',
                'c.company_name',
                'c.position',
                'c.residency',
                'c.email_1',
                'c.prefix_mobile_1',
                'c.mobile_1',
                'c.prefix_mobile_2',
                'c.mobile_2',
                'c.verified',
                'c.metadata',
                DB::raw('GROUP_CONCAT(DISTINCT ct.id) as tags'),
                DB::raw('GROUP_CONCAT(DISTINCT ct.label SEPARATOR "||") as tag_labels'),
                'cl.id as landlord_id',
                DB::raw('GROUP_CONCAT(l.id) as leads_ids'),
                DB::raw('GROUP_CONCAT(d.id) as deals_ids')
            ])
            ->leftJoin('nationalities as n', 'c.nationality_id', '=', 'n.id')
            ->leftJoin('contacts_list_x_tags as cxt', 'cxt.contacts_list_id', '=', 'c.id')
            ->leftJoin('contacts_list_tags as ct', 'ct.id', '=', 'cxt.contacts_list_tag_id')
            ->leftJoin('contacts_landlords as cl', 'cl.contact_id', '=', 'c.id')
            ->leftJoin('leads as l', 'l.contact_id', '=', 'c.id')
            ->leftJoin('deals as d', 'd.lead_id', '=', 'l.id')
            ->leftJoin('contacts_agents_connection as cac', 'cac.contact_id', '=', 'c.id')
            ->offset(0)
            ->groupBy('c.id')
            ->where('c.id', $id);

        if (!$isOfficeManager && !$isCallCenterAgent && !$isLeadAgent) {
            $qb->where(function ($iqb) use ($user, $userTagIdsAccess) {
                $iqb->where(function ($iiqb) use ($user) {
                    $iiqb->where('c.created_by', $user->id)
                        ->orWhere('cac.user_id', $user->id);
                });
                if (count($userTagIdsAccess)) {
                    $iqb->orWhereIn('ct.id', $userTagIdsAccess);
                }
                // if ($userCanManageDubaiInvestors) {
                //     $iqb->orWhere('ct.label', 'Dubai Investors');
                // }
                // if ($userCanManagePearlOwners) {
                //     $iqb->orWhere('ct.label', 'Pearl Owner');
                // }
            });
        }
        // else {
        //     if (!$isLeadAgent) {
        //         if ($userCanManageDubaiInvestors || $userCanManagePearlOwners) {
        //             if (!$userCanManagePearlOwners) {
        //                 $qb->where('ct.label', 'Dubai Investors');
        //             } else if (!$userCanManageDubaiInvestors) {
        //                 $qb->where('ct.label', 'Pearl Owner');
        //             } else {
        //                 $qb->where(function ($sqlq) {
        //                     $sqlq->where('ct.label', 'Dubai Investors')
        //                         ->orWhere('ct.label', 'Pearl Owner');
        //                 });
        //             }
        //         }
        //     }
        // }


        return $qb->first();
    }
}
