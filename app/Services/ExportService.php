<?php
namespace App\Services;

use App\Models\Admin\ExportPlatform;
use App\Models\Admin\ExportAssignment;

use App\Models\Admin\SnapshotExportAssignment;
use App\Models\Property;
use App\Models\PropertySnapshot;
use Log;
use DB;
class ExportService {
    /**
     * Returns an ExportPlatform by its export_url_tag
     * @param string $tag
     * @return ExportPlatform
     */
    public function getExportPlatformByTag($tag) {
        return ExportPlatform::where("export_url_tag", $tag)->where('is_active', 1)->first();
    }

    public function assignPropertyToPlatform($propertyId, $platformId) {
        $assignment = ExportAssignment::firstOrNew(["property_id" => $propertyId, "export_platform_id" => $platformId]);
        $assignment->save();
        Log::info("Created ExportAssignment", compact('assignment'));
    }

    public function assignPropertySnapshotToPlatform($snapshotId, $platformId) {
        $assignment = SnapshotExportAssignment::firstOrNew(["snapshot_id" => $snapshotId, "export_platform_id" => $platformId]);
        $assignment->save();
        Log::info("Created ExportAssignment", compact('assignment'));
    }

    public function removePropertyFromPlatform($propertyId, $platformId) {
        $assignment = ExportAssignment::where("property_id", $propertyId)->where("export_platform_id", $platformId)->first();

        if ($assignment) {
            Log::info("Deleting ExportAssignment", compact('assignment'));
            $assignment->delete();
        }
    }

    public function removePropertySnapshotFromPlatform($snapshotId, $platformId) {
        $assignment = SnapshotExportAssignment::where("snapshot_id", $snapshotId)->where("export_platform_id", $platformId)->first();

        if ($assignment) {
            Log::info("Deleting ExportAssignment", compact('assignment'));
            $assignment->delete();
        }
    }

    public function handlePropertyCreate($property) {
        Log::info("Export Service: Property create!");
    }

    public function handlePropertyUpdate($property) {
        Log::info("Export Service: Property update!");
    }

    public function handlePropertyDelete($property) {
        Log::info("Export Service: Property delete!");
    }

    public function handlePropertyRestore($property) {
        Log::info("Export Service: Property restore!");
    }

    public function flushQueue($queue, $xml) {
        $queue->last_export_at = date_now();
        $queue->last_export_content = $xml;
        $queue->save();
    }

    /**
     * Returns a Collection of all the available ExportPlatforms
     * @return Collection
     */
    public function allExportPlatforms() {
        return ExportPlatform::all();
    }

    public function exportAllListingsToPlatform() {
        // get all published listings
        $allPlatforms = $this->allExportPlatforms()
            ->filter(function($p) { return in_array($p->export_url_tag, ["saakin"]); })
            ->map(function($p) { return $p->id; });

        $snapshotData = PropertySnapshot::whereNull('deleted_at')->select(["id", "listing_id"])->get()->toArray();
        foreach ($allPlatforms as $platformId) {
            foreach ($snapshotData as $data) {
                $this->assignPropertyToPlatform($data['listing_id'], $platformId);
                $this->assignPropertySnapshotToPlatform($data['id'], $platformId);
            }
        }
    }
}
