<?php

namespace App\Services;

use App\Models\MarketingCampaign;
use App\Models\MarketingProject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MarketingProjectService extends GenericService
{
    public function getAll()
    {
        $projects = MarketingProject::get();
        return $projects;
    }

    public function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $extraClause = $this->getRequestParams($request);

        $q = DB::table('marketing__projects as projects');

        if (!empty($extraClause['sort'])) {
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($extraClause['sort'], $extraClause['dir']);
            } else {
                $q = $q->orderBy($extraClause['sort']);
            }
        } else {
            $q = $q->orderBy('projects.created_at', 'desc');
        }

        if (!empty($extraClause['q'])) {
            $q->where(function ($sql) use ($extraClause) {
                $term = trim($extraClause['q']) . '%';
                $sql->where('projects.id', 'like', $term)
                    ->orWhere('projects.name', 'like', $term)
                    ->orWhere('projects.created_at', 'like', $term)
                    ->orWhere('users.name', 'like', $term)
                ;
            });
        }
        $q->join('users', 'projects.created_by', '=', 'users.id');
        $q->leftJoin('marketing__campaigns', 'projects.id', '=', 'marketing__campaigns.marketing_project_id');
        $q->select(['projects.*', 'users.name as createdBy']);

        $selectableFields = $this->getSelectableFields($request, $q);
        $q->select($selectableFields);

        // Add GROUP BY clause to ensure we only get rows for actual projects
        $q->groupBy('projects.id');

        return $q;
    }


    private function getSelectableFields()
    {
        $selectableFields = [
            'projects.id as id',
            'projects.name',
            'projects.created_at',
            'projects.created_by',
            'users.name as createdBy',
            DB::raw('COUNT(marketing__campaigns.id) as campaigns_count')
        ];

        return $selectableFields;
    }

    /**
     * Override the parent getTableItemsCount method to ensure we count only actual projects
     */
    public function getTableItemsCount(Request $request, $extraClauses = [])
    {
        $q = $this->getTableItemsBaseQ($request, $extraClauses);

        // We need to count distinct project IDs to get the correct count
        $count = $q->getCountForPagination();

        return $count;
    }

    public function destroyProject($id)
    {
        $project = MarketingProject::where('id', $id)->first();
        if (!is_null($project)) {
            // The cascading delete will handle the campaigns
            MarketingProject::destroy($id);
        }
    }

    public function destroyCampaign($id)
    {
        $campaign = MarketingCampaign::where('id', $id)->first();
        if (!is_null($campaign)) {
            MarketingCampaign::destroy($id);
        }
    }
}
