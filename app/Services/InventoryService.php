<?php

namespace App\Services;

use App\Services\PropertyTypesService;
use App\Models\Admin\ExportAssignment;
use App\Models\Admin\SnapshotExportAssignment;
use App\Models\AttachmentAssignment;
use App\Models\Attribute;
use App\Models\AttributeDefinition;
use App\Models\CacheKeys;
use App\Models\Crm\ListingPublishingStatus;
use App\Models\Crm\PermissionsDef;
use App\Models\Crm\RolesDef;
use App\Models\InventoryTag;
use App\Models\PFSyncQueue;
use App\Models\Property;
use App\Models\PropertySnapshot;
use App\Models\PropertySnapshotAttribute;
use App\Models\PropertySnapshotTranslation;
use App\Models\PropertyTranslation;
use App\Models\PropertyView;
use App\Models\QueryParamsDef;
use App\Models\RequestParamsMap;
use App\Models\RouteListingItem;
use App\Models\Timings;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;
use DB;
use Log;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log as FacadesLog;

class InventoryService extends GenericService
{
    public $purifierService = null;

    private $attributeDefinitionIds = [
        'property-features-balcony' => 0,
        'property-features-kitchen' => 0,
        'property-features-pantry' => 0,
        'property-features-bills' => 0,
        'property-features-furnishings' => 0,
        'property-features-furnishings-office' => 0,

        'property-features-build-up-area' => 0,
        'property-features-parking-info' => 0,
        'property-features-construction-year' => 0,
        'property-features-bathrooms' => 0,
        'property-features-bedrooms' => 0,
        'property-features-service_charge' => 0
    ];

    public $llCategories = [
        0 => 'Private',
        1 => 'Corporate'
    ];

    public $rentSaleOptions = [
        'rent' => 'Rent',
        'sale' => 'Sale'
    ];

    public $bedroomsNextOptions = [
        ['value' => 's', 'label' => 'Studio'],
        ['value' => '1', 'label' => '1'],
        ['value' => '1+', 'label' => '1 + office'],
        ['value' => '2', 'label' => '2'],
        ['value' => '2+', 'label' => '2 + maid'],
        ['value' => '3', 'label' => '3'],
        ['value' => '3+', 'label' => '3 + maid'],
        ['value' => '4', 'label' => '4'],
        ['value' => '4+', 'label' => '4 + maid'],
        ['value' => '5', 'label' => '5'],
        ['value' => '5+', 'label' => '5 + maid'],
        ['value' => '6', 'label' => '6'],
        ['value' => '6+', 'label' => '6 + maid'],
        ['value' => '7', 'label' => '7'],
        ['value' => '7+', 'label' => '7 + maid'],
        ['value' => '8', 'label' => '8'],
        ['value' => '8+', 'label' => '8+'],
        ['value' => '9', 'label' => '9'],
        ['value' => '9+', 'label' => '9+'],
        ['value' => '10', 'label' => '10'],
        ['value' => '10+', 'label' => '10+']
    ];

    public $furnishingOptions = [
        ['value' => 'unfurnished', 'label' => 'Unfurnished'],
        ['value' => 'semi-furnished', 'label' => 'Semi furnished'],
        ['value' => 'fully-furnished', 'label' => 'Fully furnished'],
        ['value' => 'fully-equiped', 'label' => 'Fully equiped']
    ];

    public $billsOptions = [
        ['value' => 'all-excluded', 'label' => 'All Excluded'],
        ['value' => 'all-included', 'label' => 'All Included except internet'],
        ['value' => 'karamaa-included', 'label' => 'Karamaa Included'],
        ['value' => 'qatar-cool-included', 'label' => 'Qatar Cool Included'],
        ['value' => 'internet-included', 'label' => 'Internet included'],
    ];

    public $minRentPrices = [
        ['key' => '4000', 'value' => '4.000 '],
        ['key' => '5000', 'value' => '5.000 '],
        ['key' => '6000', 'value' => '6.000'],
        ['key' => '7000', 'value' => '7.000'],
        ['key' => '8000', 'value' => '8.000'],
        ['key' => '9000', 'value' => '9.000'],
        ['key' => '10000', 'value' => '10.000'],
        ['key' => '11000', 'value' => '11.000'],
        ['key' => '12000', 'value' => '12.000'],
        ['key' => '13000', 'value' => '13.000'],
        ['key' => '14000', 'value' => '14.000'],
        ['key' => '15000', 'value' => '15.000'],
        ['key' => '16000', 'value' => '16.000'],
        ['key' => '17000', 'value' => '17.000'],
        ['key' => '18000', 'value' => '18.000'],
        ['key' => '19000', 'value' => '19.000'],
        ['key' => '20000', 'value' => '20.000'],
    ];

    public $maxRentPrices = [
        ['key' => '6000', 'value' => '6.000'],
        ['key' => '7000', 'value' => '7.000'],
        ['key' => '8000', 'value' => '8.000'],
        ['key' => '9000', 'value' => '9.000'],
        ['key' => '10000', 'value' => '10.000'],
        ['key' => '12000', 'value' => '12.000'],
        ['key' => '14000', 'value' => '14.000'],
        ['key' => '16000', 'value' => '16.000'],
        ['key' => '18000', 'value' => '18.000'],
        ['key' => '20000', 'value' => '20.000'],
        ['key' => '22000', 'value' => '22.000'],
        ['key' => '24000', 'value' => '24.000'],
        ['key' => '26000', 'value' => '26.000'],
        ['key' => '28000', 'value' => '28.000'],
        ['key' => '30000', 'value' => '30.000'],
        ['key' => '40000', 'value' => '40.000'],
        ['key' => '50000', 'value' => '50.000'],
        ['key' => '60000', 'value' => '60.000'],
        ['key' => '70000', 'value' => '70.000'],
        ['key' => '80000', 'value' => '80.000'],
        ['key' => '90000', 'value' => '90.000'],
        ['key' => '100000', 'value' => '100.000'],
        ['key' => '120000', 'value' => '120.000'],
        ['key' => '150000', 'value' => '150.000'],
        ['key' => '170000', 'value' => '170.000'],
        ['key' => '200000', 'value' => '200.000'],
    ];

    public $minSalePrices = [
        ['key' => '800000', 'value' => '800.000'],
        ['key' => '900000', 'value' => '900.000'],
        ['key' => '1000000', 'value' => '1.000.000'],
        ['key' => '1500000', 'value' => '1.500.000'],
        ['key' => '2000000', 'value' => '2.000.000'],
        ['key' => '2500000', 'value' => '2.500.000'],
        ['key' => '3000000', 'value' => '3.000.000'],
        ['key' => '3500000', 'value' => '3.500.000'],
        ['key' => '4000000', 'value' => '4.000.000'],
        ['key' => '4500000', 'value' => '4.500.000'],
        ['key' => '1000000', 'value' => '5.000.000'],

    ];

    public $maxSalePrices = [
        ['key' => '950000', 'value' => '950.000'],
        ['key' => '1000000', 'value' => '1.000.000'],
        ['key' => '1750000', 'value' => '1.750.000'],
        ['key' => '2000000', 'value' => '2.000.000'],
        ['key' => '2750000', 'value' => '2.750.000'],
        ['key' => '3000000', 'value' => '3.000.000'],
        ['key' => '3750000', 'value' => '3.750.000'],
        ['key' => '4000000', 'value' => '4.000.000'],
        ['key' => '4750000', 'value' => '4.750.000'],
        ['key' => '5000000', 'value' => '5.000.000'],
        ['key' => '6000000', 'value' => '6.000.000'],
        ['key' => '7000000', 'value' => '7.000.000'],
        ['key' => '8000000', 'value' => '8.000.000'],
        ['key' => '9000000', 'value' => '9.000.000'],
        ['key' => '10000000', 'value' => '10.000.000'],
        ['key' => '11000000', 'value' => '11.000.000'],
        ['key' => '12000000', 'value' => '12.000.000'],
        ['key' => '13000000', 'value' => '13.000.000'],
        ['key' => '14000000', 'value' => '14.000.000'],
        ['key' => '15000000', 'value' => '15.000.000'],
        ['key' => '16000000', 'value' => '16.000.000'],
        ['key' => '17000000', 'value' => '17.000.000'],
        ['key' => '18000000', 'value' => '18.000.000'],
        ['key' => '19000000', 'value' => '19.000.000'],
        ['key' => '20000000', 'value' => '20.000.000'],
        ['key' => '50000000', 'value' => '50.000.000'],
        ['key' => '100000000', 'value' => '100.000.000'],
        ['key' => '150000000', 'value' => '150.000.000'],

    ];

    public $operationTypeOptions = [
        ['value' => 'rent', 'label' => 'Rent'],
        ['value' => 'sale', 'label' => 'Sale']
    ];

    private $propertiesService;
    private $emailService;
    private $propertyTypesService;
    private $exportService;

    public function __construct(
        PropertiesService $propertiesService,
        EmailService $emailService,
        PropertyFinderService $propertyFinderService,
        OperationHistoryService $operationHistoryService,
        PropertyTypesService $propertyTypesService,
        ExportService $exportService,
    ) {
        $this->propertiesService = $propertiesService;
        $this->emailService = $emailService;
        $this->propertyFinderService = $propertyFinderService;
        $this->operationHistoryService = $operationHistoryService;
        $this->propertyTypesService = $propertyTypesService;
        $this->exportService = $exportService;
    }

    public function getFilterAttributeValueLists()
    {
        //        Cache::forget(CacheKeys::FILTER_ATTRIBUTE_VALUE_LISTS);
        $attributeValueLists = Cache::remember(CacheKeys::FILTER_ATTRIBUTE_VALUE_LISTS, Timings::DAY, function () {
            return AttributeDefinition::with(['allowedValues'])
                ->whereIn('name', [
                    'property-features-bedrooms',
                    'property-features-bathrooms',
                    'property-features-furnishings',
                    'property-features-furnishings-office',
                    'property-features-furnishings-retail',
                    'property-features-kitchen',
                    'property-features-pantry',
                    'property-features-bills'
                ])
                ->get()
                ->mapWithKeys(function ($definition) {
                    return [$definition->name => $definition->allowedValues->map(
                        function ($allowedValue) {
                            return ['value' => $allowedValue->value, 'label' => $allowedValue->label];
                        }
                    )->toArray()];
                });
        });

        return $attributeValueLists;
    }

    public function getPropertyViews()
    {
        return PropertyView::get();
    }

    protected function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $attributeDefinitionIds = $this->getAttributeDefinitions();
        $q = DB::table('properties')
            ->leftJoin('contacts', 'properties.contact_id', '=', 'contacts.id')
            ->leftJoin('contacts_landlords as cl', 'contacts.id', '=', 'cl.contact_id')
            ->leftJoin('users', 'properties.created_by', '=', 'users.id')
            ->leftJoin('assets', 'properties.asset_id', '=', 'assets.id')
            ->leftJoin('geography as location', 'properties.location_id', '=', 'location.id')
            ->leftJoin('towers', 'properties.tower_id', '=', 'towers.id')
            ->leftJoin('properties_stared as propst', 'properties.id', '=', 'propst.listing_id')
            ->leftJoin('users as propst_users', 'propst_users.id', '=', 'propst.user_id')
            ->leftJoin('inventory_x_tags as ixt', 'properties.id', '=', 'ixt.listing_id')
            ->leftJoin('inventory_tags as it', 'it.id', '=', 'ixt.inventory_tag_id')

            ->leftJoin('reminders', function ($qb) {
                $qb->on('properties.asset_id', '=', 'reminders.object_id')
                    ->where('reminders.object_type', '=', 'properties');
            })
            ->leftJoin('export_assignments as ea', function ($qb) {
                $qb->on('properties.id', '=', 'ea.property_id')
                    ->where('ea.export_platform_id', '=', '4');
            })
            ->join('property_types as pt', 'properties.property_type_id', '=', 'pt.id')
            ->leftJoin('users as uc', 'contacts.created_by', '=', 'uc.id')
            ->leftJoin('attributes as be', function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'be.asset_id')
                    ->where('be.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bedrooms']);
            })

            ->leftJoin('attributes as ba', function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'ba.asset_id')
                    ->where('ba.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bathrooms']);
            })
            ->leftJoin('property_snapshots as ps', 'ps.listing_id', '=', 'properties.id');

        $furnishingDefinitionId = $attributeDefinitionIds['property-features-furnishings'];
        $q->leftJoin("attributes AS furn_attr", function ($join) use ($furnishingDefinitionId) {
            $join->on('properties.asset_id', '=', 'furn_attr.asset_id')
                ->where('furn_attr.attribute_definition_id', '=', $furnishingDefinitionId);
        });

        $q->leftJoin("attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
            $join->on('properties.asset_id', '=', 'furn_attr_office.asset_id')
                ->where('furn_attr_office.attribute_definition_id', '=', $attributeDefinitionIds['property-features-furnishings-office']);
        });
        if (!empty($request->get(QueryParamsDef::FURNISHINGS))  && !empty($request->get(QueryParamsDef::FURNISHINGS_OFFICE))) {
            $param = QueryParamsDef::FURNISHINGS;
            $valueFurnishing = $request->get($param);
            if (str_contains($valueFurnishing, ",")) {
                $valueFurnishing = explode(",", $valueFurnishing);
            }

            $param = QueryParamsDef::FURNISHINGS_OFFICE;
            $valueFurnishingOffice = $request->get($param);
            if (str_contains($valueFurnishingOffice, ",")) {
                $valueFurnishingOffice = explode(",", $valueFurnishingOffice);
            }
            $q->where(function ($furnQb) use ($valueFurnishing, $valueFurnishingOffice) {
                if (is_array($valueFurnishing)) {
                    $furnQb->whereIn("furn_attr.value", $valueFurnishing);
                } else {
                    $furnQb->where("furn_attr.value", $valueFurnishing);
                }
                if (is_array($valueFurnishingOffice)) {
                    $furnQb->orWhereIn("furn_attr_office.value", $valueFurnishingOffice);
                } else {
                    $furnQb->orWhere("furn_attr_office.value", $valueFurnishingOffice);
                }
            });
        } else if (!empty($request->get(QueryParamsDef::FURNISHINGS))) {
            $propertyTypes = collect([]);
            if ($request->has(QueryParamsDef::PROPERTY_TYPE) && !empty($request->get(QueryParamsDef::PROPERTY_TYPE))) {
                $propertyTypeId = $request->get(QueryParamsDef::PROPERTY_TYPE);
                $propertyTypes = $this->propertyTypesService->getCachedTypes()->filter(function ($propertyType) use ($propertyTypeId) {
                    return $propertyType->id == $propertyTypeId && $propertyType->filter_value == 'office';
                });
            }
            // dd($propertyTypes);
            if ($propertyTypes->count() > 0) {
                $param = QueryParamsDef::FURNISHINGS_OFFICE;
                if (!empty($request->get($param))) {
                    $urlValue = $request->get($param);
                    if (str_contains($urlValue, ",")) {
                        $dbValues = explode(",", $urlValue);
                        $q->whereIn("furn_attr_office.value", $dbValues);
                    } else {
                        $q->where("furn_attr_office.value", "=", $urlValue);
                    }
                }
            } else {
                $param = QueryParamsDef::FURNISHINGS;
                if (!empty($request->get($param))) {
                    $urlValue = $request->get($param);
                    if (str_contains($urlValue, ",")) {
                        $dbValues = explode(",", $urlValue);
                        $q->whereIn("furn_attr.value", $dbValues);
                    } else {
                        $q->where("furn_attr.value", "=", $urlValue);
                    }
                }
            }
        } else if (!empty($request->has(QueryParamsDef::FURNISHINGS_OFFICE))) {
            $param = QueryParamsDef::FURNISHINGS_OFFICE;
            if (!empty($request->get($param))) {
                $urlValue = $request->get($param);
                if (str_contains($urlValue, ",")) {
                    $dbValues = explode(",", $urlValue);
                    $q->whereIn("furn_attr_office.value", $dbValues);
                } else {
                    $q->where("furn_attr_office.value", "=", $urlValue);
                }
            }
        }

        $extraClause = $this->getRequestParams($request);

        $param = QueryParamsDef::AMENITIES;
        if (!empty($request->get($param))) {
            $amenitiesIds = explode(",", $request->get($param));
            $q->join("attributes AS amenities", function ($join) use ($amenitiesIds) {
                $join->on('properties.asset_id', '=', 'amenities.asset_id')
                    ->whereIn('amenities.attribute_definition_id', $amenitiesIds);
            });
        }

        $param = QueryParamsDef::TAGS;
        if (!empty($request->get($param))) {
            $tags = explode(',', $request->get($param));
            if (count($tags) > 0) {
                $q->whereIn('ixt.inventory_tag_id', $tags);
            }
        }

        $param = QueryParamsDef::LOCATION_DATA;
        if (!empty($request->get($param))) {
            $locationIds = explode(",", $request->get($param));
            $q->leftJoin('geography as pg', 'pg.id', '=', 'location.parent_id')
                ->leftJoin('geography as ppg', 'ppg.id', '=', 'pg.parent_id')
                ->where(function ($sql) use ($locationIds) {
                    $sql->whereIn('properties.location_id', $locationIds)
                        ->orWhereIn('pg.id', $locationIds)
                        ->orWhereIn('ppg.id', $locationIds);
                });
        }

        if (count($extraClauses) > 0) {
            foreach ($extraClauses as $clause => $clauseValue) {
                $q = $q->where($clause, '=', $clauseValue);
            }
        }

        $q = $q->whereNull('properties.imported_at');

        foreach ([QueryParamsDef::BEDROOMS] as $t) {
            if ($request->get($t, '') != '') {
                $requestValue = $request->get($t);
                if (str_contains($requestValue, ",")) {
                    $valuesFromRequest = explode(",", $requestValue);
                    $q = $q->whereIn($t . '.value', $valuesFromRequest);
                } else {
                    $q = $q->where($t . '.value', '=', $request->get($t));
                }
            }
        }

        $param = QueryParamsDef::BATHROOMS;
        if ($request->has($param)) {
            $q = $q->where($param . '.value', '>=', $request->get($param));
        }

        foreach (['properties.location_id' => 'locationId'] as $t => $param) {
            if ($request->has($param) && !empty($request->get($param))) {
                $q = $q->where($t, '=', $request->get($param));
            }
        }

        foreach (
            [
                QueryParamsDef::PARKING => 'property-features-parking-info',
                QueryParamsDef::BALCONY => 'property-features-balcony',
                QueryParamsDef::KITCHEN => 'property-features-kitchen',
                QueryParamsDef::PANTRY => 'property-features-pantry'
            ] as $param => $attributeDefinitionId
        ) {
            $tableName = 'table_' . $param;
            $q->leftJoin('attributes as ' . $tableName, function ($join) use ($attributeDefinitionIds, $attributeDefinitionId, $tableName) {
                $join->on('properties.asset_id', '=', $tableName . '.asset_id')
                    ->where($tableName . '.attribute_definition_id', '=', $attributeDefinitionIds[$attributeDefinitionId]);
            });
        }

        foreach (
            [
                QueryParamsDef::BALCONY => 'property-features-balcony',
                QueryParamsDef::KITCHEN => 'property-features-kitchen',
                QueryParamsDef::PANTRY => 'property-features-pantry'
            ] as $param => $attributeDefinitionId
        ) {
            if (($request->has($param) && $request->get($param) != "")) {
                $tableName = 'table_' . $param;
                $tableVal = $request->get($param);
                $q->where($tableName . ".value", "=", $tableVal);
            }
        }

        $q->leftJoin('property_x_property_views as pxv', 'pxv.property_id', '=', 'properties.id')
            ->leftJoin('property_views as pv', 'pv.id', '=', 'pxv.property_view_id');

        $param = QueryParamsDef::VIEW;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->whereIn('pv.id', explode(',', $request->get($param)));
        }

        $param = QueryParamsDef::PRICE_FROM;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('properties.price', '>=', $request->get($param));
        }

        $param = QueryParamsDef::TOWER;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('towers.id', '=', $request->get($param));
        }

        $param = QueryParamsDef::PRICE_TO;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('properties.price', '<=', $request->get($param));
        }

        $param = QueryParamsDef::CREATED_BY;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('properties.created_by', '=', $request->get($param));
        }

        $param = QueryParamsDef::IS_EXCLUSIVE;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->whereNotNull('properties.is_exclusive')->where('properties.is_exclusive', '1');
        }

        $param = QueryParamsDef::IS_STARED;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->whereNotNull('propst.id');
        }

        $param = QueryParamsDef::IS_VERIFIED;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->whereNotNull('cl.marketing_agreement_path');
        }

        $q->leftJoin('attributes as bua', function ($join) use ($attributeDefinitionIds) {
            $join->on('properties.asset_id', '=', 'bua.asset_id')
                ->where('bua.attribute_definition_id', '=', $attributeDefinitionIds['property-features-build-up-area']);
        });

        $param = QueryParamsDef::SQRFT_FROM;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->where(DB::raw('CAST(bua.value as DECIMAL)'), '>=', doubleval($request->get($param)));
        }

        $param = QueryParamsDef::SQRFT_TO;
        if ($request->has($param) && !empty($request->get($param))) {
            $area = doubleval($request->get($param));
            $q->where(DB::raw('CAST(bua.value as DECIMAL)'), '<=', $area);
        }

        $param = QueryParamsDef::MARKETING_PLATFORMS;
        if ($request->has($param) && !empty($request->get($param))) {
            $usedPlatformIds = explode(",", $request->get($param));
            $q->join('export_assignments as expa', 'expa.property_id', '=', 'properties.id')->whereIn('expa.export_platform_id', $usedPlatformIds);
        }

        $param = 'propertyType';
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('pt.url_value', '=', $request->get($param));
        }

        $param = QueryParamsDef::PROPERTY_TYPE;
        if ($request->has($param) && !empty($request->get($param))) {
            $propertyTypeIdVal = $request->get($param);
            if (is_numeric($propertyTypeIdVal)) {
                if ($propertyTypeIdVal == 1) {
                    // include hotel apart
                    $valuesToLookIn = [1, 17];
                    $q = $q->whereIn('properties.property_type_id', $valuesToLookIn);
                } else {
                    $q = $q->where('properties.property_type_id', '=', $request->get($param));
                }
            } else if (str_contains($propertyTypeIdVal, ",")) {
                $valuesToLookIn = explode(",", $propertyTypeIdVal);
                if (in_array(1, $valuesToLookIn)) {
                    $valuesToLookIn[] = 17;
                }
                $q = $q->whereIn('properties.property_type_id', $valuesToLookIn);
            }
        }

        $param = QueryParamsDef::OPERATION_TYPE;
        if ($request->has($param) && !empty($request->get($param))) {
            $requestOpType = $request->get($param);
            if ($requestOpType == 'rent') {
                $q = $q->where('properties.ad_type', '=', 'rent');
            } else {
                $q = $q->whereIn('properties.ad_type', ['sale', 'rent_and_sale']);
            }
        }

        $param = QueryParamsDef::AVAILABILITY;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == "0" || $val == "1") {
                $q = $q->where('properties.is_sold_leased', '=', $request->get($param));
            }
        }

        $param = QueryParamsDef::OFFERS;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 0) {
                $q->whereNull('properties.offers');
            } elseif ($val == 1) {
                $q->whereNotNull('properties.offers');
            }
        }

        $param = QueryParamsDef::LANDLORD_CATEGORY;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 20 || $val == "1") {
                $q->where('cl.is_corporate_landlord', '=', 1);
            } elseif ($val == 10 || $val == "0") {
                $q->where(function ($qb) {
                    $qb->whereNull('cl.is_corporate_landlord')
                        ->orWhere('cl.is_corporate_landlord', '0');
                });
            }
        }

        foreach (
            [
                QueryParamsDef::COMMISSION_LL => 'commission_ll',
                QueryParamsDef::COMMISSION_BUYER => 'commission_buyer',
                QueryParamsDef::COMMISSION_TENANT => 'commission_tenant'
            ] as $param => $tableField
        ) {
            if ($request->has($param)) {
                $val = $request->get($param);
                if ($val == 0) {
                    $q->whereNull('properties.' . $tableField);
                } elseif ($val == 1) {
                    $q->whereNotNull('properties.' . $tableField);
                }
            }
        }

        $param = QueryParamsDef::BILLS;
        if ($request->has($param) && !empty($request->get($param))) {
            $billsVals = explode(",", $request->get($param));
            $q->where(function ($theQB) use ($billsVals) {
                foreach ($billsVals as $billSlug) {
                    $theQB->orWhere('properties.bills', 'LIKE', '%' . $billSlug . '%');
                }
            });
            // $filterVal = $request->get($param);
            // $q->join("attributes AS bills_attr", "bills_attr.asset_id", "=", "properties.asset_id")
            //     ->where('bills_attr.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bills']);
            // if ($filterVal == 0) {
            //     $q->where('bills_attr.value', '=', 'all-excluded');
            // } elseif ($filterVal == 1) {
            //     $q->where('bills_attr.value', '!=', 'all-excluded');
            // }
        }

        $param = QueryParamsDef::STATUS;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->where('properties.status', 'LIKE', $request->get($param));
        }

        $param = QueryParamsDef::PUBLISHING_STATUS;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->where('properties.publishing_status', 'LIKE', $request->get($param));
        }

        $param = QueryParamsDef::PRORATED_RATA;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 0) {
                $q->where(function ($qb) {
                    $qb->whereNull('properties.prorated_rata')
                        ->orWhere('properties.prorated_rata', '=', 0);
                });
            } elseif ($val == 1) {
                $q->where(function ($qb) {
                    $qb->whereNotNull('properties.prorated_rata')
                        ->where('properties.prorated_rata', '!=', 0);
                });
            }
        }

        $param = QueryParamsDef::TITLE_DEED;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 1) {
                $q->where('properties.title_deed', '=', 1);
            } elseif ($val == 0) {
                $q->where(function ($qb) {
                    $qb->whereNull('properties.title_deed')
                        ->orWhere('properties.title_deed', '=', 0);
                });
            }
        }

        $param = QueryParamsDef::PARKING;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 1) {
                $q->where(function ($sql) {
                    $sql->whereNotNull('table_par.value')
                        ->orWhere('table_par.value', 'not like', 0);
                });
            } else {
                $q->where(function ($sql) {
                    $sql->whereNull('table_par.value')
                        ->orWhere('table_par.value', 'like', 0);
                });
            }
        }

        $param = QueryParamsDef::SERVICE_CHARGE;
        if ($request->has($param)) {
            $q->join("attributes AS service_charge_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'service_charge_attr.asset_id')
                    ->where('service_charge_attr.attribute_definition_id', '=', $attributeDefinitionIds['property-features-service_charge']);
            });
            $q->where('service_charge_attr.value', '=', $request->get($param));
        }

        $param = QueryParamsDef::AGENT;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->where('properties.created_by', $request->get($param));
        }

        $param = QueryParamsDef::CONTACT_ID;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->where('properties.contact_id', $request->get($param));
        }
        $param = QueryParamsDef::REFFERED_BY;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->where('properties.reffered_by', $request->get($param));
        }

        if (!empty($request->get(QueryParamsDef::CREATED_AT_FROM)) && !empty($request->get(QueryParamsDef::CREATED_AT_TO))) {
            $createdAtFrom = Carbon::parse($request->get(QueryParamsDef::CREATED_AT_FROM));
            $createdAtTo = Carbon::parse($request->get(QueryParamsDef::CREATED_AT_TO));
            $q = $q->whereBetween(DB::raw('DATE(properties.created_at)'), [$createdAtFrom->format("Y-m-d"), $createdAtTo->format("Y-m-d")]);
        }

        $clauses = [];

        if (!empty($extraClause['wheres']) && is_array($extraClause['wheres']) && count($extraClause['wheres']) > 0) {
            foreach ($extraClause['wheres'] as $k => $filterVal) {
                if ($k == 'properties.is_featured' || $k == 'properties.is_plot') {
                    if ($this->startsWith($filterVal, "y")) {
                        $filterVal = 1;
                    }

                    if (!$this->startsWith($filterVal, "y")) {
                        $filterVal = 0;
                    }
                    $clauses[] = [$k, '=', $filterVal];
                } else {
                    $clauses[] = [$k, 'LIKE', $filterVal . '%'];
                }
            }
        }

        if (count($clauses) > 0) {
            $q = $q->where($clauses);
        }

        if (!empty($extraClause['q'])) {
            $q = $q->where(function ($query) use ($extraClause) {
                $term = $extraClause['q'] . '%';
                $fullTerm = '%' . $extraClause['q'] . '%';
                return $query->where('properties.ref_no', 'LIKE', $fullTerm)
                    ->orWhere('properties.title', 'LIKE', $term)
                    ->orWhere('location.name', 'LIKE', $term)
                    ->orWhere('properties.address', 'LIKE', $term)
                    ->orWhere('properties.developer', 'LIKE', $term)
                    ->orWhere('properties.keys_place', 'LIKE', $term)
                    ->orWhere('properties.unit_no', 'LIKE', $term)
                    ->orWhere('properties.contact_to_view', 'LIKE', $term)
                    ->orWhere('towers.name', 'LIKE', $term)
                    ->orWhere('contacts.record_no', 'LIKE', $term)
                    ->orWhere('contacts.name', 'LIKE', $term)
                    ->orWhere('contacts.company_name', 'LIKE', $term)
                    ->orWhere('users.name', 'LIKE', $term);
            });
        }

        // check is personal list
        $user = auth()->user();
        $userIsAdmin = $user->hasAnyRole([RolesDef::OFFICE_MANAGER]);
        $userIsMatrixAgent = $user->hasRole(RolesDef::MATRIX_AGENT);
        $userIsMatrixAgentManager = $user->hasRole(RolesDef::MATRIX_AGENT_MANAGER);
        $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
        $userIsPhotographer = $user->hasRole(RolesDef::PHOTOGRAPHER);
        $userCanEditAllListings = $user->hasPermissionTo(PermissionsDef::CAN_EDIT_ALL_LISTINGS);
        if ($userIsMatrixAgent || $userIsMatrixAgentManager) {
            if (!$userIsMatrixAgentManager) {
                if ($userIsMatrixAgent) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.id', $user->id);
                    });
                }
            } else {
                $q->where(function ($qb) use ($user) {
                    $qb->where('users.id', $user->id)->orWhere('users.created_by', $user->id);
                });
            }
        }
        if (request()->has('vt')) {
            $viewType = request()->get('vt');
            if (!$userIsAdmin && !$userIsTeamLeader && !$userCanEditAllListings && $viewType != 'master' && $viewType != 'not_available') {
                $q->where('users.id', $user->id);
            }
            if ($viewType == 'master') {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_MASTERLIST, ListingPublishingStatus::STATUS_PUBLISHED]);
                $q->whereNull('properties.deleted_at');
                $q->whereNotIn('properties.status', ['rented', 'sold']);
                $q->where(function ($qb) {
                    $qb->whereNull('properties.price_on_request')
                        ->orWhere('properties.price_on_request', '!=', 1);
                    // ->orWhere('properties.status', ['to-be-available']);
                });
                $q->where(function ($qb) {
                    $qb->whereNull('properties.is_sold_leased')
                        ->orWhere('properties.is_sold_leased', '!=', 1);
                });
                // $q->orderBy('properties.publishing_status', 'asc');
            } else if ($viewType == 'not_available') {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_MASTERLIST, ListingPublishingStatus::STATUS_PUBLISHED]);
                $q->whereNull('properties.deleted_at');
                $q->where(function ($qb) {
                    $qb->whereIn('properties.status', ['rented', 'sold'])
                        ->orwhere('properties.price_on_request', 1)
                        ->orWhere('properties.is_sold_leased', 1);
                });
                if ($userIsTeamLeader && !$userIsAdmin) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } else if ($viewType == 'pending') {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_PENDING, ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE]);
                if (!$userIsAdmin && !$userIsMatrixAgentManager && !$userIsTeamLeader && !$userCanEditAllListings) {
                    $q->where('properties.created_by', $user->id);
                }
                $q->whereNull('properties.deleted_at');
                if ($userIsTeamLeader && !$userIsAdmin) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } elseif ($viewType == 'archived' && ($userIsAdmin || $userIsMatrixAgentManager || $userIsTeamLeader || $userCanEditAllListings)) {
                $q->whereNotNull('properties.deleted_at');
                if ($userIsTeamLeader) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } else if ($viewType == 'personal') {
                if ($userIsPhotographer) {
                    $q->where('properties.publishing_status', [ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES]);
                }
                $q->where('properties.created_by', $user->id);
                $q->whereNotIn('properties.status', ['rented', 'sold']);
                $q->whereNull('properties.deleted_at');
            } else if ($viewType == 'all_listings') {
                $q->whereIn('properties.publishing_status', [
                    ListingPublishingStatus::STATUS_MASTERLIST,
                    ListingPublishingStatus::STATUS_PUBLISHED,
                    ListingPublishingStatus::STATUS_DRAFT,
                    ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES,
                    ListingPublishingStatus::STATUS_PENDING
                ]);
                if ($userIsTeamLeader && !$userIsAdmin) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } else {
                $q->whereNull('properties.deleted_at');
                if ($userIsTeamLeader) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                } else {
                    $q->where('properties.created_by', $user->id);
                }
            }
        } else {
            if (!$userIsAdmin && !$userIsMatrixAgentManager && !$userIsTeamLeader && !$userCanEditAllListings) {
                $q->where('properties.created_by', $user->id);
            } else {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_PENDING, ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE]);
                $q->whereNotIn('properties.status', ['rented', 'sold', 'to-be-available']);
                if ($userIsTeamLeader) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            }
        }

        $q = $q->select(
            'properties.id',
            'properties.status',
            DB::raw('DATE_FORMAT(properties.available_when, "%d/%m/%Y") as available_when'),
            'properties.asset_id',
            'properties.ref_no',
            'properties.is_exclusive',
            'properties.ad_type',
            'properties.location_id',
            'properties.property_type_id',
            'properties.title',
            DB::raw('COALESCE(users.name, "-") as created_by_name'),
            'properties.created_by',
            'properties.reffered_by',
            'towers.name as tower_name',
            'towers.id as tower_id',
            'properties.address',
            'properties.is_published',
            DB::raw("DATE_FORMAT(properties.created_at, '%d.%m.%Y %H:%i') as created_at"),
            DB::raw("DATE_FORMAT(properties.updated_at, '%d.%m.%Y %H:%i') as updated_at"),
            DB::raw('COALESCE(properties.price, 0) as price'),
            DB::raw('COALESCE(properties.best_price, 0) as best_price'),
            DB::raw('GROUP_CONCAT(DISTINCT(pv.name)) as property_views'),
            'properties.contact_to_view',
            'properties.keys_place',
            'properties.price_on_request',
            'properties.transfer_fee',
            'properties.commission_ll',
            'properties.commission_buyer',
            'properties.payment_method',
            'properties.commission_tenant',
            'properties.prorated_rata',
            'properties.title_deed',
            'properties.brochure_path',
            'properties.brochure_title',
            'properties.bills',
            'properties.offers',
            'properties.is_duplicated_for_deal',
            DB::raw('DATE_FORMAT(propst.created_at, "%d.%m.%Y %H:%i") as propst_stared_at'),
            'propst_users.name as propst_user_name',
            'contacts.name',
            'be.value as bedrooms_no_next',
            'ba.value as bathrooms_no_next',
            'bua.value as build_up_area',
            'table_par.value as parking',
            'table_bal.value as balcony',
            'table_ki.value as kitchen',
            DB::raw('(IF(pt.filter_value = "office", furn_attr_office.value, furn_attr.value)) as furnishing'),
            DB::raw('COALESCE(furn_attr_office.value, "__") as furnishing_office'),
            'pt.filter_value as property_type_filter_value',
            'pt.label as property_type',
            'location.name as location',
            'properties.unit_type',
            'properties.unit_no',
            'properties.publishing_status',
            'users.name as author_name',
            'users.position as author_position',
            'users.team_leader_id as author_team_leader_id',
            'properties.deleted_at as property_deleted_at',
            'cl.payment_closing_requiring_documents as payment_closing_requiring_documents',
            'properties.contact_id',
            'cl.availability_lists',
            'cl.is_corporate_landlord',
            'cl.marketing_agreement_path',
            'contacts.contract_end_date',
            DB::raw('IF(!ISNULL(contacts.contract_end_date), DATE(contacts.contract_end_date) < NOW(), false) as is_contract_expired'),
            DB::raw('COALESCE(uc.name, "Unknown User") as landlord_created_by_name'),
            'properties.remote_propertyfinder_public_id',
            'reminders.id as reminder',
            'it.label as tags',
        );

        $q->orderBy('properties.deleted_at', 'ASC');
        $q->orderBy(DB::raw('COALESCE(properties.price_on_request, 0)', 'ASC'));

        if (isset($extraClause['sortCriterias']) && is_array($extraClause['sortCriterias']) && count($extraClause['sortCriterias']) > 1) {
            foreach ($extraClause['sortCriterias'] as $sortCriteriaArr) {
                $sortCriteriaDir = !empty($sortCriteriaArr['dir']) ? $sortCriteriaArr['dir'] : "asc";
                $sortCriteriaField = $sortCriteriaArr['sort'];
                // dd($sortCriteriaField, $sortCriteriaDir);
                if ($sortCriteriaField === "short_status") {
                    $sortCriteriaField = "properties.publishing_status";
                }
                $q = $q->orderBy($sortCriteriaField, $sortCriteriaDir);
            }
        } elseif (!empty($extraClause['sort'])) {
            $sortField = $extraClause['sort'];
            if ($sortField === "short_status") {
                $sortField = "properties.publishing_status";
            }
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($sortField, $extraClause['dir']);
            } else {
                $q = $q->orderBy($sortField);
            }
        } else {
            $q = $q->orderBy('properties.created_at', 'desc');
        }

        $q->groupBy('properties.id', 'towers.id');
        return $q;
    }

    protected function getTableItemsBaseQNew(RequestParamsMap $requestParamsMap, $extraClauses = [], $groupByClause = null)
    {
        $attributeDefinitionIds = $this->getAttributeDefinitions();
        $q = DB::table('properties')
            ->leftJoin('contacts', 'properties.contact_id', '=', 'contacts.id')
            ->leftJoin('contacts_landlords as cl', 'contacts.id', '=', 'cl.contact_id')
            ->leftJoin('users', 'properties.created_by', '=', 'users.id')
            ->leftJoin('assets', 'properties.asset_id', '=', 'assets.id')
            ->leftJoin('geography as location', 'properties.location_id', '=', 'location.id')
            ->leftJoin('towers', 'properties.tower_id', '=', 'towers.id')
            ->join('property_types as pt', 'properties.property_type_id', '=', 'pt.id')
            ->leftJoin('attributes as be', function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'be.asset_id')
                    ->where('be.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bedrooms']);
            })
            ->leftJoin('attributes as ba', function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'ba.asset_id')
                    ->where('ba.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bathrooms']);
            });

        $furnishingDefinitionId = $attributeDefinitionIds['property-features-furnishings'];
        if ($requestParamsMap->has(QueryParamsDef::FURNISHINGS)) {
            $propertyTypes = collect([]);
            if ($requestParamsMap->has(QueryParamsDef::PROPERTY_TYPE) && !empty($requestParamsMap->get(QueryParamsDef::PROPERTY_TYPE))) {
                $propertyTypeId = $requestParamsMap->get(QueryParamsDef::PROPERTY_TYPE);
                $propertyTypes = $this->propertyTypesService->getCachedTypes()->filter(function ($propertyType) use ($propertyTypeId) {
                    return $propertyType->id == $propertyTypeId && $propertyType->filter_value == 'office';
                });
            }

            if ($propertyTypes->count() > 0) {
                $q->leftJoin("attributes AS furn_attr", function ($join) use ($furnishingDefinitionId) {
                    $join->on('properties.asset_id', '=', 'furn_attr.asset_id')
                        ->where('furn_attr.attribute_definition_id', '=', $furnishingDefinitionId);
                });
                $q->join("attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
                    $join->on('properties.asset_id', '=', 'furn_attr_office.asset_id')
                        ->where('furn_attr_office.attribute_definition_id', '=', $attributeDefinitionIds['property-features-furnishings-office']);
                });

                $param = QueryParamsDef::FURNISHINGS;
                if (!empty($requestParamsMap->get($param))) {
                    $q->where("furn_attr_office.value", "=", $requestParamsMap->get($param));
                }
            } else {
                $q->join("attributes AS furn_attr", function ($join) use ($furnishingDefinitionId) {
                    $join->on('properties.asset_id', '=', 'furn_attr.asset_id')
                        ->where('furn_attr.attribute_definition_id', '=', $furnishingDefinitionId);
                });
                $q->leftJoin("attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
                    $join->on('properties.asset_id', '=', 'furn_attr_office.asset_id')
                        ->where('furn_attr_office.attribute_definition_id', '=', $attributeDefinitionIds['property-features-furnishings-office']);
                });
                $param = QueryParamsDef::FURNISHINGS;
                if (!empty($requestParamsMap->get($param))) {
                    $q->where("furn_attr.value", "=", $requestParamsMap->get($param));
                }
            }
        } else {
            $q->leftJoin("attributes AS furn_attr", function ($join) use ($furnishingDefinitionId) {
                $join->on('properties.asset_id', '=', 'furn_attr.asset_id')
                    ->where('furn_attr.attribute_definition_id', '=', $furnishingDefinitionId);
            });

            $q->leftJoin("attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'furn_attr_office.asset_id')
                    ->where('furn_attr_office.attribute_definition_id', '=', $attributeDefinitionIds['property-features-furnishings-office']);
            });
        }

        $extraClause = $this->getRequestParams(request());

        $param = QueryParamsDef::AMENITIES;
        if (!empty($requestParamsMap->get($param))) {
            $amenitiesIds = explode(",", $requestParamsMap->get($param));
            $q->join("attributes AS amenities", function ($join) use ($amenitiesIds) {
                $join->on('properties.asset_id', '=', 'amenities.asset_id')
                    ->whereIn('amenities.attribute_definition_id', $amenitiesIds);
            });
        }
        $param = QueryParamsDef::LOCATION_DATA;
        if (!empty($requestParamsMap->get($param))) {
            $locationIds = explode(", ", $requestParamsMap->get($param));
            $q->leftJoin('geography as pg', 'pg.id', '=', 'location.parent_id')
                ->leftJoin('geography as ppg', 'ppg.id', '=', 'pg.parent_id')
                ->where(function ($sql) use ($locationIds) {
                    $sql->whereIn('location_id', $locationIds)
                        ->orWhereIn('pg.id', $locationIds)
                        ->orWhereIn('ppg.id', $locationIds);
                });
        }

        if (count($extraClauses) > 0) {
            foreach ($extraClauses as $clause => $clauseValue) {
                $q = $q->where($clause, '=', $clauseValue);
            }
        }

        $q = $q->whereNull('properties.imported_at');

        foreach ([QueryParamsDef::BEDROOMS] as $t) {
            if ($requestParamsMap->has($t)) {
                $q = $q->where($t . '.value', '=', $requestParamsMap->get($t));
            }
        }

        $param = QueryParamsDef::BATHROOMS;
        if ($requestParamsMap->has($param)) {
            $q = $q->where($param . '.value', '>=', $requestParamsMap->get($param));
        }

        foreach (['properties.location_id' => 'locationId'] as $t => $param) {
            if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
                $q = $q->where($t, '=', $requestParamsMap->get($param));
            }
        }

        foreach (
            [
                QueryParamsDef::PARKING => 'property-features-parking-info',
                QueryParamsDef::BALCONY => 'property-features-balcony',
                QueryParamsDef::KITCHEN => 'property-features-kitchen',
                QueryParamsDef::PANTRY => 'property-features-pantry'
            ] as $param => $attributeDefinitionId
        ) {
            $tableName = 'table_' . $param;
            $q->leftJoin('attributes as ' . $tableName, function ($join) use ($attributeDefinitionIds, $attributeDefinitionId, $tableName) {
                $join->on('properties.asset_id', '=', $tableName . '.asset_id')
                    ->where($tableName . '.attribute_definition_id', '=', $attributeDefinitionIds[$attributeDefinitionId]);
            });
        }

        foreach (
            [
                //                     QueryParamsDef::PARKING => 'property-features-parking-info',
                QueryParamsDef::BALCONY => 'property-features-balcony',
                QueryParamsDef::KITCHEN => 'property-features-kitchen',
                QueryParamsDef::PANTRY => 'property-features-pantry'
            ] as $param => $attributeDefinitionId
        ) {
            if (($requestParamsMap->has($param) && $requestParamsMap->get($param) != "")) {
                $tableName = 'table_' . $param;
                $tableVal = $requestParamsMap->get($param);
                $q->where($tableName . ".value", "=", $tableVal);
            }
        }

        $q->leftJoin('property_x_property_views as pxv', 'pxv.property_id', '=', 'properties.id')
            ->leftJoin('property_views as pv', 'pv.id', '=', 'pxv.property_view_id');

        $param = QueryParamsDef::VIEW;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q->whereIn('pv.id', $requestParamsMap->get($param));
        }

        // budget min & max
        //        $param = QueryParamsDef::LISTED_PRICE;
        //        if ($request->has($param) && !empty($request->get($param))) {
        //            $q = $q->where('properties.price', '<=', $request->get($param));
        //        }

        $param = QueryParamsDef::PRICE_FROM;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q = $q->where('properties.price', '>=', $requestParamsMap->get($param));
        }

        $param = QueryParamsDef::PRICE_TO;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q = $q->where('properties.price', '<=', $requestParamsMap->get($param));
        }

        $param = QueryParamsDef::CREATED_BY;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q = $q->where('properties.created_by', '=', $requestParamsMap->get($param));
        }

        $q->leftJoin('attributes as bua', function ($join) use ($attributeDefinitionIds) {
            $join->on('properties.asset_id', '=', 'bua.asset_id')
                ->where('bua.attribute_definition_id', '=', $attributeDefinitionIds['property-features-build-up-area']);
        });

        $param = QueryParamsDef::SQRFT_FROM;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q->where(DB::raw('CAST(bua.value as DECIMAL)'), '>=', doubleval($requestParamsMap->get($param)));
        }

        $param = QueryParamsDef::SQRFT_TO;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $area = doubleval($requestParamsMap->get($param));
            $q->where(DB::raw('CAST(bua.value as DECIMAL)'), '<=', $area);
        }

        $param = 'propertyType';
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q = $q->where('pt.url_value', '=', $requestParamsMap->get($param));
        }

        $param = QueryParamsDef::PROPERTY_TYPE;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $propertyTypeIdVal = $requestParamsMap->get($param);
            if (is_numeric($propertyTypeIdVal)) {
                if ($propertyTypeIdVal == 1) {
                    // include hotel apart
                    $valuesToLookIn = [1, 17];
                    $q = $q->whereIn('properties.property_type_id', $valuesToLookIn);
                } else {
                    $q = $q->where('properties.property_type_id', '=', $requestParamsMap->get($param));
                }
            }
        }

        $param = QueryParamsDef::OPERATION_TYPE;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $requestOpType = $requestParamsMap->get($param);
            if ($requestOpType == 'rent') {
                $q = $q->where('properties.ad_type', '=', 'rent');
            } else {
                $q = $q->whereIn('properties.ad_type', ['sale', 'rent_and_sale']);
            }
        }

        $param = QueryParamsDef::AVAILABILITY;
        if ($requestParamsMap->has($param)) {
            $val = $requestParamsMap->get($param);
            if ($val == "0" || $val == "1") {
                $q = $q->where('properties.is_sold_leased', '=', $requestParamsMap->get($param));
            }
        }

        $param = QueryParamsDef::OFFERS;
        if ($requestParamsMap->has($param)) {
            $val = $requestParamsMap->get($param);
            if ($val == 0) {
                $q->whereNull('properties.offers');
            } elseif ($val == 1) {
                $q->whereNotNull('properties.offers');
            }
        }

        $param = QueryParamsDef::LANDLORD_CATEGORY;
        if ($requestParamsMap->has($param)) {
            $val = $requestParamsMap->get($param);
            if ($val == 20 || $val == "1") {
                $q->where('cl.is_corporate_landlord', '=', 1);
            } elseif ($val == 10 || $val == "0") {
                $q->where(function ($qb) {
                    $qb->whereNull('cl.is_corporate_landlord')
                        ->orWhere('cl.is_corporate_landlord', '0');
                });
            }
        }

        foreach (
            [
                QueryParamsDef::COMMISSION_LL => 'commission_ll',
                QueryParamsDef::COMMISSION_BUYER => 'commission_buyer',
                QueryParamsDef::COMMISSION_TENANT => 'commission_tenant'
            ] as $param => $tableField
        ) {
            if ($requestParamsMap->has($param)) {
                $val = $requestParamsMap->get($param);
                if ($val == 0) {
                    $q->whereNull('properties.' . $tableField);
                } elseif ($val == 1) {
                    $q->whereNotNull('properties.' . $tableField);
                }
            }
        }

        $param = QueryParamsDef::BILLS;
        if ($requestParamsMap->has($param)) {
            $filterVal = $requestParamsMap->get($param);
            $q->join("attributes AS bills_attr", "bills_attr.asset_id", "=", "properties.asset_id")
                ->where('bills_attr.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bills']);
            if ($filterVal == 0) {
                $q->where('bills_attr.value', '=', 'all-excluded');
            } elseif ($filterVal == 1) {
                $q->where('bills_attr.value', '!=', 'all-excluded');
            }
        }

        $param = QueryParamsDef::STATUS;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q->where('properties.status', 'LIKE', $requestParamsMap->get($param));
        }

        $param = QueryParamsDef::PUBLISHING_STATUS;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q->where('properties.publishing_status', 'LIKE', $requestParamsMap->get($param));
        }

        $param = QueryParamsDef::PRORATED_RATA;
        if ($requestParamsMap->has($param)) {
            $val = $requestParamsMap->get($param);
            if ($val == 0) {
                $q->where(function ($qb) {
                    $qb->whereNull('properties.prorated_rata')
                        ->orWhere('properties.prorated_rata', '=', 0);
                });
            } elseif ($val == 1) {
                $q->where(function ($qb) {
                    $qb->whereNotNull('properties.prorated_rata')
                        ->where('properties.prorated_rata', '!=', 0);
                });
            }
        }

        $param = QueryParamsDef::TITLE_DEED;
        if ($requestParamsMap->has($param)) {
            $val = $requestParamsMap->get($param);
            if ($val == 1) {
                $q->where('properties.title_deed', '=', 1);
            } elseif ($val == 0) {
                $q->where(function ($qb) {
                    $qb->whereNull('properties.title_deed')
                        ->orWhere('properties.title_deed', '=', 0);
                });
            }
        }

        $param = QueryParamsDef::PARKING;
        if ($requestParamsMap->has($param)) {
            $val = $requestParamsMap->get($param);
            if ($val == 1) {
                $q->where(function ($sql) {
                    $sql->whereNotNull('table_par.value')
                        ->orWhere('table_par.value', 'not like', 0);
                });
            } else {
                $q->where(function ($sql) {
                    $sql->whereNull('table_par.value')
                        ->orWhere('table_par.value', 'like', 0);
                });
            }
        }

        $param = QueryParamsDef::SERVICE_CHARGE;
        if ($requestParamsMap->has($param)) {
            $q->join("attributes AS service_charge_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'service_charge_attr.asset_id')
                    ->where('service_charge_attr.attribute_definition_id', '=', $attributeDefinitionIds['property-features-service_charge']);
            });
            $q->where('service_charge_attr.value', '=', $requestParamsMap->get($param));
        }

        $param = QueryParamsDef::AGENT;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q->where('properties.created_by', $requestParamsMap->get($param));
        }

        $param = QueryParamsDef::CONTACT_ID;
        if ($requestParamsMap->has($param) && !empty($requestParamsMap->get($param))) {
            $q->where('properties.contact_id', $requestParamsMap->get($param));
        }

        $clauses = [];

        if (!empty($extraClause['wheres']) && is_array($extraClause['wheres']) && count($extraClause['wheres']) > 0) {
            foreach ($extraClause['wheres'] as $k => $filterVal) {
                if ($k == 'properties.is_featured' || $k == 'properties.is_plot') {
                    if ($this->startsWith($filterVal, "y")) {
                        $filterVal = 1;
                    }

                    if (!$this->startsWith($filterVal, "y")) {
                        $filterVal = 0;
                    }
                    $clauses[] = [$k, '=', $filterVal];
                } else {
                    $clauses[] = [$k, 'LIKE', $filterVal . '%'];
                }
            }
        }

        if (count($clauses) > 0) {
            $q = $q->where($clauses);
        }

        if (!empty($extraClause['q'])) {
            $q = $q->where(function ($query) use ($extraClause) {
                $term = $extraClause['q'] . '%';
                $fullTerm = '%' . $extraClause['q'] . '%';
                return $query->where('properties.ref_no', 'LIKE', $fullTerm)
                    ->orWhere('properties.title', 'LIKE', $term)
                    ->orWhere('location.name', 'LIKE', $term)
                    ->orWhere('properties.address', 'LIKE', $term)
                    ->orWhere('properties.developer', 'LIKE', $term)
                    ->orWhere('properties.keys_place', 'LIKE', $term)
                    ->orWhere('properties.unit_no', 'LIKE', $term)
                    ->orWhere('properties.contact_to_view', 'LIKE', $term)
                    ->orWhere('towers.name', 'LIKE', $term)
                    ->orWhere('contacts.record_no', 'LIKE', $term)
                    ->orWhere('contacts.name', 'LIKE', $term)
                    ->orWhere('contacts.company_name', 'LIKE', $term);
            });
        }

        // check is personal list
        $user = auth()->user();
        if ($user) {
            $userIsAdmin = $user->hasAnyRole([RolesDef::OFFICE_MANAGER]);
            $userIsMatrixAgent = $user->hasRole(RolesDef::MATRIX_AGENT);
            $userIsMatrixAgentManager = $user->hasRole(RolesDef::MATRIX_AGENT_MANAGER);
            $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);

            if ($userIsMatrixAgent || $userIsMatrixAgentManager) {
                $managerId = $userIsMatrixAgent ? $user->created_by : $user->id;
                $q->where(function ($qb) use ($managerId) {
                    $qb->where('users.created_by', $managerId)
                        ->orWhere('users.id', $managerId);
                });
            }
            if ($userIsTeamLeader) {

                $leaderId = $userIsTeamLeader ? $user->team_leader_id : $user->id;
                $q->where(function ($qb) use ($leaderId) {
                    $qb->where('users.team_leader_id', $leaderId)
                        ->orWhere('users.id', $leaderId);
                });
            }
        }

        // if(request()->has('vt')) {
        //     $viewType = request()->get('vt');
        //     if($viewType == 'master') {
        //         $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_MASTERLIST, ListingPublishingStatus::STATUS_PUBLISHED]);
        //         $q->whereNull('properties.deleted_at');
        //     } else if($viewType == 'pending') {
        //         $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_PENDING, ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE]);
        //         if(!$userIsAdmin && !$userIsMatrixAgentManager) {
        //             $q->where('properties.created_by', $user->id);
        //         }
        //         $q->whereNull('properties.deleted_at');
        //     } elseif($viewType == 'archived' && ($userIsAdmin || $userIsMatrixAgentManager)) {
        //         $q->whereNotNull('properties.deleted_at');
        //     } else {
        //         $q->where('properties.created_by', $user->id);
        //         $q->whereNull('properties.deleted_at');
        //     }
        // } else {
        //     if(!$userIsAdmin && !$userIsMatrixAgentManager) {
        //         $q->where('properties.created_by', $user->id);
        //     } else {
        //         $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_PENDING, ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE]);
        //     }
        // }

        $q = $q->select(
            'properties.id as listing_id',
            'properties.status',
            DB::raw('DATE_FORMAT(properties.available_when, "%d/%m/%Y") as available_when'),
            'properties.asset_id',
            'properties.ref_no',
            'properties.is_exclusive',
            'properties.ad_type',
            'properties.location_id',
            'properties.property_type_id',
            'properties.title',
            DB::raw('COALESCE(users.name, "-") as created_by_name'),
            'properties.created_by',
            'towers.name as tower_name',
            'towers.id as tower_id',
            'properties.address',
            'properties.is_published',
            DB::raw("DATE_FORMAT(properties.created_at, '%d.%m.%Y %H:%i') as created_at"),
            DB::raw("DATE_FORMAT(properties.updated_at, '%d.%m.%Y %H:%i') as updated_at"),
            DB::raw('COALESCE(properties.price, 0) as price'),
            DB::raw('COALESCE(properties.best_price, 0) as best_price'),
            DB::raw('GROUP_CONCAT(pv.name) as property_views'),
            'properties.contact_to_view',
            'properties.keys_place',
            'properties.price_on_request',
            'properties.images',
            'properties.transfer_fee',
            'properties.commission_ll',
            'properties.commission_buyer',
            'properties.payment_method',
            'properties.commission_tenant',
            'properties.prorated_rata',
            'properties.title_deed',
            'properties.brochure_path',
            'properties.brochure_title',
            'properties.bills',
            'properties.offers',
            'contacts.name',
            'be.value as bedrooms',
            'ba.value as bathrooms',
            'bua.value as build_up_area',
            'table_par.value as parking',
            'table_bal.value as balcony',
            'table_ki.value as kitchen',
            DB::raw('(IF(pt.filter_value = "office", furn_attr_office.value, furn_attr.value)) as furnishing'),
            DB::raw('COALESCE(furn_attr_office.value, "__") as furnishing_office'),
            'pt.filter_value as property_type_filter_value',
            'pt.label as property_type',
            'location.name as location',
            'properties.unit_type',
            'properties.unit_no',
            'properties.publishing_status',
            'users.name as author_name',
            'users.position as author_position',
            'users.team_leader_id as author_team_leader_id',
            'properties.deleted_at as property_deleted_at',
            'cl.payment_closing_requiring_documents as payment_closing_requiring_documents',
            'properties.contact_id'
        );

        $q->orderBy('properties.deleted_at', 'ASC');

        if (!empty($extraClause['sort'])) {
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($extraClause['sort'], $extraClause['dir']);
            } else {
                $q = $q->orderBy($extraClause['sort']);
            }
        } else {
            $q = $q->orderBy('properties.created_at', 'desc');
        }

        $q->groupBy('properties.id', 'towers.id');
        return $q;
    }

    protected function getAPITableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $attributeDefinitionIds = $this->getAttributeDefinitions();
        $amenitiesDefinitions = $this->getAmenitiesDefinitions();

        $q = DB::table('properties')
            ->leftJoin('contacts', 'properties.contact_id', '=', 'contacts.id')
            ->leftJoin('contacts_landlords as cl', 'contacts.id', '=', 'cl.contact_id')
            ->leftJoin('users', 'properties.created_by', '=', 'users.id')
            //            ->leftJoin('assets', 'properties.asset_id', '=', 'assets.id')
            ->leftJoin('geography as location', 'properties.location_id', '=', 'location.id')
            ->leftJoin('towers', 'properties.tower_id', '=', 'towers.id')
            ->join('property_types as pt', 'properties.property_type_id', '=', 'pt.id')
            ->leftJoin('attributes as be', function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'be.asset_id')
                    ->where('be.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bedrooms']);
            })
            ->leftJoin('attributes as ba', function ($join) use ($attributeDefinitionIds) {
                $join->on('properties.asset_id', '=', 'ba.asset_id')
                    ->where('ba.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bathrooms']);
            });
        // ->leftJoin('attributes as constr_attr', function ($join) use ($attributeDefinitionIds) {
        //     $join->on('properties.asset_id', '=', 'constr_attr.asset_id')
        //         ->where('constr_attr.attribute_definition_id', '=', $attributeDefinitionIds['property-features-construction-year']);
        // })
        // ->leftJoin('attributes as amenities', function ($join) use ($attributeDefinitionIds, $amenitiesDefinitions) {
        //     $join->on('properties.asset_id', '=', 'amenities.asset_id')
        //         ->whereIn('amenities.attribute_definition_id', array_keys($amenitiesDefinitions));
        // });

        // $furnishingDefinitionId = $attributeDefinitionIds['property-features-furnishings'];
        // if ($request->has(QueryParamsDef::FURNISHINGS)) {
        //     $propertyTypes = collect([]);
        //     if ($request->has(QueryParamsDef::PROPERTY_TYPE) && !empty($request->get(QueryParamsDef::PROPERTY_TYPE))) {
        //         $propertyTypeId = $request->get(QueryParamsDef::PROPERTY_TYPE);
        //         $propertyTypes = $this->propertyTypesService->getCachedTypes()->filter(function ($propertyType) use ($propertyTypeId) {
        //             return $propertyType->id == $propertyTypeId && $propertyType->filter_value == 'office';
        //         });
        //     }

        //     if ($propertyTypes->count() > 0) {
        //         $q->leftJoin("attributes AS furn_attr", function ($join) use ($furnishingDefinitionId) {
        //             $join->on('properties.asset_id', '=', 'furn_attr.asset_id')
        //                 ->where('furn_attr.attribute_definition_id', '=', $furnishingDefinitionId);
        //         });
        //         $q->join("attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
        //             $join->on('properties.asset_id', '=', 'furn_attr_office.asset_id')
        //                 ->where('furn_attr_office.attribute_definition_id', '=', $attributeDefinitionIds['property-features-furnishings-office']);
        //         });

        //         $param = QueryParamsDef::FURNISHINGS;
        //         if (!empty($request->get($param))) {
        //             $q->where("furn_attr_office.value", "=", $request->get($param));
        //         }
        //     } else {
        //         $q->join("attributes AS furn_attr", function ($join) use ($furnishingDefinitionId) {
        //             $join->on('properties.asset_id', '=', 'furn_attr.asset_id')
        //                 ->where('furn_attr.attribute_definition_id', '=', $furnishingDefinitionId);
        //         });
        //         $q->leftJoin("attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
        //             $join->on('properties.asset_id', '=', 'furn_attr_office.asset_id')
        //                 ->where('furn_attr_office.attribute_definition_id', '=', $attributeDefinitionIds['property-features-furnishings-office']);
        //         });
        //         $param = QueryParamsDef::FURNISHINGS;
        //         if (!empty($request->get($param))) {
        //             $q->where("furn_attr.value", "=", $request->get($param));
        //         }
        //     }
        // } else {
        //     $q->leftJoin("attributes AS furn_attr", function ($join) use ($furnishingDefinitionId) {
        //         $join->on('properties.asset_id', '=', 'furn_attr.asset_id')
        //             ->where('furn_attr.attribute_definition_id', '=', $furnishingDefinitionId);
        //     });

        //     $q->leftJoin("attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
        //         $join->on('properties.asset_id', '=', 'furn_attr_office.asset_id')
        //             ->where('furn_attr_office.attribute_definition_id', '=', $attributeDefinitionIds['property-features-furnishings-office']);
        //     });
        // }

        $extraClause = $this->getRequestParams($request);

        $param = QueryParamsDef::CONTACT_ID;
        if (!empty($request->get($param))) {
            $q = $q->where('properties.contact_id', $request->get($param));
        }

        $param = QueryParamsDef::LOCATION_DATA;
        if (!empty($request->get($param))) {
            $q = $q->whereIn('location_id', explode(",", $request->get($param)));
        }

        if (count($extraClauses) > 0) {
            foreach ($extraClauses as $clause => $clauseValue) {
                $q = $q->where($clause, '=', $clauseValue);
            }
        }

        $param = 'showImported';
        if (!$request->has($param) || ($request->has($param) && $request->get($param) == 0)) {
            $q = $q->whereNull('properties.imported_at');
        }

        foreach ([QueryParamsDef::BEDROOMS] as $t) {
            if ($request->has($t)) {
                $q = $q->where($t . '.value', '=', $request->get($t));
            }
        }

        $param = QueryParamsDef::BATHROOMS;
        if ($request->has($param)) {
            $q = $q->where($param . '.value', '>=', $request->get($param));
        }

        foreach (['properties.location_id' => 'locationId'] as $t => $param) {
            if ($request->has($param) && !empty($request->get($param))) {
                $q = $q->where($t, '=', $request->get($param));
            }
        }

        // foreach ([
        //     QueryParamsDef::PARKING => 'property-features-parking-info',
        //     QueryParamsDef::BALCONY => 'property-features-balcony',
        //     QueryParamsDef::KITCHEN => 'property-features-kitchen',
        //     QueryParamsDef::PANTRY => 'property-features-pantry'
        // ] as $param => $attributeDefinitionId) {
        //     $tableName = 'table_' . $param;
        //     $q->leftJoin('attributes as ' . $tableName, function ($join) use ($attributeDefinitionIds, $attributeDefinitionId, $tableName) {
        //         $join->on('properties.asset_id', '=', $tableName . '.asset_id')
        //             ->where($tableName . '.attribute_definition_id', '=', $attributeDefinitionIds[$attributeDefinitionId]);
        //     });
        // }

        // foreach ([
        //     QueryParamsDef::PARKING => 'property-features-parking-info',
        //     QueryParamsDef::BALCONY => 'property-features-balcony',
        //     QueryParamsDef::KITCHEN => 'property-features-kitchen',
        //     QueryParamsDef::PANTRY => 'property-features-pantry'
        // ] as $param => $attributeDefinitionId) {
        //     if (($request->has($param) && $request->get($param) != "")) {
        //         $tableName = 'table_' . $param;
        //         $tableVal = $request->get($param);
        //         $q->where($tableName . ".value", "=", $tableVal);
        //     }
        // }

        $q->leftJoin('property_x_property_views as pxv', 'pxv.property_id', '=', 'properties.id')
            ->leftJoin('property_views as pv', 'pv.id', '=', 'pxv.property_view_id');

        $param = QueryParamsDef::VIEW;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->whereIn('pv.id', explode(',', $request->get($param)));
        }

        // budget min & max
        //        $param = QueryParamsDef::LISTED_PRICE;
        //        if ($request->has($param) && !empty($request->get($param))) {
        //            $q = $q->where('properties.price', '<=', $request->get($param));
        //        }

        $param = QueryParamsDef::PRICE_FROM;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('properties.price', '>=', $request->get($param));
        }

        $param = QueryParamsDef::PRICE_TO;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('properties.price', '<=', $request->get($param));
        }

        $param = QueryParamsDef::CREATED_BY;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('properties.created_by', '=', $request->get($param));
        }

        // $q->leftJoin('attributes as bua', function ($join) use ($attributeDefinitionIds) {
        //     $join->on('properties.asset_id', '=', 'bua.asset_id')
        //         ->where('bua.attribute_definition_id', '=', $attributeDefinitionIds['property-features-build-up-area']);
        // });

        // $param = QueryParamsDef::SQRFT_FROM;
        // if ($request->has($param) && !empty($request->get($param))) {
        //     $q->where('bua.value', '>=', doubleval($request->get($param)));
        // }

        // $param = QueryParamsDef::SQRFT_TO;
        // if ($request->has($param) && !empty($request->get($param))) {
        //     $q->where('bua.value', '<=', doubleval($request->get($param)));
        // }

        $param = 'propertyType';
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('pt.url_value', '=', $request->get($param));
        }

        $param = QueryParamsDef::PROPERTY_TYPE;
        if ($request->has($param) && !empty($request->get($param))) {
            if (is_numeric($request->get($param))) {
                $q = $q->where('properties.property_type_id', '=', $request->get($param));
            }
        }

        $param = QueryParamsDef::OPERATION_TYPE;
        if ($request->has($param) && !empty($request->get($param))) {
            $requestOpType = $request->get($param);
            if ($requestOpType == 'rent') {
                $q = $q->where('properties.ad_type', '=', 'rent');
            } else {
                $q = $q->whereIn('properties.ad_type', ['sale', 'rent_and_sale']);
            }
        }

        $param = QueryParamsDef::AVAILABILITY;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == "0" || $val == "1") {
                $q = $q->where('properties.is_sold_leased', '=', $request->get($param));
            }
        }

        $param = QueryParamsDef::OFFERS;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 0) {
                $q->whereNull('properties.offers');
            } elseif ($val == 1) {
                $q->whereNotNull('properties.offers');
            }
        }

        $param = QueryParamsDef::LANDLORD_CATEGORY;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 20) {
                $q->where('cl.is_corporate_landlord', '=', 1);
            } elseif ($val == 10) {
                $q->whereNull('cl.is_corporate_landlord');
            }
        }

        foreach (
            [
                QueryParamsDef::COMMISSION_LL => 'commission_ll',
                QueryParamsDef::COMMISSION_BUYER => 'commission_buyer',
                QueryParamsDef::COMMISSION_TENANT => 'commission_tenant'
            ] as $param => $tableField
        ) {
            if ($request->has($param)) {
                $val = $request->get($param);
                if ($val == 0) {
                    $q->whereNull('properties.' . $tableField);
                } elseif ($val == 1) {
                    $q->whereNotNull('properties.' . $tableField);
                }
            }
        }

        // $param = QueryParamsDef::BILLS;
        // if ($request->has($param)) {
        //     $filterVal = $request->get($param);
        //     $q->join("attributes AS bills_attr", "bills_attr.asset_id", "=", "properties.asset_id")
        //         ->where('bills_attr.attribute_definition_id', '=', $attributeDefinitionIds['property-features-bills']);
        //     if ($filterVal == 0) {
        //         $q->where('bills_attr.value', '=', 'all-excluded');
        //     } elseif ($filterVal == 1) {
        //         $q->where('bills_attr.value', '!=', 'all-excluded');
        //     }
        // }

        $param = QueryParamsDef::STATUS;
        if ($request->has($param) && !empty($request->get($param))) {
            $q->where('properties.status', 'LIKE', $request->get($param));
        }

        $param = QueryParamsDef::TITLE_DEED;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == 1) {
                $q->where('properties.title_deed', '=', 1);
            } else {
                $q->whereNull('properties.title_deed');
            }
        }

        // $q->leftJoin("attributes AS service_charge_attr", function ($join) use ($attributeDefinitionIds) {
        //     $join->on('properties.asset_id', '=', 'service_charge_attr.asset_id')
        //         ->where('service_charge_attr.attribute_definition_id', '=', $attributeDefinitionIds['property-features-service_charge']);
        // });

        // $param = QueryParamsDef::SERVICE_CHARGE;
        // if ($request->has($param)) {
        //     $q->where('service_charge_attr.value', '=', $request->get($param));
        // }

        $clauses = [];

        if (!empty($extraClause['wheres']) && is_array($extraClause['wheres']) && count($extraClause['wheres']) > 0) {
            foreach ($extraClause['wheres'] as $k => $filterVal) {
                if ($k == 'properties.is_featured' || $k == 'properties.is_plot') {
                    if ($this->startsWith($filterVal, "y")) {
                        $filterVal = 1;
                    }

                    if (!$this->startsWith($filterVal, "y")) {
                        $filterVal = 0;
                    }
                    $clauses[] = [$k, '=', $filterVal];
                } else {
                    $clauses[] = [$k, 'LIKE', $filterVal . '%'];
                }
            }
        }
        //        $q = $q->whereNull('properties.deleted_at');

        if (count($clauses) > 0) {
            $q = $q->where($clauses);
        }

        if (!empty($extraClause['q'])) {
            $q = $q->where(function ($query) use ($extraClause) {
                $term = $extraClause['q'] . '%';
                return $query->where('properties.ref_no', 'LIKE', $term)
                    ->orWhere('properties.title', 'LIKE', $term)
                    ->orWhere('location.name', 'LIKE', $term)
                    ->orWhere('properties.address', 'LIKE', $term)
                    ->orWhere('properties.developer', 'LIKE', $term)
                    ->orWhere('properties.keys_place', 'LIKE', $term)
                    ->orWhere('properties.unit_no', 'LIKE', $term)
                    ->orWhere('properties.contact_to_view', 'LIKE', $term)
                    ->orWhere('towers.name', 'LIKE', $term);
            });
        }

        $user = auth()->user();
        $userIsAdmin = $user->hasAnyRole([RolesDef::OFFICE_MANAGER]);
        $userIsMatrixAgent = $user->hasRole(RolesDef::MATRIX_AGENT);
        $userIsMatrixAgentManager = $user->hasRole(RolesDef::MATRIX_AGENT_MANAGER);
        $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
        $userIsPhotographer = $user->hasRole(RolesDef::PHOTOGRAPHER);
        $userCanEditAllListings = $user->hasPermissionTo(PermissionsDef::CAN_EDIT_ALL_LISTINGS);
        if ($userIsMatrixAgent || $userIsMatrixAgentManager) {
            if (!$userIsMatrixAgentManager) {
                if ($userIsMatrixAgent) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.id', $user->id);
                    });
                }
            } else {
                $q->where(function ($qb) use ($user) {
                    $qb->where('users.id', $user->id)->orWhere('users.created_by', $user->id);
                });
            }
        }
        if (request()->has('vt')) {
            $viewType = request()->get('vt');
            if (!$userIsAdmin && !$userIsTeamLeader && !$userCanEditAllListings && $viewType != 'master' && $viewType != 'landlord-details' && $viewType != 'not_available') {
                $q->where('users.id', $user->id);
            }
            if ($viewType == 'landlord-details') {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_MASTERLIST, ListingPublishingStatus::STATUS_PUBLISHED, ListingPublishingStatus::STATUS_PENDING, ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE, ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES]);
                $q->whereNull('properties.deleted_at');
            } elseif ($viewType == 'master') {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_MASTERLIST, ListingPublishingStatus::STATUS_PUBLISHED]);
                $q->whereNull('properties.deleted_at');
                $q->whereNotIn('properties.status', ['rented', 'sold']);
                $q->where(function ($qb) {
                    $qb->whereNull('properties.price_on_request')
                        ->orWhere('properties.price_on_request', '!=', 1);
                    // ->orWhere('properties.status', ['to-be-available']);
                });
                $q->where(function ($qb) {
                    $qb->whereNull('properties.is_sold_leased')
                        ->orWhere('properties.is_sold_leased', '!=', 1);
                });
                // $q->orderBy('properties.publishing_status', 'asc');
            } else if ($viewType == 'not_available') {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_MASTERLIST, ListingPublishingStatus::STATUS_PUBLISHED]);
                $q->whereNull('properties.deleted_at');
                $q->where(function ($qb) {
                    $qb->whereIn('properties.status', ['rented', 'sold'])
                        ->orwhere('properties.price_on_request', 1)
                        ->orWhere('properties.is_sold_leased', 1);
                });
                if ($userIsTeamLeader && !$userIsAdmin) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } else if ($viewType == 'pending') {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_PENDING, ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE]);
                if (!$userIsAdmin && !$userIsMatrixAgentManager && !$userIsTeamLeader && !$userCanEditAllListings) {
                    $q->where('properties.created_by', $user->id);
                }
                $q->whereNull('properties.deleted_at');
                if ($userIsTeamLeader && !$userIsAdmin) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } elseif ($viewType == 'archived' && ($userIsAdmin || $userIsMatrixAgentManager || $userIsTeamLeader || $userCanEditAllListings)) {
                $q->whereNotNull('properties.deleted_at');
                if ($userIsTeamLeader) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } else if ($viewType == 'personal') {
                if ($userIsPhotographer) {
                    $q->where('properties.publishing_status', [ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES]);
                }
                $q->where('properties.created_by', $user->id);
                $q->whereNotIn('properties.status', ['rented', 'sold']);
                $q->whereNull('properties.deleted_at');
            } else if ($viewType == 'all_listings') {
                $q->whereIn('properties.publishing_status', [
                    ListingPublishingStatus::STATUS_MASTERLIST,
                    ListingPublishingStatus::STATUS_PUBLISHED,
                    ListingPublishingStatus::STATUS_DRAFT,
                    ListingPublishingStatus::STATUS_WAITING_FOR_PICTURES,
                    ListingPublishingStatus::STATUS_PENDING
                ]);
                if ($userIsTeamLeader && !$userIsAdmin) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            } else {
                $q->whereNull('properties.deleted_at');
                if ($userIsTeamLeader) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                } else {
                    $q->where('properties.created_by', $user->id);
                }
            }
        } else {
            if (!$userIsAdmin && !$userIsMatrixAgentManager && !$userIsTeamLeader && !$userCanEditAllListings) {
                $q->where('properties.created_by', $user->id);
            } else {
                $q->whereIn('properties.publishing_status', [ListingPublishingStatus::STATUS_PENDING, ListingPublishingStatus::STATUS_REQUEST_FOR_CHANGE]);
                $q->whereNotIn('properties.status', ['rented', 'sold', 'to-be-available']);
                if ($userIsTeamLeader) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('users.team_leader_id', $user->id)
                            ->orWhere('users.id', $user->id);
                    });
                }
            }
        }

        $q->whereNull('properties.imported_at');

        $q = $q->select(
            'properties.id',
            'properties.status',
            DB::raw('DATE_FORMAT(properties.available_when, "%d/%m/%Y") as available_when'),
            'properties.asset_id',
            'properties.ref_no',
            'properties.contact_id',
            'properties.is_exclusive',
            'properties.ad_type',
            'properties.title',
            DB::raw('COALESCE(users.name, "-") as created_by'),
            'towers.name as tower_name',
            'towers.id as tower_id',
            'properties.address',
            'properties.is_published',
            DB::raw("DATE_FORMAT(properties.created_at, '%d.%m.%Y %H:%i') as created_at"),
            'properties.updated_at',
            DB::raw('COALESCE(properties.price, 0) as price'),
            DB::raw('COALESCE(properties.best_price, 0) as best_price'),
            DB::raw('GROUP_CONCAT(DISTINCT CONCAT(pv.id, "_", pv.name)) as property_views'),
            'properties.contact_to_view',
            'properties.keys_place',
            'properties.price_on_request',
            'properties.transfer_fee',
            'properties.commission_ll',
            'properties.commission_buyer',
            'properties.payment_method',
            'properties.commission_tenant',
            'properties.prorated_rata',
            'properties.title_deed',
            'properties.brochure_path',
            'properties.brochure_title',
            'properties.bills',
            'properties.offers',
            'properties.payment_booking_fee',
            'properties.payment_cheque_addressed_to',
            'cl.payment_closing_requiring_documents',
            'properties.asset_id',
            'contacts.name',
            'be.value as bedrooms_no_next',
            'ba.value as bathrooms_no_next',
            // 'bua.value as build_up_area',
            // 'table_par.value as parking',
            // 'table_bal.value as balcony',
            // 'table_ki.value as kitchen',
            // 'constr_attr.value as construction_year',
            // DB::raw('(IF(pt.filter_value = "office", furn_attr_office.value, furn_attr.value)) as furnishing'),
            // DB::raw('COALESCE(furn_attr_office.value, "__") as furnishing_office'),
            // DB::raw('GROUP_CONCAT(DISTINCT amenities.attribute_definition_id) as amenities_ids'),
            'pt.filter_value as property_type_filter_value',
            'pt.label as property_type',
            'location.name as location',
            'properties.unit_type',
            'properties.unit_no',
            'properties.publishing_status',
            'properties.created_by',
            'properties.images',
            'properties.primary_image',
            'users.name as author_name',
            'users.position as author_position',
            'properties.deleted_at as property_deleted_at',
            'cl.payment_closing_requiring_documents as payment_closing_requiring_documents',
            // 'service_charge_attr.value as service_charge'
        );


        $q->orderBy('properties.deleted_at', 'ASC');

        if (!empty($extraClause['sort'])) {
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($extraClause['sort'], $extraClause['dir']);
            } else {
                $q = $q->orderBy($extraClause['sort']);
            }
        } else {
            $q = $q->orderBy('properties.created_at', 'desc');
        }

        $q->groupBy('properties.id', 'towers.id');

        return $q;
    }

    private function getAttributeDefinitions()
    {
        $definitionNames = array_keys($this->attributeDefinitionIds);
        Cache::forget('attributeDefinitionIds');
        return Cache::remember('attributeDefinitionIds', Timings::DAY, function () use ($definitionNames) {
            return AttributeDefinition::whereIn('name', $definitionNames)->get()->mapWithKeys(function ($item) {
                return [$item->name => $item->id];
            })->toArray();
        });
    }

    public function getAmenitiesDefinitions()
    {
        return Cache::remember(CacheKeys::AMENITIES_DEFINITIONS_IDS, Timings::MONTH, function () {
            return DB::table('attribute_groups as ag')
                ->join('attribute_definitions as ad', 'ad.attribute_group_id', '=', 'ag.id')
                ->where('ag.name', 'Amenities')
                ->select('ad.id', 'ad.label')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->id => $item->label];
                })
                ->toArray();
        });
    }

    public function snapshot($dbItem)
    {
        $snapshot = PropertySnapshot::withTrashed()->where('asset_id', '=', $dbItem->asset_id)->first();

        $currentData = $dbItem->toArray();
        $currentData['listing_id'] = $currentData['id'];
        unset($currentData['id']);

        if (is_null($snapshot)) {
            $snapshot = new PropertySnapshot();
        } elseif ($snapshot->trashed()) {
            $snapshot->restore();
        }

        // transfer fee, service charge, title deed
        $attributes = Attribute::with(['definition'])->whereHas('definition', function ($qb) {
            $qb->whereIn('name', ['property-features-transfer_fee', 'property-features-service_charge']);
        })->where('asset_id', $dbItem->asset_id)->get();

        $attributes->each(function ($attr) use (&$currentData) {
            if ($attr->definition->name == 'property-features-transfer_fee') {
                $currentData['transfer_fee'] = $attr->value;
            }
            if ($attr->definition->name == 'property-features-service_charge') {
                $currentData['service_charge'] = $attr->value;
            }
        });

        $snapshot->fill($currentData);
        $snapshot->geo_point = $dbItem->geo_point;
        $snapshot->save();
        $this->propertiesService->generateSnapshotsStats();

        // property views
        $viewIds = $dbItem->propertyViews->map(function ($item) {
            return $item->id;
        })->toArray();
        $snapshot->views()->sync($viewIds);

        // attributes
        $attributes = Attribute::where('asset_id', $dbItem->asset_id)->get();
        PropertySnapshotAttribute::where('asset_id', $dbItem->asset_id)->forceDelete();
        foreach ($attributes as $attribute) {
            $attributeData = $attribute->toArray();
            unset($attributeData['id']);
            PropertySnapshotAttribute::create($attributeData);
        }

        // images -
        // delete all attachment assignments for the snapshot
        $snapshotImagesDir = public_path('/images_cache/snapshots/' . $dbItem->asset_id);
        File::deleteDirectory($snapshotImagesDir);
        AttachmentAssignment::where(['object_type' => 'snapshot', 'object_id' => $dbItem->asset_id])->forceDelete();

        // export platforms
        $assignmentPlatforms = ExportAssignment::with('exportPlatform')->where('property_id', $snapshot->listing_id)->get()
            ->map(function ($ea) {
                return $ea->exportPlatform;
            });

        SnapshotExportAssignment::where('snapshot_id', $snapshot->id)->delete();
        $snapshot->exportPlatforms()->saveMany($assignmentPlatforms);

        // translations
        $listingTranslations = PropertyTranslation::where('property_id', $snapshot->listing_id)->get();
        PropertySnapshotTranslation::where('snapshot_id', $snapshot->id)->delete();
        foreach ($listingTranslations as $listingTranslation) {
            PropertySnapshotTranslation::create([
                'snapshot_id' => $snapshot->id,
                'title' => $listingTranslation->title,
                'propertyfinder_title' => $listingTranslation->propertyfinder_title,
                'description' => $listingTranslation->description,
                'language' => $listingTranslation->language
            ]);
        }

        $shouldSaveSnapshot = false;
        // sync with PropertyFinder
        $hasPropertyFinderPlatform = false;
        foreach ($assignmentPlatforms->toArray() as $platform) {
            if ($platform["export_url_tag"] == "propertyfinder") {
                $hasPropertyFinderPlatform = true;
            }
        }
        Log::info("Has PF platform / Price on request " . $hasPropertyFinderPlatform . " / " . $dbItem->price_on_request);
        if ($hasPropertyFinderPlatform && $dbItem->price_on_request != 1) {
            PFSyncQueue::create(['snapshot_id' => $snapshot->id]);
        }

        if ($shouldSaveSnapshot) {
            $snapshot->save();
        }

        return $snapshot;
    }

    public function takeDownSnapshot($dbItem)
    {
        $snapshot = PropertySnapshot::where('asset_id', '=', $dbItem->asset_id)->first();
        if (!is_null($snapshot)) {
            $snapshotListingId = $snapshot->listing_id;
            $snapshot->delete();
            $this->propertiesService->generateSnapshotsStats();
            if (!is_null($snapshotListingId)) {
                foreach (['eur', 'qar', 'usd', 'gbp'] as $currency) {
                    foreach (['', 'en', 'ar'] as $locale) {
                        Cache::forget(CacheKeys::HOMEPAGE_RECENT_PROPERTIES_MAP . "_" . $currency . "_" . $locale);
                        Cache::forget(CacheKeys::SNAPSHOT_PART . $snapshotListingId . "_" . $locale . "_" . $currency);
                    }
                }
            }

            Log::info("WILL REMOVE LISTING AND SNAPSHOT FROM ALL PLATFORMS");
            foreach ($this->exportService->allExportPlatforms() as $platform) {
                $this->exportService->removePropertyFromPlatform($snapshot->listing_id, $platform->id);
                $this->exportService->removePropertySnapshotFromPlatform($snapshot->id, $platform->id);
            }
        }
    }

    public function takeDownOlderSnapshots()
    {
        $threeMonthsFromNow = Carbon::now()->subMonths(3)->format('Y-m-d');
        $olderSnapshots = PropertySnapshot::with('author')->where('updated_at', "<=", $threeMonthsFromNow)->where('publishing_status', 'published')->where(function ($qb) {
            $qb->whereNull('is_short_stay')
                ->orWhere('is_short_stay', 0);
        })->orderBy('updated_at', 'DESC')->get();
        Log::info('Start automatically unpublish of' . $olderSnapshots->count() . ' snapshots');
        if ($olderSnapshots->count() > 0) {
            $agentListings = [];
            foreach ($olderSnapshots as $snapshot) {
                $snapshotListingId = $snapshot->listing_id;
                $listing = Property::where('id', $snapshotListingId)->first();
                if (!is_null($listing)) {
                    $listing->publishing_status = ListingPublishingStatus::STATUS_MASTERLIST;
                    $listing->save();
                    if (!in_array($snapshot->author->email, $agentListings)) {
                        $agentListings[$snapshot->author->email][] = $listing;
                    }
                }
                $snapshot->delete();
                foreach (['eur', 'qar', 'usd', 'gbp'] as $currency) {
                    foreach (['', 'en', 'ar'] as $locale) {
                        Cache::forget(CacheKeys::SNAPSHOT_PART . $snapshotListingId . "_" . $locale);
                    }
                }
            }
            foreach ($agentListings as $agentEmail => $listings) {
                $this->emailService->sendEmailOnUnpublishListingsForAgents($agentEmail, $listings);
            }
            $this->propertiesService->generateSnapshotsStats();
            foreach (['eur', 'qar', 'usd', 'gbp'] as $currency) {
                foreach (['', 'en', 'ar'] as $locale) {
                    Cache::forget(CacheKeys::HOMEPAGE_RECENT_PROPERTIES_MAP . "_" . $currency . "_" . $locale);
                }
            }
            Log::info('Finished the unpublish of ' . $olderSnapshots->count() . ' snapshots');
        } else {
            Log::info('Nothing to do');
        }
    }

    protected function getItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $attributeDefinitionIds = $this->propertiesService->getAttributeDefinitionIds();
        $q = DB::table('properties as ps')
            ->leftJoin('contacts', 'ps.contact_id', '=', 'contacts.id')
            ->leftJoin('assets', 'ps.asset_id', '=', 'assets.id')
            ->leftJoin('geography as location', 'ps.location_id', '=', 'location.id')
            ->leftJoin('geography as location_parent', 'location.parent_id', '=', 'location_parent.id')
            ->join('property_types as pt', 'ps.property_type_id', '=', 'pt.id')
            ->leftJoin('attributes as be', function ($q) use ($attributeDefinitionIds) {
                $q->on('ps.asset_id', '=', 'be.asset_id')
                    ->where('be.attribute_definition_id', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BEDROOMS]);
            })
            ->leftJoin('attributes as ba', function ($q) use ($attributeDefinitionIds) {
                $q->on('ps.asset_id', '=', 'ba.asset_id')
                    ->where('ba.attribute_definition_id', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BATHROOMS]);;
            });

        $extraClause = $this->getRequestParams($request);
        if (!empty($extraClause['sort'])) {
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($extraClause['sort'], $extraClause['dir']);
            } else {
                $q = $q->orderBy($extraClause['sort']);
            }
        } else {
            $q = $q->orderBy('ps.created_at', 'desc');
        }

        $param = QueryParamsDef::FURNISHINGS;
        if (!empty($request->get($param))) {
            $q->join("attributes AS furn_attr", "furn_attr.asset_id", "=", "ps.asset_id")
                ->join("attribute_definitions AS furn_attr_def", "furn_attr_def.id", "=", "furn_attr.attribute_definition_id");
            $q->where("furn_attr_def.name", "=", $request->get($param));
            $q->where("furn_attr.value", "=", 'true');
        }

        if (count($extraClauses) > 0) {
            foreach ($extraClauses as $clause => $clauseValue) {
                $q = $q->where($clause, '=', $clauseValue);
            }
        }

        $param = 'showImported';
        if (!$request->has($param) || ($request->has($param) && $request->get($param) == 0)) {
            $q = $q->whereNull('ps.imported_at');
        }

        foreach ([QueryParamsDef::BEDROOMS, QueryParamsDef::BATHROOMS] as $t) {
            if (($request->has($t) && !empty($request->get($t)))) {
                if ($t == QueryParamsDef::BEDROOMS && $request->get($param) == 'studio') {
                    $q->join("attributes AS studio_attr", "studio_attr.asset_id", "=", "ps.asset_id")
                        ->join("attribute_definitions AS studio_attr_def", "studio_attr_def.id", "=", "studio_attr.attribute_definition_id");
                    $q->where("studio_attr_def.name", "=", $request->get($t));
                    $q->where("studio_attr.value", "=", 'true');
                } else {
                    $q = $q->where($t . '.value', '=', $request->get($t));
                }
            }
        }

        //        foreach (['be', 'ba'] as $t) {
        //            if (($request->has($t) && !empty($request->get($t)))) {
        //                $q = $q->where($t . '.value', '=', $request->get($t));
        //            }
        //        }

        //        foreach (['properties.city_id' => 'cityId', 'properties.region_id' => 'regionId', 'properties.area_id' => 'areaId'] as $t => $param) {
        //            if ($request->has($param) && !empty($request->get($param))) {
        //                $q = $q->where($t, '=', $request->get($param));
        //            }
        //        }

        // budget min & max
        $param = QueryParamsDef::PRICE_FROM;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where(DB::raw('CAST(ps.price AS DECIMAL)'), '>=', $request->get($param));
        }

        $param = QueryParamsDef::PRICE_TO;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where(DB::raw('CAST(ps.price AS DECIMAL)'), '<=', $request->get($param));
        }

        if ($request->has(QueryParamsDef::SQRFT_FROM) || $request->has(QueryParamsDef::SQRFT_TO)) {
            $q->leftJoin('attributes as bua', function ($join) use ($attributeDefinitionIds) {
                $join->on('bua.asset_id', '=', 'ps.asset_id')
                    ->where("bua.attribute_definition_id", $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA]);
            });
        }

        // min & max area
        $param = QueryParamsDef::SQRFT_FROM;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('bua.value', '>=', $request->get($param));
        }

        $param = QueryParamsDef::SQRFT_TO;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('bua.value', '<=', $request->get($param));
        }

        $param = QueryParamsDef::PROPERTY_TYPE;
        if ($request->has($param) && !empty($request->get($param))) {
            if (is_numeric($request->get($param))) {
                $q = $q->where('ps.property_type_id', '=', $request->get($param));
            } else {
                $q = $q->where('pt.url_value', '=', $request->get($param));
            }
        }

        $param = QueryParamsDef::OPERATION_TYPE;
        if ($request->has($param) && !empty($request->get($param))) {
            $q = $q->where('ps.ad_type', '=', $request->get($param));
        }

        $param = QueryParamsDef::AVAILABILITY;
        if ($request->has($param)) {
            $val = $request->get($param);
            if ($val == "0" || $val == "1") {
                $q = $q->where('ps.is_sold_leased', '=', $request->get($param));
            }
        }

        $param = 'bills';
        if ($request->has($param) && !empty($request->get($param)) && in_array($request->get($param), ['included', 'not-included'])) {
            $attrName = $param . "-" . $request->get($param);
            $q->join("attributes AS bills_attr", "bills_attr.asset_id", "=", "ps.asset_id")
                ->join("attribute_definitions AS bills_attr_def", "bills_attr_def.id", "=", "bills_attr.attribute_definition_id");
            $q->where("bills_attr_def.name", "=", $attrName);
            $q->where("bills_attr.value", "=", 'true');
        }

        // $param = 'tenanted';
        // if ($request->has($param) && !empty($request->get($param)) && in_array($request->get($param), ['yes', 'no'])) {
        //     if ($request->get($param) == 'yes') {
        //         $q->join('contacts as tenants', function ($join) {
        //             $join->on('properties.id', '=', 'contacts.property_id')
        //              ->where('contacts.contact_type', '=', 'tenant');
        //         });

        //         $q->where('tenants.lease_start_date', '<', DB::raw('NOW()'));
        //         $q->where('tenants.lease_end_date', '>', DB::raw('NOW()'));
        //     } else {
        //         $q->whereNotExists(function ($query) {
        //             $query->select(DB::raw(1))
        //             ->from('contacts as tenants')
        //             ->whereRaw('properties.id = tenants.property_id AND tenants.lease_start_date < NOW() AND tenants.lease_end_date > NOW()');
        //         });
        //     }
        // }

        $clauses = [];
        // $clauses[] = ['properties.deleted_at', 'IS NOT NULL', null];
        if (!empty($extraClause['wheres']) && is_array($extraClause['wheres']) && count($extraClause['wheres']) > 0) {
            foreach ($extraClause['wheres'] as $k => $filterVal) {
                if ($k == 'ps.is_featured' || $k == 'ps.is_plot') {
                    if ($this->startsWith($filterVal, "y")) {
                        $filterVal = 1;
                    }

                    if (!$this->startsWith($filterVal, "y")) {
                        $filterVal = 0;
                    }
                    $clauses[] = [$k, '=', $filterVal];
                } else {
                    $clauses[] = [$k, 'LIKE', $filterVal . '%'];
                }
            }
        }
        $q = $q->whereNull('ps.deleted_at');

        if (count($clauses) > 0) {
            $q = $q->where($clauses);
        }

        if (!empty($extraClause['q'])) {
            $q = $q->where(function ($query) use ($extraClause) {
                $term = $extraClause['q'] . '%';
                return $query->where('ps.ref_no', 'LIKE', $term)
                    //                    ->orWhere('ps.ref_propertyfinder', 'LIKE', $term)
                    //                    ->orWhere('ps.ref_qatarliving', 'LIKE', $term)
                    //                    ->orWhere('ps.ref_hapondo', 'LIKE', $term)
                    //                    ->orWhere('ps.ref_socialmedia', 'LIKE', $term)
                    ->orWhere('ps.title', 'LIKE', $term)
                    ->orWhere('location.name', 'LIKE', $term)
                    ->orWhere('location_parent.name', 'LIKE', $term)
                    //                    ->orWhere('areas.name', 'LIKE', $term)
                    ->orWhere('ps.address', 'LIKE', $term);
            });
        }

        if (!is_null($groupByClause)) {
            $q->groupBy($groupByClause);
        }

        $q = $q->select(
            'ps.id',
            'ps.ref_no',
            'ps.ad_type',
            'ps.title',
            'ps.address',
            'ps.is_featured',
            'ps.is_plot',
            'ps.is_published',
            'ps.is_active',
            'ps.is_available',
            DB::raw("DATE_FORMAT(ps.created_at, '%d.%m.%Y %H:%i') as created_at"),
            'ps.updated_at',
            DB::raw('COALESCE(ps.price, 0) as price'),
            'ps.location_id',
            'ps.property_type_id',
            'assets.id as asset_id',
            'contacts.name',
            'be.value as beds_no',
            'ba.value as baths_no',
            ($request->has('areaMin') || $request->has('areaMax')) ? 'bua.value as build_up_area' : DB::raw('"" as build_up_area'),
            'pt.filter_value as property_type_filter_value',
            'pt.label as property_type_label',
            'location.slug as location',
            'location.name as location_name',
            'location_parent.slug as location_parent',
            'location_parent.name as location_parent_name',
            //            'areas.slug as area',
            //            'areas.name as area_name',
            'ps.ref_propertyfinder',
            'ps.ref_qatarliving',
            'ps.ref_hapondo',
            'ps.ref_socialmedia',
            'ps.tower',
            'ps.unit_type',
            'ps.unit_no'
        );

        // dd($q->toSql());

        return $q;
    }

    public function getItems(Request $request, $skipPagination = false, $extraClauses = [], $groupByClause = null)
    {
        $q = $this->getItemsBaseQ($request, $extraClauses, $groupByClause);

        if (!$skipPagination) {
            $offset = $this->getRequestOffset($request);
            $limit = $this->getRequestLimit($request);
            $q = $q->offset($offset)->limit($limit);
        }

        //         dd($q->toSql());

        $items = $q->get();
        return $items;
    }

    public function getItemsCount(Request $request)
    {
        $q = $this->getItemsBaseQ($request);
        $count = $q->count();

        return $count;
    }

    public function prepareLeadSuggestionItems($properties, $allowedActions = [])
    {
        $items = $properties->map(
            function ($item) use ($allowedActions) {
                $values = [];
                //"System" fields - defined in Property
                $fields = ["id", "ref_no", "title", 'location'];

                foreach ($fields as $index => $field) {
                    $values[$index] = $item->$field;

                    if ($values[$index] == null) {
                        $values[$index] = "";
                    }
                }

                $values['ref_no'] = $item->ref_no;
                $values['address'] = $item->address;
                $values['title'] = $item->title;
                $values['bedrooms'] = empty($item->beds_no) ? "-" : $item->beds_no;
                $values['bathrooms'] = empty($item->baths_no) ? "-" : $item->baths_no;
                $values['property_type'] = empty($item->property_type_label) ? "-" : $item->property_type_label;
                $values['operation_type'] = empty($item->ad_type) ? "-" : $item->ad_type;
                $values['price'] = empty($item->price) ? "0" : \number_format($item->price, 2);
                $values['build_up_area'] = empty($item->build_up_area) ? "-" : $item->build_up_area;
                $values['is_published'] = empty($item->is_published) ? "No" : "Yes";
                $values['is_active'] = empty($item->is_active) ? "No" : "Yes";
                $values['is_available'] = empty($item->is_available) ? "No" : "Yes";
                $values['created_at'] = $item->created_at;
                $values['updated_at'] = $item->updated_at;

                //                $routeParams = getRouteParamsNext($item);
                if (!is_null($item->ref_no)) {

                    $routeListingItem = new RouteListingItem();
                    $routeListingItem->id = $item->id;
                    $routeListingItem->ref_no = $item->ref_no;
                    $routeListingItem->ad_type = $item->ad_type;
                    $routeListingItem->geography_slug = '';
                    $routeListingItem->bedrooms = intval($item->beds_no) ?? 0;

                    $cachedGeographies = $this->propertiesService->cachedGeographiesMap();
                    $cachedPropertyTypesHashmap = $this->propertiesService->cachedPropertyTypesHashmap();

                    foreach ($cachedGeographies as $geoSlug => $geoData) {
                        if ($geoData['id'] == $item->location_id) {
                            $usedSlug = $geoSlug;
                            $routeListingItem->geography_slug = $usedSlug;
                        }
                    }

                    foreach ($cachedPropertyTypesHashmap as $_ => $propertyTypeData) {
                        if ($propertyTypeData['id'] == $item->property_type_id) {
                            $routeListingItem->property_type_url = $propertyTypeData['filter_value'];
                        }
                    };
                    $itemLink = MenuHelperService::createListingURL($routeListingItem);
                } else {
                    $itemLink = '#';
                }

                $values['slug'] = $itemLink;
                $values["ref_propertyfinder"] = $item->ref_propertyfinder;
                $values["ref_qatarliving"] = $item->ref_qatarliving;
                $values["ref_hapondo"] = $item->ref_hapondo;
                $values["ref_socialmedia"] = $item->ref_socialmedia;

                $actions = [];
                if (count($allowedActions) > 0) {
                    Log::info("Allowed actions for property " . $item->ref_no, compact('allowedActions'));
                    if ($allowedActions['read']) {
                        $actions['read'] = route('inventory.edit', ['id' => $item->asset_id]);
                    }
                }

                $values['actions'] = $actions; //Dummy column for row editor buttons

                $values['DT_RowId'] = $item->asset_id; //Row Id for DataTables.js

                $values['is_featured'] = $item->is_featured ? "Yes" : "No";
                $values['is_plot'] = $item->is_plot ? "Yes" : "No";
                $values["landlord"] = $this->getLandlordName($item);
                return $values;
            }
        );

        return $items;
    }

    private function getLandlordName($propertyItem)
    {
        if (auth()->user()->hasRole(RolesDef::OFFICE_MANAGER)) {
            return $propertyItem->name;
        }

        return "Serban Spirea";
    }

    public function getAgentsProperties($agentId)
    {
        $properties = PropertySnapshot::where('created_by', $agentId)->get();
        return $properties;
    }

    public function getTopListingsForSale()
    {
        DB::table('inventory_x_tags')->truncate();

        $propertiesForRent = Property::where('ad_type', 'rent')
            ->whereNull('deleted_at')
            ->whereNotNull('price')
            ->where('price', '>', 0)
            ->where('publishing_status', 'LIKE', 'published')
            ->where(function ($qb) {
                $qb->where('status', 'available')->orWhereNull('status');
            })
            ->orderBy('price', 'asc')
            ->limit(50)
            ->with('tags')
            ->get();

        $propertiesForSale = Property::where('ad_type', 'sale')
            ->whereNull('deleted_at')
            ->whereNotNull('price')
            ->where('price', '>', 0)
            ->where('publishing_status', 'LIKE', 'published')
            ->where(function ($qb) {
                $qb->where('status', 'available')->orWhereNull('status');
            })
            ->orderBy('price', 'asc')
            ->limit(50)
            ->with('tags')
            ->get();

        $bestDealsTag = InventoryTag::where('label', 'BD')->first();

        foreach ($propertiesForRent as $property) {
            $property->tags()->attach($bestDealsTag->id);
        }

        foreach ($propertiesForSale as $property) {
            $property->tags()->attach($bestDealsTag->id);
        }
        Log::info('Am generat best deals');
    }
}
