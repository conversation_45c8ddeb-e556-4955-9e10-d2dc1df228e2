<?php


namespace App\Services;

use App\Models\Email\BrokerEmail;
use App\Models\Email\UserEmail;
use App\Models\Email\ContactEmail;
use App\Models\Email\ForUserEmail;
use App\Models\Email\FranchiseEmail;
use App\Models\Email\DevelopmentsEmail;
use App\Models\Email\InventoryOperationHistoryAdd;
use App\Models\Email\AssignedLead;
use App\Models\Email\InventoryDataChange;
use App\Models\Email\LandlordContract;
use App\Models\Email\LandlordExists;
use App\Models\Email\LeadsAdded;
use App\Models\Email\ListingDeleteRequest;
use App\Models\Email\PropertyAdded;
use App\Models\Email\PublishingStatusChange;
use App\Models\Email\TourAdded;
use App\Models\Crm\RolesDef;
use App\Models\Email\AdminReminderWhenDealsExpires;
use App\Models\Email\AgentAfterDealsIsAprroved;
use App\Models\Email\AgentConnectionEmail;
use App\Models\Email\AgentListingStatusReminder;
use App\Models\Email\AgentNoteReminder;
use App\Models\Email\AgentRatingEmail;
use App\Models\Email\AgentReminderWhenContractExpires;
use App\Models\Email\AgentUnpublishedOlderListing;
use App\Models\Email\BirthdayReminderEmail;
use App\Models\Email\ClientDealsEmail;
use App\Models\Email\ClientThankYouEmailAfterReview;
use App\Models\Email\DealsOperationHistoryAdd;
use App\Models\Email\DocumentsToClientOrOwner;
use App\Models\Email\EmailOnCreatePublicLead;
use App\Models\Email\LandlordContractExpiringEmail;
use App\Models\Email\LandlordDealsEmail;
use App\Models\Email\LandlordPublishedListingEmail;
use App\Models\Email\LandlordVerificationExpiredEmail;
use App\Models\Email\LeadsOperationHistoryAdd;
use App\Models\Email\OfficeManagerDealsApprovalEmail;
use App\Models\Email\PropertyVisitOwnerNotification;
use App\Models\Email\PFSyncResponseEmail;
use App\Models\Email\Qatar2022BookingEmail;
use App\Models\Email\ResetPasswordCodeEmail;
use App\Models\Email\SendDOBReminder;
use App\Models\Email\SendHBEmail;
use App\Models\Email\StatsReportEmail;
use App\Models\Email\BookingNotificationToClient;
use App\Models\Email\BookingNotificationToOffice;
use App\Models\Email\PropertyVisitClientNotification;
use App\Models\Email\JMJEnquiryConfirmationMessage;
use App\Models\Email\JMJEnquiryNotificationToHaya;
use App\Models\Email\LeadReassignNotification;
use App\Models\Lead;
use App\Models\Contact;
use App\Models\Email\AdminQNBRequest;
use App\Models\Email\AgentListingsReport;
use App\Models\Email\SendCompetitionWinnerEmail;
use App\Models\Email\ClientQNBFunding;
use App\Models\Email\CViewBookConfirmationMessage;
use App\Models\Email\CViewBookNotification;
use App\Models\Email\CViewEnquiryConfirmationMessage;
use App\Models\Email\CViewEnquiryNotification;
use App\Models\Email\DailyShortStatsEmail;
use App\Models\Email\EmailToAgentsForLandlords;
use App\Models\Email\EmailToAgentWithPayslips;
use App\Models\Email\EmailToAgentWithPerformance;
use App\Models\Email\EmailToAgentsForPerformance;
use App\Models\Email\InvoicePaymentDeleteEmail;
use App\Models\Email\EmailToAgentNotify5daysBeforeMoveLead;
use App\Models\Email\LeadStatsPerMonthEmail;
use App\Models\Email\LeadWithRatingPerAgentStats;
use App\Models\Email\TasksReportEmail;
use App\Models\InvoicePayment;
use App\Models\Tour;
use App\Models\Property;
use App\Models\User;
use Mail;
use Log;

class EmailService
{
    public $emailSendActive = false;

    public function __construct()
    {
        $this->emailSendActive = env('MAIL_SEND_ACTIVE', false);
    }

    public function sendPropertyAddedEmail(\App\Models\Property $property)
    {
        if ($property->author->hasAnyRole(
            RolesDef::OFFICE_MANAGER,
            RolesDef::MASTER_BROKER
        )) {
            Log::info("Will not send property added email because the user already has an approval role");
            return;
        }
        $mailerObject = new PropertyAdded($property);
        $this->sendEmail($mailerObject);
        Log::info("Sent property added email for (" . $property->ref_no . ") to " . join(", ", $mailerObject->getTo()));
    }

    public function sendPublishingStatusChangedEmail($data, $destinationEmail, $changeRequest = false)
    {
        $mailerObject = new PublishingStatusChange($data, $destinationEmail, $changeRequest);
        $this->sendEmail($mailerObject);
        Log::info("Sent publishing status changed email to " . $destinationEmail);
    }

    public function sendEditChangesEmail($listing, $allChanges, $currentUser)
    {
        $mailerObject = new InventoryDataChange($listing, $allChanges, $currentUser);
        $this->sendEmail($mailerObject);
        Log::info("Sent edit changes email to " . join(",", $mailerObject->getTo()));
    }

    public function sendRequestForDelete(Property $property, string $deletionReason, $actionAuthor)
    {
        $mailerObject = new ListingDeleteRequest($property, $deletionReason, $actionAuthor);
        $this->sendEmail($mailerObject);
        Log::info("Sent edit changes email to " . join(", ", $mailerObject->getTo()));
    }

    public function leadAssignedEmail($data, $destinationEmail)
    {
        $mailerObject = new AssignedLead($data, $destinationEmail);
        $this->sendEmail($mailerObject);

        Log::info("Sent assigned lead notification email to " . $destinationEmail);
    }

    public function createLeadEmail(Lead $lead)
    {
        $mailerObject = new LeadsAdded($lead);
        $this->sendEmail($mailerObject);

        Log::info("Sent lead added email to " . join(", ", $mailerObject->getTo()));
    }

    public function createTourEmail(Tour $tour)
    {
        $mailerObject = new TourAdded($tour);
        $this->sendEmail($mailerObject);

        Log::info("Sent tour added email.");
    }

    public function ifExistLandlordEmail($landlord, $mailPayload, $user, $destinationEmail)
    {
        $mailerObject = new LandlordExists($landlord, $mailPayload, $user, $destinationEmail);
        $this->sendEmail($mailerObject);

        Log::info("Sent landlord exist email to " . $destinationEmail . " with data: " . $mailerObject->getTo());
    }

    public function saveLandlordContract($destinationEmail, $attachmentData)
    {
        $mailerObject = new LandlordContract($destinationEmail, $attachmentData);
        $this->sendEmail($mailerObject);

        Log::info("Sent landlord contract email to " . $destinationEmail . " with data: " . $mailerObject->getTo());
    }

    public function sendEmailContact($payload, $destinationEmail)
    {
        $mailerObject = new ContactEmail($payload, $destinationEmail);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Sent email to " . $destinationEmail);
    }

    public function sendEmailToSender($destinationEmailForUser)
    {
        $mailerObject = new ForUserEmail($destinationEmailForUser);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Sent email to " . $destinationEmailForUser);
    }

    public function sendEmailFranchise($payload, $destinationEmail)
    {
        $mailerObject = new FranchiseEmail($payload, $destinationEmail);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Sent email to " . $destinationEmail);
    }

    public function sendEmailDevelopments($payload, $destinationEmail)
    {
        $mailerObject = new DevelopmentsEmail($payload, $destinationEmail);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Sent email to " . $destinationEmail);
    }

    public function sendEmailToBroker($broker, $snapshot, $payload)
    {
        $mailerObject = new BrokerEmail($broker, $snapshot, $payload);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Mail to broker sent. [" . $broker->email . "]");
    }

    public function sendEmailToMessageAgentContactSender($destinationEmail, $snapshot)
    {
        $mailerObject = new UserEmail($destinationEmail, $snapshot);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Sent email to " . $destinationEmail);
    }

    public function sendEmailOnAddingOperationHistory($listing, $operationHistoryAuthor, $operationHistoryBody)
    {
        $mailerObject = new InventoryOperationHistoryAdd($listing, $operationHistoryAuthor, $operationHistoryBody);
        $this->sendEmail($mailerObject);
        Log::info("New remarks added and the changes has been sent. " . join(",", $mailerObject->getTo()));
    }

    public function sendEmailOnAddingOperationHistoryForLeads($lead, $operationHistoryAuthor, $operationHistoryBody)
    {
        $mailerObject = new LeadsOperationHistoryAdd($lead, $operationHistoryAuthor, $operationHistoryBody);
        $this->sendEmail($mailerObject);
        Log::info("New remarks added and the changes has been sent. " . join(",", $mailerObject->getTo()));
    }

    public function sendEmailOnAddingOperationHistoryForDeals($deal, $operationHistoryAuthor, $operationHistoryBody)
    {
        $mailerObject = new DealsOperationHistoryAdd($deal, $operationHistoryAuthor, $operationHistoryBody);
        $this->sendEmail($mailerObject);
        Log::info("New remarks added and the changes has been sent. " . join(",", $mailerObject->getTo()));
    }

    public function sendAgentReminders($agentData, $properties)
    {
        $mailerObject = new AgentListingStatusReminder($agentData, $properties);
        $this->sendEmail(
            $mailerObject
        );
        // Log::info("Agent reminder email sent to: " . $destinationEmail);
    }

    public function sendAgentRemindersWhenContractExpires($property)
    {
        $mailerObject = new AgentReminderWhenContractExpires($property);
        $this->sendEmail(
            $mailerObject
        );
        // Log::info("Agent reminder email sent to: " . $destinationEmail);
    }

    public function sendStatsReportEmail($destinationEmail, $allData)
    {
        $mailerObject = new StatsReportEmail($destinationEmail, $allData);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Stats report email sent to: " . $destinationEmail);
    }

    public function sendDOBReminder($contact)
    {
        $mailerObject = new SendDOBReminder($contact);
        $this->sendEmail(
            $mailerObject
        );
        // Log::info("Agent reminder email sent to: " . $destinationEmail);
    }

    public function sendHBEmail($contact)
    {
        $mailerObject = new SendHBEmail($contact);
        $this->sendEmail(
            $mailerObject
        );
        // Log::info("Happy birthday to: " . $destinationEmail);
    }

    public function sendQatar2022BookingEmail($data)
    {
        $mailerObject = new Qatar2022BookingEmail($data);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Qatar2022 form submit sent to: " . $mailerObject->getTo());
    }

    public function sendPFSyncResponseEmail($data)
    {
        $mailerObject = new PFSyncResponseEmail($data);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("PF Sync response email sent out to: " . $mailerObject->getTo());
    }

    public function sendEmailToClientWhenDealAreClosed($deal, $destinationEmail, $property)
    {
        $mailerObject = new ClientDealsEmail($deal, $destinationEmail, $property);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to client after a deal are closed " . $mailerObject->getTo());
    }

    public function sendEmailToLandlordWhenDealAreClosed($deal, $destinationEmail, $property)
    {
        $mailerObject = new LandlordDealsEmail($deal, $destinationEmail, $property);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to owner after a deal are closed " . $mailerObject->getTo());
    }

    public function sendAgentRatingEmail(User $user, $data)
    {
        $mailerObject = new AgentRatingEmail($user, $data);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Email when receive rating sent to " . $mailerObject->getTo());
    }

    public function sendAgentConnectionEmail(User $user, $data)
    {
        $mailerObject = new AgentConnectionEmail($user, $data);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Email when receive rating sent to " . $mailerObject->getTo());
    }

    public function sendLandlordPublishedListingEmail($property, $destinationEmail)
    {
        $mailerObject = new LandlordPublishedListingEmail($property, $destinationEmail);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Listing published email for listing [" . $property->ref_no . "] was sent to " . $mailerObject->getTo());
    }

    public function sendThankYouEmailToClientAfterRatingApproved(User $user, $data)
    {
        $mailerObject = new ClientThankYouEmailAfterReview($user, $data);
        $this->sendEmail(
            $mailerObject
        );
    }

    public function sendAgentNoteReminders($reminder, $destinationEmail)
    {
        $mailerObject = new AgentNoteReminder($reminder, $destinationEmail);
        $this->sendEmail(
            $mailerObject
        );
    }

    public function sendLandlordContractExpiringNotification($landlords)
    {
        $mailerObject = new LandlordContractExpiringEmail($landlords);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Landlord contract expiring email was sent for [" . $landlords->count() . "] records");
    }

    public function sendLandlordVerificationExpiredNotification($landlords)
    {
        $mailerObject = new LandlordVerificationExpiredEmail($landlords);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Landlord verification expired email was sent for [" . $landlords->count() . "] records");
    }

    public function sendEmailOnUnpublishListingsForAgents($agentEmail, $listings)
    {
        $mailerObject = new AgentUnpublishedOlderListing($agentEmail, $listings);
        $this->sendEmail(
            $mailerObject
        );
    }

    public function sendEmailToOMForDealApproval($deal)
    {
        $mailerObject = new OfficeManagerDealsApprovalEmail($deal);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to office manager for approve the deal " . json_encode($mailerObject->getTo()));
    }

    public function sendEmailToAgentAfterDealApproval($deal)
    {
        $mailerObject = new AgentAfterDealsIsAprroved($deal);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to agent after the deal is approved" . $mailerObject->getTo());
    }

    public function sendDocumentsToClientOrOwner($deal, $addedForms, $personType)
    {
        $mailerObject = new DocumentsToClientOrOwner($deal, $addedForms, $personType);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to owner and client after the documents was uploaded" . json_encode($mailerObject->getTo()));
    }

    public function sendResetPasswordCode($emailAddress, $code)
    {
        $mailerObject = new ResetPasswordCodeEmail($emailAddress, $code);
        $this->sendEmail(
            $mailerObject,
        );
    }

    public function sendBirthdayReminder($users)
    {
        if ($users->count()) {
            $mailerObject = new BirthdayReminderEmail($users);
            $this->sendEmail(
                $mailerObject
            );
        }
    }

    public function sendEmailOnPublicLeadAdded(Lead $lead, $requestData)
    {
        $mailerObject = new EmailOnCreatePublicLead($lead, $requestData);
        $this->sendEmail(
            $mailerObject
        );
    }

    public function sendBookingNotificationToOffice($createdBooking, $room, $validData)
    {
        $mailerObject = new BookingNotificationToOffice($createdBooking, $room, $validData);
        $this->sendEmail(
            $mailerObject,
            true
        );
    }

    public function sendBookingNotificationToClient($createdBooking, $startDate, $endDate, $diffInDays, $validData)
    {
        $mailerObject = new BookingNotificationToClient($createdBooking, $startDate, $endDate, $diffInDays, $validData);
        $this->sendEmail(
            $mailerObject,
            true
        );
    }

    public function sendPropertyVisitOwnerNotification($property, $lead, $reminderData)
    {
        $mailerObject = new PropertyVisitOwnerNotification($property, $lead, $reminderData);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to owner when a client is interested" . $mailerObject->getTo());
    }

    public function sendPropertyVisitClientNotification($property, $lead, $reminderData)
    {
        $mailerObject = new PropertyVisitClientNotification($property, $lead, $reminderData);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to client " . $mailerObject->getTo());
    }

    public function sendAdminRemindersWhenDealsExpires($deals, $destinationEmail, $authorName)
    {
        $mailerObject = new AdminReminderWhenDealsExpires($deals, $destinationEmail, $authorName);
        $this->sendEmail(
            $mailerObject
        );
    }

    public function sendJMJEnquiryConfirmationMessageToContact(Contact $contact)
    {
        $mailerObject = new JMJEnquiryConfirmationMessage($contact);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("JMJ enquiry confirmation message sent to " . $mailerObject->getTo());
    }

    public function sendJMJEnquiryNotificationToHaya(Lead $lead, $formName)
    {
        $mailerObject = new JMJEnquiryNotificationToHaya($lead, $formName);
        $this->sendEmail(
            $mailerObject
        );
    }

    public function sendCViewBookConfirmationMessageToContact(Contact $contact)
    {
        $mailerObject = new CViewBookConfirmationMessage($contact);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("CView book confirmation message sent to " . $mailerObject->getTo());
    }

    public function sendCViewBookNotificationToHaya(Lead $lead, $unit_no)
    {
        $mailerObject = new CViewBookNotification($lead, $unit_no);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("CView book notification sent to FGREALTY");
    }

    public function sendCViewEnquiryConfirmationMessageToContact(Contact $contact)
    {
        $mailerObject = new CViewEnquiryConfirmationMessage($contact);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("CView enquiry confirmation message sent to " . $mailerObject->getTo());
    }

    public function sendCViewEnquiryNotification(Lead $lead)
    {
        $mailerObject = new CViewEnquiryNotification($lead);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("CView enquiry notification sent to FGREALTY");
    }

    public function sendAgentsListingReport($properties, $agent)
    {
        $mailerObject = new AgentListingsReport($properties, $agent);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to agent with listings report " . $mailerObject->getTo());
    }

    public function sendEmailOnLeadReassign($lead, $user)
    {
        $mailerObject = new LeadReassignNotification($lead, $user);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Email sent on lead reassignment to " . $mailerObject->getTo());
    }

    public function sendCompetitionWinnerEmail($agent, $statistics)
    {
        $mailerObject = new SendCompetitionWinnerEmail($agent, $statistics);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Email to competition winner to: " . $mailerObject->getTo());
    }

    public function sendEmailToClientForQNBFunding($destinationEmail, $clientName)
    {
        $mailerObject = new ClientQNBFunding($destinationEmail, $clientName);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to client about QNB funding to " . $mailerObject->getTo());
    }

    public function sendEmailToAdminForQNBRequest($allData)
    {
        $mailerObject = new AdminQNBRequest($allData);
        $this->sendEmail(
            $mailerObject
        );
        Log::info("Send email to admin for qnb funding");
    }

    public function sendLeadStatsPerMonth($leadStats, $startDate, $endDate)
    {
        $mailerObject = new LeadStatsPerMonthEmail($leadStats, $startDate, $endDate);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Sent leads stats email");
    }

    public function sendEmailToAgentsForLandlords($user, $item, $validFields)
    {
        $mailerObject = new EmailToAgentsForLandlords($user, $item, $validFields);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Sent email to agent when was assigned to a landlord unit. " . $user->email);
    }

    public function sendEmailToAgentWithPerformance($uploadedDocuments, $user)
    {
        $mailerObject = new EmailToAgentWithPerformance($uploadedDocuments, $user);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Sent email to agent when was performance document is uploaded. " . $user);
    }

    public function sendEmailToAgentWithPayslips($uploadedDocuments, $user)
    {
        $mailerObject = new EmailToAgentWithPayslips($uploadedDocuments, $user);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Sent email to agent when was payslips is uploaded. " . $user);
    }

    public function sendEmailToUserForPerformance($user)
    {
        $mailerObject = new EmailToAgentsForPerformance($user);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Sent email to agent with notification that the performance document has been uploaded " . $user->email);
    }

    public function sendRatedLeadsReport($leadStats)
    {
        $mailerObject = new LeadWithRatingPerAgentStats($leadStats);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Leads with rating per agent report email sent.");
    }

    public function sendTasksReport($leadStats)
    {
        $mailerObject = new TasksReportEmail($leadStats);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Tasks report email was sent.");
    }

    public function sendDailyShortStats($reportData)
    {
        $mailerObject = new DailyShortStatsEmail($reportData);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Daily short stats sent");
    }

    public function sendEmailOnDeletingPaymentProof(InvoicePayment $payment, $currentUser) {
        $mailerObject = new InvoicePaymentDeleteEmail($payment, $currentUser);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Invoice payment deleted email sent.");
    }

    public function emailToAgentNotify5daysBeforeMove($leads, $agent) {
        $mailerObject = new EmailToAgentNotify5daysBeforeMoveLead($leads, $agent);
        $this->sendEmail(
            $mailerObject,
        );
        Log::info("Notification email sent to agent - 48h before move");
    }

    private function sendEmail($mailerObject, $shouldStop = false)
    {
        $to = $mailerObject->getTo();
        $cc = $mailerObject->getCC();
        $from = $mailerObject->getFrom();
        $subject = $mailerObject->getSubject();
        $template = $mailerObject->getTemplateName();
        $params = $mailerObject->getTemplateVars();
        $attachmentData = $mailerObject->getAttachmentData();

        try {
            if ($shouldStop) {
                $view = view($template, $params ?? []);
                echo $view;
                dd('after view');
            }

            if ($this->emailSendActive) {
                Mail::send($template, $params ?? [], function ($m) use ($to, $from, $subject, $attachmentData, $cc) {
                    if (is_array($from) && count($from) == 2) {
                        $m->from($from[0], $from[1]);
                    } else {
                        $m->from($from);
                    }
                    if (is_array($to)) {
                        foreach ($to as $mailTo) {
                            $m->to($mailTo);
                        }
                    } else {
                        $m->to($to);
                    }
                    if (!empty($cc)) {
                        if (is_array($cc)) {
                            foreach ($cc as $mailCCTo) {
                                $m->to($mailCCTo);
                            }
                        } else {
                            $m->to($cc);
                        }
                    }

                    $m->bcc(env('MAIL_BCC_ADDRESS'));
                    $m->subject($subject);

                    // attachment
                    if (!is_null($attachmentData) && is_array($attachmentData) && isset($attachmentData['attachment_path']) && isset($attachmentData['attachment_title'])) {
                        $attachmentArgs = [];
                        if (!empty($attachmentData['attachment_title'])) {
                            $attachmentArgs = ['as' => $attachmentData['attachment_title']];
                        }
                        $m->attach($attachmentData['attachment_path'], $attachmentArgs);
                    }
                });
            } else {
                $toStr = is_array($to) ? join(", ", $to) : $to;
                Log::info("Skip sending email to $toStr, from $from");
            }
        } catch (\Exception $Ex) {
            // dd($Ex->getMessage());
            // dd($Ex->getTraceAsString());
            Log::error($Ex->getMessage());
        }
    }
}
