<?php

namespace App\Services;

use App\Models\ActiveNotification;
use App\Models\CacheKeys;
use App\Models\Contact;
use App\Models\Crm\ContactsListTag;
use App\Models\Lead;
use App\Models\Crm\Deal;
use App\Models\LeadStatus;
use App\Models\DealAgent;
use App\Models\Property;
use App\Models\Invoice;
use Illuminate\Support\Facades\DB;
use App\Models\LeadInteractionTracking;
use Carbon\Carbon;
use App\Models\LeadProposal;
use App\Models\LeadAssignment;
use App\Models\LeadFollowupTask;
use App\Models\LeadSource;
use App\Models\LeadXStatus;
use App\Models\Notification;
use App\Models\PropertySnapshot;
use App\Models\QueryParamsDef;
use App\Models\RequestParamsMap;
use App\Models\Timings;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class LeadsService
{
    private $propertiesService;
    private $notesService;
    private $emailService;
    private $operationHistoryService;
    private $inventoryService;
    private $dealsService;
    private $brokerLandlordsService;
    private $leadStatusService;
    private $contactsService;
    private $notificationsService;
    private $authorizationService;

    public static $documentOptions = [
        ['text' => "Contract", 'value' => "contract", 'disabled' => true],
        ['text' => "Lease Agreement", 'value' => "leaseAgreement"],
        ['text' => "Sales and Purchase Agreement (SPA)", 'value' => "SalesAndPurchaseAgreement"],
        ['text' => "Passport", 'value' => "passport"],
        ['text' => "PVF", 'value' => "pvf"],
        ['text' => "Breakdown Payment", 'value' => "breakdown"],
        ['text' => "Copy of Payments", 'value' => "copy"],
        ['text' => "Salary Certificate", 'value' => "salary"],
        ['text' => "Credit Bureau", 'value' => "credit"],
        ['text' => "Booking Form", 'value' => "bookingForm"],
        ['text' => "Receipt", 'value' => "receipt"],
        ['text' => "Handover Form", 'value' => "handoverForm"],
        ['text' => "Trading License", 'value' => "tradingLicense"],
        ['text' => "Company Registration", 'value' => "companyRegistration"],
        ['text' => "Tenant QID", 'value' => "tenantQID"],
        ['text' => "Ownership Documents (Title Deed or SPA)", 'value' => "ownershipDocuments"],
        ['text' => "Owner QID", 'value' => "ownerQID"],
        ['text' => "Cheque Copies", 'value' => "chequeCopies"],
        ['text' => "Lease Agreement", 'value' => "leaseAgreement"],
        ['text' => "Buyer QID", 'value' => "buyerQID"],
        ['text' => "Seller QID", 'value' => "sellerQID"],
        ['text' => "New Ownership Documents (Title Deed or SPA)", 'value' => "newOwnershipDocuments"],
        ['text' => "MOU or Reservation Form", 'value' => "mouReservation"],
        ['text' => "Trade License", 'value' => "tradeLicense"],
        ['text' => "Business Card", 'value' => "businessCard"],
    ];

    public static $documentsListType = [
        ['value' => '', 'label' => 'Please select'],
        ['value' => 'residentialRent', 'label' => 'Residential rent'],
        ['value' => 'residentialSale', 'label' => 'Residential sale'],
        ['value' => 'commercialRent', 'label' => 'Commercial rent'],
        ['value' => 'commercialSale', 'label' => 'Commercial sale'],
    ];

    public $residentialRentOptions = [
        ['text' => "PVF", 'value' => "pvf"],
        ['text' => "Booking Form", 'value' => "bookingForm"],
        ['text' => "Tenant QID", 'value' => "tenantQID"],
        ['text' => "Ownership Documents (Title Deed or SPA)", 'value' => "ownershipDocuments"],
        ['text' => "Owner QID", 'value' => "ownerQID"],
        ['text' => "Cheque Copies", 'value' => "chequeCopies"],
        ['text' => "Lease Agreement", 'value' => "leaseAgreement"],
    ];

    public $residentialSaleOptions = [
        ['text' => "PVF", 'value' => "pvf"],
        ['text' => "Booking Form", 'value' => "bookingForm"],
        ['text' => "Buyer QID", 'value' => "buyerQID"],
        ['text' => "Ownership Documents (Title Deed or SPA)", 'value' => "ownershipDocuments"],
        ['text' => "Seller QID", 'value' => "sellerQID"],
        ['text' => "Cheque Copies", 'value' => "chequeCopies"],
        ['text' => "New Ownership Documents (Title Deed or SPA)", 'value' => "newOwnershipDocuments"],
        ['text' => "MOU or Reservation Form", 'value' => "mouReservation"],
    ];

    public $commercialRentOptions = [
        ['text' => "PVF", 'value' => "pvf"],
        ['text' => "Booking Form", 'value' => "bookingForm"],
        ['text' => "Tenant QID", 'value' => "tenantQID"],
        ['text' => "Ownership Documents (Title Deed or SPA)", 'value' => "ownershipDocuments"],
        ['text' => "Owner QID", 'value' => "ownerQID"],
        ['text' => "Trade License", 'value' => "tradeLicense"],
        ['text' => "Cheque Copies", 'value' => "chequeCopies"],
        ['text' => "Lease Agreement", 'value' => "leaseAgreement"],
    ];

    public $commercialSaleOptions = [
        ['text' => "PVF", 'value' => "pvf"],
        ['text' => "Booking Form", 'value' => "bookingForm"],
        ['text' => "Buyer QID", 'value' => "buyerQID"],
        ['text' => "Ownership Documents (Title Deed or SPA)", 'value' => "ownershipDocuments"],
        ['text' => "Sellet QID", 'value' => "sellerQID"],
        ['text' => "Cheque Copies", 'value' => "chequeCopies"],
        ['text' => "New Ownership Documents (Title Deed or SPA)", 'value' => "newOwnershipDocuments"],
        ['text' => "MOU or Reservation Form", 'value' => "mouReservation"],
    ];

    public function __construct(
        PropertiesService $propertiesService,
        NotesService $notesService,
        EmailService $emailService,
        OperationHistoryService $operationHistoryService,
        InventoryService $inventoryService,
        DealsService $dealsService,
        BrokerLandlordsService $brokerLandlordsService,
        LeadStatusService $leadStatusService,
        ContactsService $contactsService,
        NotificationsService $notificationsService,
        AuthorizationService $authorizationService
    ) {
        $this->propertiesService = $propertiesService;
        $this->notesService = $notesService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->inventoryService = $inventoryService;
        $this->dealsService = $dealsService;
        $this->brokerLandlordsService = $brokerLandlordsService;
        $this->leadStatusService = $leadStatusService;
        $this->contactsService = $contactsService;
        $this->notificationsService = $notificationsService;
        $this->authorizationService = $authorizationService;
    }

    public function getPaginated($params)
    {
        $perPage = $params['perPage'] ?? 12;
        return Lead::whereNotNull('platform')->with(['contact', 'nationality'])->orderBy('id', 'desc')->paginate($perPage);
    }

    public function getSimilarEntries($leadId)
    {
        $q = DB::table('leads as l')
            ->join('leads as se', 'se.contact_id', '=', 'l.contact_id')
            ->join('property_types as pt', 'pt.id', '=', 'se.property_type_id')
            ->join('geography as g', 'g.id', '=', 'se.location_id')
            ->where('l.id', $leadId)
            ->where('se.id', '!=', $leadId)
            ->select([
                'se.id as id',
                'se.created_at as created_at',
                'pt.label as property_type_label',
                'g.name as location',
                'se.request_action',
                'se.status'
            ]);

        return $q->get()->map(function ($item) {
            return [
                'created_at' => $item->created_at,
                'url' => route('leads.edit', ['leadId' => $item->id]),
                'property_type' => $item->property_type_label,
                'location' => $item->location,
                'request_action' => $item->request_action,
                'status' => $item->status,
            ];
        });
    }

    public function getSuggestions($leadId)
    {

        $attributeDefinitions = $this->propertiesService->getAttributeDefinitionIds();

        $snapshotSuggestions =
            DB::select(DB::raw("
                SELECT
                       p.id,
                       ps.asset_id,
                       ps.ref_no,
                       ps.title,
                       ps.price,
                       ps.best_price,
                       sbath.value as bathrooms,
                       sbed.value as bedrooms,
                       sbua.value as built_up_area,
                       a.name attachment_name
                FROM
                    leads l
                    JOIN property_snapshots as ps ON l.property_type_id = ps.property_type_id AND l.location_id = ps.location_id AND IF(l.request_action = 'rent', ps.ad_type = 'rent', ps.ad_type = 'sale')
                    JOIN properties p ON p.asset_id = ps.asset_id
                    LEFT JOIN snapshot_attributes sbath ON sbath.asset_id = ps.asset_id AND sbath.attribute_definition_id = {$attributeDefinitions['property-features-bathrooms']}
                    LEFT JOIN snapshot_attributes sbed ON sbed.asset_id = ps.asset_id AND sbed.attribute_definition_id = {$attributeDefinitions['property-features-bedrooms']}
                    LEFT JOIN snapshot_attributes sbua ON sbua.asset_id = ps.asset_id AND sbua.attribute_definition_id = {$attributeDefinitions['property-features-build-up-area']}
                    LEFT JOIN attachment_assignments aa ON aa.object_id = ps.asset_id AND aa.object_type = 'snapshot'
                    LEFT JOIN attachments a ON a.id = aa.attachment_id
                WHERE l.id = :leadId
                GROUP BY l.id, ps.asset_id
                "), ['leadId' => $leadId]);

        return $snapshotSuggestions;
    }

    public function makeWarmExpiredHotLeads()
    {
        DB::statement("
            UPDATE
                leads l
            SET
                l.status = 'warm'
            WHERE l.id IN (
                SELECT
                    lal.id
                FROM
                    lead_assignments la
                    JOIN leads lal ON lal.id = la.lead_id
                WHERE
                    lal.status = 'hot'
                    AND DATE_ADD(la.created_at, INTERVAL 1 HOUR) < NOW()
            )
        ");
    }

    public function promote($request, $lead)
    {
        $input = $request->only([
            "type",
            "property_id",
            "unit_number",
            "price",
            "deposit",
            "commission_landlord",
            "commission_client",
            "start_date",
            "end_date",
            "status",
            "comments",
            "owner_invoice_no",
            "client_invoice_no",
            "shared_deals",
            "listing_agent_id",
            "referred_listing_agent_id",
            "closing_agent_id",
            "referred_closing_agent_id"
        ]);
        $deal = new Deal();
        $deal->fill($input);

        $property = Property::with('theTower', 'location')->find($request->input("property_id"));
        $propertySnapshot = PropertySnapshot::with('theTower', 'location')->where('listing_id', $request->input("property_id"))->first();
        $deal->owner_id = $property->contact_id;
        $deal->client_id = $lead->contact_id;
        $deal->lead_id = $lead->id;
        $deal->created_by = Auth::user()->id;
        $deal->deal_status = LeadStatus::STATUS_PENDING;

        $deal->save();

        // Generate invoices for landlord and client
        $this->generateInvoicesForDeal($deal);

        $this->copyLeadFormsToDealForms($lead, $deal);

        if (!is_null($request->agent_id_client)) {
            $dealAgent = new DealAgent();
            $dealAgent->deal_id = $deal->id;
            $dealAgent->agent_id = $request->agent_id_client;
            $dealAgent->agent_type = 'client';
            $dealAgent->save();
        }
        if (!is_null($request->agent_id_landlord)) {
            $dealAgent = new DealAgent();
            $dealAgent->deal_id = $deal->id;
            $dealAgent->agent_id = $request->agent_id_landlord;
            $dealAgent->agent_type = 'landlord';
            $dealAgent->save();
        }

        $this->emailService->sendEmailToOMForDealApproval($deal);
        $this->operationHistoryService->addOperationHistory($lead->contact, 'Deal [' . $this->dealsService->computeDealRefNo($deal->id) . '] was closed with status pending for listing [' . $property->ref_no . ']', auth()->user());
        $this->operationHistoryService->addOperationHistory($lead->contact, 'Deal was saved with status pending', auth()->user());

        if (isset($input['end_date']) && $input['end_date'] && $input['end_date'] != "") {
            //Create note end reminder for contract expiry
            $clientFullName
                = concatValues(
                    $lead->contact->first_name,
                    $lead->contact->last_name,
                    " "
                );

            // $noteData = [
            //     "type" => "NOTE",
            //     "title" => "Contract renewal"
            //         . ": "
            //         . $clientFullName,
            //     "text" => "Follow-up before contract expires",
            //     "status" => ""
            // ];
            // $note = $this->notesService->createNote($noteData);
            // $note->save();

            $expiryDate = new Carbon($input['end_date']);
            $reminder_date = new Carbon($input['end_date']);
            $reminder_date->subMonths(2)->toDateTimeString();

            $reminderData
                = [
                    "title" => "Contract renewal follow-up for " . $clientFullName,
                    "text" => "You should contact " . $clientFullName . ". His/her contract on " . $property->ref_no . " is about to expire on " . $expiryDate->toDateTimeString(),
                    "priority" => "MEDIUM",
                    "due_date" => $expiryDate,
                    "reminder_email_date" => $reminder_date,
                    "reminder_email" => "1",
                    "is_for_deal_end_date" => "1"
                ];

            $reminder = $this->notesService->createReminderWithObject($reminderData, $deal);
            // $this->notesService->attachNoteToObject($deal, $note, [$reminder]);
        }

        $action = LeadInteractionTracking::CREATE_DEAL;
        $this->authorizationService->leadInteractionsTracking($lead->id, $action, null);

        return $deal;
    }

    /**
     * Generate invoices for a deal (one for landlord and one for client)
     *
     * @param Deal $deal
     * @return void
     */
    private function generateInvoicesForDeal($deal)
    {
        try {
            // Generate client invoice
            if (!empty($deal->commission_client) || !empty($deal->client_invoice_no)) {
                $clientInvoice = new Invoice();
                $clientInvoice->deal_id = $deal->id;
                $clientInvoice->amount = $deal->commission_client;
                $clientInvoice->is_client = true;
                $clientInvoice->ref_no = $deal->client_invoice_no;
                $clientInvoice->issue_date = now();
                $clientInvoice->save();

                Log::info('Client invoice created for deal #' . $deal->id);
            }

            // Generate landlord invoice
            if (!empty($deal->commission_landlord) || !empty($deal->owner_invoice_no)) {
                $landlordInvoice = new Invoice();
                $landlordInvoice->deal_id = $deal->id;
                $landlordInvoice->amount = $deal->commission_landlord;
                $landlordInvoice->is_client = false;
                $landlordInvoice->ref_no = $deal->owner_invoice_no;
                $landlordInvoice->issue_date = now();
                $landlordInvoice->save();

                Log::info('Landlord invoice created for deal #' . $deal->id);
            }
        } catch (\Exception $e) {
            Log::error('Failed to generate invoices for deal #' . $deal->id . ': ' . $e->getMessage());
        }
    }

    public function copyLeadFormsToDealForms($lead, $deal)
    {
        if (!is_null($lead->forms) && is_array($lead->forms) && count($lead->forms)) {
            $dealForms = [];
            foreach ($lead->forms as $formMetadata) {
                $fileInfo = explode("/", $formMetadata->path);
                $copied = Storage::copy($formMetadata->path, 'deal-forms/' . $deal->id . '/' . $fileInfo[2]);
                if ($copied) {
                    $formMetadata->sentAt = null;
                    $dealForms[] = $formMetadata;
                } else {
                    Log::warn('Image not copied from ' . $formMetadata->path . ' to ' . 'deal-forms/' . $deal->id . '/' . $fileInfo[2]);
                }
            }
            $deal->forms = $dealForms;
            $deal->save();
        }
    }

    public function hold($request, $lead)
    {
        $reminder_date = $request->input("reminder_date");
        $note_text = $request->input("note_text");

        $clientFullName
            = concatValues(
                $lead->contact->first_name,
                $lead->contact->last_name,
                " "
            );

        $noteData = [
            "type" => "NOTE",
            "title" => "Pipeline freeze until"
                . $reminder_date
                . ": "
                . $clientFullName,
            "text" => $note_text,
            "status" => "On Hold"
        ];
        $note = $this->notesService->createNote($noteData);
        $note->save();

        $reminderData
            = [
                "title" => "Lead pipeline freeze follow-up " . $clientFullName,
                "text" => "You should contact " . $clientFullName,
                "priority" => "HIGH",
                "due_date" => $reminder_date,
                "send_email" => "1"
            ];

        $reminder = $this->notesService->createReminder($reminderData);

        $this->notesService->attachNoteToObject($lead, $note, [$reminder]);

        $lead->state = Lead::HOLD;
        $lead->save();

        return true;
    }

    public function discard($request, $lead)
    {
        $lead->state = Lead::REJECTED;
        $lead->save();

        $lead->proposals->each(function ($proposal) {
            LeadProposal::find($proposal->id)->delete();
        });

        $this->notesService->deleteObjectNotesAndReminders($lead);

        $assignment = LeadAssignment::where("lead_id", $lead->id)->first();
        if ($assignment) {
            $assignment->delete();
        }

        $lead->delete();

        return true;
    }

    public function onAssignment($leadAssignment)
    {
        $lead = $leadAssignment->lead;
        $user = $leadAssignment->user;
        $contact = $lead->contact;
        if (!empty($contact)) {
            // Log::info('Contact not empty');
            $userIsContactCreator = !is_null($contact->owner) && $user->id == $contact->owner->id;
            if (!$userIsContactCreator) {
                // Log::info('User is not contact creator');
                $isContactConectedWithUser = $this->brokerLandlordsService->isLandlordConnectedWithUser($contact, $user);
                if (!$isContactConectedWithUser) {
                    // Log::info('Contact is not connected with user');
                    // create a new contact connection for the current user and add a history line
                    $this->brokerLandlordsService->connectUserWithLandlord($user, $contact);
                    $this->operationHistoryService->addOperationHistory($contact, "The contact has been shared to [" . $user->email . "] on lead assignment [Lead id: " . $lead->id . "]");
                    Log::info('Contact has been connected with user');
                }
            }
        } else {
            // Log::info('Contact empty');
        }
        if (env('FEATURE_MOBILE_NOTIFICATIONS_ENABLED')) {
            $mobileTokens = $user->notification_tokens;
            if (is_array($mobileTokens)) {
                $notificationTitle = "New lead assigned";
                $notificationBody = "The lead with ID {$lead->getRefNo()} has been assigned to you";
                $notificationPayload = ['leadId' => $lead->id, 'type' => 'LEAD_ASSIGNMENT'];
                $notification = Notification::create([
                    'title' => $notificationTitle,
                    'body' => $notificationBody,
                    'type' => "Mobile - Agent",
                    'payload' => $notificationPayload
                ]);
                ActiveNotification::create([
                    'notification_id' => $notification->id,
                    'user_id' => $user->id,
                    'no_of_tries' => 3,
                    'sent_at' => now()
                ]);
                foreach ($mobileTokens as $mobileAppToken) {
                    $pushNotificationTitle = "🚨 New Lead Assigned! 🏡";
                    $pushNotificationBody = "You’ve got a new lead [#{$lead->getRefNo()}] to assist with their real estate journey!
Tap here to view the details and start connecting. 📲";
                    $this->notificationsService->sendPushNotification($mobileAppToken, $pushNotificationTitle, $pushNotificationBody, ['leadId' => $lead->id]);
                }
            }
        }
        if ($this->emailService->emailSendActive) {
            // $lead = $leadAssignment->lead;
            // $user = $leadAssignment->user;
            $enquiryListing = null;
            $snapshotURL = "";
            $snapshot = null;
            $lead->load(['agentContact', 'agentContact.listing', 'location', 'propertyType']);
            if (!is_null($lead->agentContact) && !is_null($lead->agentContact->listing)) {
                $enquiryListing = $lead->agentContact->listing;
                $snapshot = $this->propertiesService->getSnapshot($enquiryListing->listing_id);
                $snapshotURL = MenuHelperService::createURLForSnapshot($snapshot);
            }

            $properties = [];
            if (!is_null($lead->leads_request)) {
                $obj = new RequestParamsMap();
                $leadRequestKeys =  get_object_vars(json_decode($lead->leads_request));
                foreach ($leadRequestKeys as $key => $value) {
                    if ($key === QueryParamsDef::LOCATION_DATA || $key === QueryParamsDef::AMENITIES) {
                        if (is_array($value)) {
                            $value = implode(',', $value);
                        } else {
                            $value = $value;
                        }
                    }
                    $obj->set($key, $value);
                }

                $properties = $this->inventoryService->getTableItemsNew($obj);
            }

            try {
                if ($leadAssignment->for_reassignment) {
                    $this->emailService->sendEmailOnLeadReassign($lead, $user);
                } else {
                      Mail::send(
                        'crm.emails.lead-assignment',
                        [
                            'lead' => $lead,
                            'user' => $user,
                            'enquiredSnapshot' => $snapshot,
                            'snapshotURL' => $snapshotURL,
                            'properties' => $properties
                        ],
                        function ($m) use ($lead, $user) {
                            $m->from('<EMAIL>', 'FG Realty CRM');
                            $m->to($user->email, $user->name)->subject('FGREALTY CRM - New Lead Assignment');
                            $m->bcc('<EMAIL>', 'CSR');
                        }
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send lead assignment email: ' . $e->getMessage());
            }
        }
    }

    public function computeLeadRefNo($leadId)
    {
        return sprintf('L-%06d', $leadId);
    }

    public function moveLeadsToUser($fromUserId, $toUserId)
    {
        DB::table('lead_assignments')
            ->where('user_id', $fromUserId)
            ->update(['user_id' => $toUserId]);
        DB::table('leads')
            ->where('created_by', $fromUserId)
            ->update(['created_by' => $toUserId]);
        DB::table('lead_activity')
            ->where('created_by', $fromUserId)
            ->update(['created_by' => $toUserId]);
    }

    public function getCachedSources()
    {
        return Cache::remember(CacheKeys::LEAD_SOURCES, Timings::MONTH, function () {
            return LeadSource::get();
        });
    }

    public function getLeadRatingsList()
    {
        return ['', 'A', 'B', 'C', 'D', 'E', 'F', 'X'];
    }

    public function assignLeadToUser($lead, $user, $forReassignment = false)
    {
        // delete all the leads assignments
        LeadAssignment::where('lead_id', $lead->id)->delete();
        LeadAssignment::create([
            'user_id' => $user->id,
            'lead_id' => $lead->id,
            'for_reassignment' => $forReassignment
        ]);
    }

    public function getJMJLeadsCollection()
    {
        $leads = Lead::with(['leadSource', 'contact'])
            ->where(function ($qb) {
                $qb->whereHas('leadSource',  function ($qb) {
                    $qb->where('name', 'JMJ_enquiry_form');
                })
                    ->orWhere('inquired_ref_no', 'LIKE', '%AS-001018-2863%')
                    ->orWhere('inquired_ref_no', 'LIKE', '%AS-001016-2863%')
                    ->orWhere('inquired_ref_no', 'LIKE', '%AS-001017-2863%')
                    ->orWhere('inquired_ref_no', 'LIKE', '%AS-001020-2863%');
            })
            // ->where('created_at', '>', '2024-05-01 00:00:00')
            ->get();

        return $leads;
    }

    public function getFgrealtyDatabase($reportType = '')
    {
        $q = Lead::with(['contact', 'leadSource'])
            ->whereRaw('created_at >= DATE_SUB(NOW(), INTERVAL 3 YEAR)')
            ->has('contact')
            ->orderBy('created_at', 'DESC')
            ->whereNull('deleted_at');

        if ($reportType == 'budget') {
            $q->whereRaw("
                ((budget IS NOT NULL AND budget > 15000)
                OR (budget_min IS NOT NULL AND budget_min > 15000)
                OR (filter_budget_min IS NOT NULL AND filter_budget_min > 15000))
            ");
        }

        $q
            ->limit(20000);

        return $q->get();
    }

    public function trackLeadStatusChange($lead, $nextLeadStatus)
    {
        $agent = auth()->user();
        $leadXStatus = new LeadXStatus();
        $leadXStatus->lead_id = $lead->id;

        if (!is_null($lead->lead_status_id)) {
            $leadXStatus->lead_initial_status_id = $lead->lead_status_id;
        }

        if (!is_null($nextLeadStatus)) {
            $leadXStatus->lead_final_status_id = $nextLeadStatus->id;
        }

        $leadXStatus->created_by = $agent->id;
        $leadXStatus->save();
    }

    /**
     * this function is executed by the LeadObserver
     */
    public function handleLeadStatusOrRatingChange(Lead $lead)
    {
        if ($lead->isDirty('lead_status_id') || $lead->isDirty('rating')) {

            // delete whatever tasks are scheduled
            LeadFollowupTask::where('lead_id', $lead->id)->delete();
            Log::info('Deleted followup tasks for lead_id: ' . $lead->id);

            $newLeadStatusId = $lead->lead_status_id;

            // $originalRating = $lead->getOriginal('rating');
            $newRating = $lead->rating;
            $statusesMap = $this->leadStatusService->getLeadStatusesMap();

            $isNewStatusObservable = in_array($newLeadStatusId, [$statusesMap[LeadStatus::STATUS_CONTINUING_DISCUSSION], $statusesMap[LeadStatus::STATUS_OFFER_SENT], $statusesMap[LeadStatus::STATUS_MEETING_SCHEDULED], $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED], $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED], $statusesMap[LeadStatus::STATUS_OFFER_NEGOTIATION], $statusesMap[LeadStatus::STATUS_CONTRACT_PENDING], $statusesMap[LeadStatus::STATUS_NEW_R]]);
            $isNewStatusInactive = in_array($newLeadStatusId, [$statusesMap[LeadStatus::STATUS_NOT_QUALIFIED], $statusesMap[LeadStatus::STATUS_NOT_ANSWERED]]);
            if ($isNewStatusInactive && $lead->rating != 'X') {
                $lead->rating = 'X';
                $lead->save();
                Log::info('Lead rating changed automatically to X');
                return;
            }
            $isNewRatingObservable = in_array($newRating, ['A', 'B', 'C', 'D', 'E', 'F']);

            if ($isNewStatusObservable && $isNewRatingObservable) {
                $windows = $this->getCachedLeadFollowupTaskWindows();
                $daysMap = [
                    'A' => $windows['LEAD_A_FOLLOWUP_TASK_WINDOW'],
                    'B' => $windows['LEAD_B_FOLLOWUP_TASK_WINDOW'],
                    'C' => $windows['LEAD_C_FOLLOWUP_TASK_WINDOW'],
                    'D' => $windows['LEAD_D_FOLLOWUP_TASK_WINDOW'],
                    'E' => $windows['LEAD_E_FOLLOWUP_TASK_WINDOW'],
                    'F' => $windows['LEAD_F_FOLLOWUP_TASK_WINDOW']
                ];

                $dueSeconds = $daysMap[$newRating];

                $dueDate = Carbon::now()->addSeconds($dueSeconds);
                $followupTaskData = [
                    'lead_id' => $lead->id,
                    'scheduled_date' => $dueDate,
                    'time_interval' => $dueSeconds
                ];

                LeadFollowupTask::create($followupTaskData);
                Log::info('Created followup task for lead id / new status id / new rating / due days ' . $lead->id . " - " . $newLeadStatusId . " - " . $newRating . " - " . $dueSeconds);
            }
        }
    }

    public function createLeadFromGoogleLead(\App\Models\GoogleLead $googleLead)
    {
        $email = null;
        $phoneNumber = null;
        $fullName = null;

        foreach ($googleLead->user_column_data as $column) {
            if ($column['column_id'] === 'EMAIL') {
                $email = $column['string_value'];
            }
            if ($column['column_id'] === 'PHONE_NUMBER') {
                $phoneNumber = $column['string_value'];
            }
            if ($column['column_id'] === 'FULL_NAME') {
                $fullName = $column['string_value'];
            }
        }

        $existingContactByEmail = null;
        $existingContactByPhone = null;
        $existingContact = null;
        if (!is_null($email)) {
            $existingContactByEmail = $this->contactsService->getContactsByEmail($email);
            $existingMasterContact = $existingContactByEmail->whereNull('master_contact_id')->first();
            if (is_null($existingMasterContact)) {
                $existingContact = $existingContactByEmail->first();
            } else {
                $existingContact = $existingMasterContact;
            }
        }

        if (!is_null($phoneNumber)) {
            $existingContactByPhone = $this->contactsService->getContactsByPhoneNumber($phoneNumber);
            if (is_null($existingContact)) {
                $existingMasterContact = $existingContactByPhone->whereNull('master_contact_id')->first();
                if (is_null($existingMasterContact)) {
                    $existingContact = $existingContactByPhone->first();
                } else {
                    $existingContact = $existingMasterContact;
                }
            }
        }

        $leadStatusNew = LeadStatus::firstWhere('name', 'NEW');
        $leadSource = LeadSource::firstWhere('name', 'Google Lead');
        $broker = User::firstWhere('email', env('EMAIL_LOUAY'));
        $tagLabel = 'Google Leads Form';
        switch ($googleLead->lead_type) {
            case 'jmj':
                $tagLabel = 'Google Leads Form JMJ';
                break;
            case 'seef':
                $tagLabel = 'Google Leads Form SEEF';
                break;
            case 'blossom':
                $tagLabel = 'Google Leads Form BLOSSOM';
                break;
        }
        $googleLeadsFormTag = ContactsListTag::firstWhere('label', $tagLabel);

        if (is_null($existingContact)) {
            $contact = Contact::create([
                'name' => $fullName ?? 'N/A',
                'email_1' => $email ?? '',
                'prefix_mobile_1' => '+974',
                'mobile_1' => $phoneNumber,
                'master_contact_id' => null,
                'is_master_contact' => true
            ]);

            $existingContact = $contact;
            Log::info('Contact created #' . $contact->id);
        }

        if (!is_null($googleLeadsFormTag)) {
            $existingTags = $existingContact->tags;
            $tagInList = false;
            foreach ($existingTags as $t) {
                if ($t->id == $googleLeadsFormTag->id) {
                    $tagInList = true;
                }
            }
            if (!$tagInList) {
                $existingContact->tags()->attach($googleLeadsFormTag);
                $existingContact->save();
                Log::info('Tag saved');
            }
        }

        $leadMetadata = [
            'form_id' => $googleLead->form_id,
            'campaign_id' => $googleLead->campaign_id,
            'gcl_id' => $googleLead->gcl_id,
            'adgroup_id' => $googleLead->adgroup_id,
            'creative_id' => $googleLead->creative_id,
            'google_lead_id' => $googleLead->id,
        ];

        $lead = Lead::create([
            'contact_id' => $existingContact->id,
            'platform_from' => is_null($leadSource) ? null : $leadSource->id,
            'lead_status_id' => $leadStatusNew->id,
            'requirements' => 'This lead is coming from Google',
            'lead_metadata' => $leadMetadata
        ]);

        Log::info('Lead created');

        LeadAssignment::create([
            'user_id' => $broker->id,
            'lead_id' => $lead->id,
        ]);
        Log::info('Assignment created');

        $historyMessage = "Lead created automatically starting from google lead #" . $googleLead->id;
        if (!is_null($leadSource)) {
            $historyMessage .= ". Source [" . $leadSource->name . "] ";
        }
        if (!is_null($leadMetadata)) {
            $historyMessage .= "\r\n Lead Metadata: " . json_encode($leadMetadata);
        }

        $this->operationHistoryService->addOperationHistory($lead, $historyMessage);
        Log::info('Google lead added in CRM.');
    }

    public function getCachedLeadFollowupTaskWindows()
    {
        Cache::forget(CacheKeys::LEAD_FOLLOWUP_TASK_WINDOW);
        return Cache::remember(CacheKeys::LEAD_FOLLOWUP_TASK_WINDOW, Timings::DAY, function () {
            return DB::table('settings')
                ->select(['name', 'value'])
                ->whereIn('name', ['LEAD_A_FOLLOWUP_TASK_WINDOW', 'LEAD_B_FOLLOWUP_TASK_WINDOW', 'LEAD_C_FOLLOWUP_TASK_WINDOW', 'LEAD_D_FOLLOWUP_TASK_WINDOW', 'LEAD_E_FOLLOWUP_TASK_WINDOW', 'LEAD_F_FOLLOWUP_TASK_WINDOW'])
                ->get()
                ->mapWithKeys(function ($item, $key) {
                    return [$item->name => $item->value];
                })->toArray();
        });
    }
}
