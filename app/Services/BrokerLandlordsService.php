<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\Crm\ContactAgentConnection;
use App\Models\User;
use App\Models\API\Tower;
use Illuminate\Http\Request;
use App\Models\Crm\RolesDef;

use DB;

class BrokerLandlordsService extends GenericService
{
    public $landlordCategory = [
        ['value' => '', 'label' => 'Please select'],
        ['value' => '1', 'label' => 'Corporate'],
        ['value' => '0', 'label' => 'Private']
    ];
    private $nationalities = [];
    protected function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $listTypePrivate = $request->has('lt') && $request->get('lt') == 'private';
        $listTypeCorporate = $request->has('lt') && $request->get('lt') == 'corporate';
        $listTypeTeam = $request->has('lt') && $request->get('lt') == 'team_list';
        $listTypeAll = !$listTypeCorporate && !$listTypePrivate;
        $userIsAdmin = auth()->user()->hasAnyRole([
            RolesDef::OFFICE_MANAGER,
            RolesDef::MASTER_BROKER
        ]);
        $userAuthor = auth()->user()->author;
        $userAuthorId = $userAuthor ? $userAuthor->id : null;
        $userIsMatrixAgentManager = auth()->user()->hasAnyRole([RolesDef::MATRIX_AGENT_MANAGER]);
        $userIsTeamLeader = auth()->user()->hasAnyRole([RolesDef::TEAM_LEADER]);
        $userIsMatrixAgent = auth()->user()->hasAnyRole([RolesDef::MATRIX_AGENT]);
        $userAuthorIsMatrixAgentManager = !is_null($userAuthor) && $userAuthor->hasAnyRole([RolesDef::MATRIX_AGENT_MANAGER]);
        $q = DB::table('contacts as c')
            ->join('contacts_landlords as cl', 'cl.contact_id', '=', 'c.id')
            ->leftJoin('nationalities as n', 'n.id', '=', 'c.nationality_id')
            ->leftJoin('contacts_agents_connection as cac', 'cac.contact_id', '=', 'c.id')
            ->leftJoin('contact_x_towers as cxt', 'cxt.contact_id', '=', 'cl.id')
            ->leftJoin('contact_x_towers as cxts', 'cxts.contact_id', '=', 'cl.id')
            ->leftJoin('towers as t', 'cxt.tower_id', '=', 't.id')
            ->leftJoin('towers as ts', 'cxts.tower_id', '=', 'ts.id')
            ->leftJoin('geography as g', 't.geography_id', '=', 'g.id')
            ->leftJoin('geography as pg', 'g.parent_id', '=', 'pg.id')
            ->leftJoin('users as author', 'author.id', '=', 'c.created_by')
            ->leftJoin('reminders as r', function ($qb) {
                $qb->on('c.id', '=', 'r.object_id')
                    ->where('r.object_type', '=', 'contacts');
            })
            ->where('c.contact_type', '=', 'landlord')
            ->whereNull('c.master_contact_id')
            ->whereNull('c.deleted_at')
            ->whereNotNull('c.record_no');

        if ($userIsMatrixAgentManager || $userIsMatrixAgent) {
            $q->join('users as managers', 'managers.id', '=', 'author.created_by');
            if ($userIsMatrixAgentManager) {
                $q->where(function ($qb) {
                    $qb->where('managers.id', auth()->user()->id)
                        ->orWhere('author.created_by', auth()->user()->id)
                        ->orWhere('author.id', auth()->user()->id);
                });
            } else {
                $q->where(function ($qb) use ($userAuthorId, $userAuthorIsMatrixAgentManager) {
                    $qb->where('c.created_by', '=', auth()->user()->id)->orWhere('cl.created_by', '=', auth()->user()->id);
                    if ($userAuthorIsMatrixAgentManager) {
                        $qb->orWhere(function ($sqb) use ($userAuthorId) {
                            $sqb->where('c.created_by', '=', $userAuthorId)
                                ->where('cl.is_corporate_landlord', 1);
                        });
                    }
                });
            }
        } else {
            $q->where(function ($sql) use ($userIsAdmin, $listTypePrivate) {
                if (!$userIsAdmin && $listTypePrivate) {
                    $sql->where('c.created_by', '=', auth()->user()->id)
                        ->orWhere('cl.created_by', '=', auth()->user()->id)
                        ->orWhere('cac.user_id', '=', auth()->user()->id);
                }
            });
        }

        $extraClauses = $this->getRequestParams($request);

        if (isset($extraClauses['q']) && !empty($extraClauses['q'])) {
            $term = "%" . $extraClauses['q'] . "%";
            $q->where(function ($query) use ($term) {
                $query->orWhere('c.id', 'LIKE', $term)
                    ->orWhere('c.qatar_id_no', 'LIKE', $term)
                    ->orWhere('c.record_no', 'LIKE', $term)
                    ->orWhere('c.name', 'LIKE', $term)
                    ->orWhere('c.email_1', 'LIKE', $term)
                    ->orWhere('c.email_2', 'LIKE', $term)
                    ->orWhere('c.phone_1', 'LIKE', $term)
                    ->orWhere('c.phone_2', 'LIKE', $term)
                    ->orWhere('c.mobile_1', 'LIKE', $term)
                    ->orWhere('c.mobile_2', 'LIKE', $term)
                    ->orWhere('author.name', 'LIKE', $term)
                    ->orWhere('c.tower', 'LIKE', $term)
                    ->orWhere('ts.name', 'LIKE', $term)
                    ->orWhere('g.name', 'LIKE', $term)
                    ->orWhere('pg.name', 'LIKE', $term)
                    ->orWhere('c.unit_number', 'LIKE', $term)
                    ->orWhere('cl.contact_person', 'LIKE', $term)
                    ->orWhere('cl.developer_name', 'LIKE', $term);
            });
        }

        if (isset($extraClauses['sort'])) {
            $q->orderBy($extraClauses['sort'], $extraClauses['dir'] ?? 'asc');
        } else {
            $q->orderBy('c.record_no', 'desc');
        }

        if ($listTypeCorporate) {
            $q->where(function ($qb) {
                $qb->where('cl.is_corporate_landlord', 1);
            });
        } else if ($listTypePrivate) {
            $q->where(function ($qb) {
                $qb->where('cl.is_corporate_landlord', 0);
                $qb->orWhereNull('cl.is_corporate_landlord');
            });
        } else if ($listTypeAll) {
            if ($userIsMatrixAgentManager || $userIsMatrixAgent) {
                // $q->where(function ($qb) use ($userIsAdmin) {
                //     if (!$userIsAdmin) {
                //         $qb->where('c.created_by', auth()->user()->id);
                //         $qb->orWhere('c.is_corporate_landlord', 1);
                //     }
                // });
            } else {

                if ($userIsTeamLeader && $listTypeTeam) {
                    $q->where(function ($qb) use ($userIsAdmin) {
                        $qb->where('c.created_by', auth()->user()->id)->orWhere('author.team_leader_id', auth()->user()->id);
                    });
                } else {
                    $q->where(function ($qb) use ($userIsAdmin) {
                        if (!$userIsAdmin) {
                            $qb->where('c.created_by', auth()->user()->id);
                            $qb->orWhere('cl.created_by', auth()->user()->id);
                            $qb->orWhere('cl.is_corporate_landlord', 1);
                        }
                    });
                }
            }
        }

        $q->select(
            'c.id as id',
            'c.qatar_id_no',
            'c.record_no',
            DB::raw('COALESCE(c.name, "") as fullname'),
            'c.email_1',
            'c.mobile_1',
            'c.mobile_2',
            'c.unit_number',
            'c.tower',
            'c.created_by',
            DB::raw('DATE_FORMAT(c.last_contact_date, "%Y-%m-%d") as last_contact_date'),
            'author.name as created_by_name',
            'n.id as nationality_id',
            'n.name as nationality',
            'cl.contact_person as contact_person',
            'cl.developer_name',
            'r.id as reminder',
            'c.contract_end_date',
            'cl.marketing_agreement_path',
            'cl.is_corporate_landlord as is_corporate_landlord',
            DB::raw('IF(!ISNULL(c.contract_end_date), DATE(c.contract_end_date) < NOW(), false) as is_contract_expired'),
            DB::raw('GROUP_CONCAT(DISTINCT CONCAT(t.name, "||", COALESCE(pg.name, "")," ", g.name) ORDER BY g.name, t.name ASC SEPARATOR ",<br />") as towers'),
            DB::raw('COALESCE(c.mobile_1, c.mobile_2, "") as mobile'),
            DB::raw('COALESCE(c.email_1, "") as email'),
        );

        $q->groupBy('c.id');

        return $q;
    }

    public function getNextLandlordNoForUser()
    {
        $q = DB::table('contacts')->select(
            DB::raw('MAX(record_no) as max_record_no')
        );
        $data = $q->first();

        $nextRecordNo = empty($data->max_record_no) ? env('LANDLORD_DEFAULT_NEXT_RECORD_NO', 850) : $data->max_record_no + 1;

        return $nextRecordNo;
    }

    public function getLandlordsForUser(User $user)
    {
        return Contact::with('nationality')
            ->where('created_by', $user->id)
            ->where('contact_type', 'landlord')
            ->paginate();
    }

    public function getLandlordByQidOrEmail($qid, $email)
    {

        if (empty($qid) && empty($email)) {
            return null;
        }

        $qb = Contact::with(['nationality']);

        if (!empty($qid)) {
            $qb->where('qatar_id_no', $qid);
        }

        if (!empty($email)) {
            if (!empty($qid)) {
                $qb->orWhere('email_1', $email);
            } else {
                $qb->where('email_1', $email);
            }
        }

        return $qb->first();
    }

    public function getLandlordByIdAndQatarIdNo($id, $qatarIdNo)
    {
        return Contact::where('id', $id)->where('qatar_id_no', $qatarIdNo)->first();
    }

    public function getLandlordById($landlordId)
    {
        return Contact::where('id', $landlordId)->first();
    }

    public function connectUserWithLandlord(User $user, Contact $landlord)
    {
        if (!$this->isLandlordConnectedWithUser($landlord, $user)) {
            ContactAgentConnection::create([
                'contact_id' => $landlord->id,
                'user_id' => $user->id,
            ]);
            // $landlord->agents()->attach($user);
            return true;
        }

        return false;
    }

    public function isLandlordConnectedWithUser(Contact $landlord, User $user)
    {
        $existingConnection = ContactAgentConnection::where('user_id', $user->id)->where('contact_id', $landlord->id)->first();
        return !is_null($existingConnection);
    }

    public function searchItemsForCurrentUser($request)
    {
        $items = [];
        if ($request->has('phone')) {
            $encodedStrings = explode("&", $request->getQueryString());
            foreach ($encodedStrings as $encodedStr) {
                if (strpos($encodedStr, "phone=") > -1) {
                    $q = rawurldecode(str_replace("%20", "+", str_replace("phone=", "", $encodedStr)));
                }
            }
            // modified this to be searchable not only by the fullname, but having the term as part of the name
            $term = empty(trim($q)) ? '-1000000' : "%" . $q . "%";

            $user = auth()->user();
            $userAuthor = $user->author;
            $userIsMatrixAgent = auth()->user()->hasRole(RolesDef::MATRIX_AGENT);
            $userIsMatrixAgentManager = auth()->user()->hasRole(RolesDef::MATRIX_AGENT_MANAGER);
            $userIsTeamLeader = auth()->user()->hasRole(RolesDef::TEAM_LEADER);

            $userAuthorIsMatrixAgentManager = !is_null($userAuthor) && $userAuthor->hasAnyRole([RolesDef::MATRIX_AGENT_MANAGER]);
            $qb = DB::table('contacts as c')
                ->leftJoin('contacts_agents_connection as cac', 'cac.contact_id', '=', 'c.id')
                ->leftJoin('nationalities as n', 'n.id', '=', 'c.nationality_id')
                ->join('contacts_landlords as cl', 'cl.contact_id', '=', 'c.id');

            if ($userIsMatrixAgent) {
                $qb->leftJoin('users as u', 'u.id', '=', 'c.created_by');
            } else if ($userIsMatrixAgentManager || $userIsTeamLeader) {
                $qb->leftJoin('users as u', 'u.id', '=', 'c.created_by');
            }

            if (!$user->hasAnyRole([
                RolesDef::OFFICE_MANAGER,
                RolesDef::MASTER_BROKER
            ])) {
                $qb->where(function ($sql) use ($user, $userIsMatrixAgent, $userIsMatrixAgentManager, $userAuthorIsMatrixAgentManager, $userIsTeamLeader) {
                    if ($userIsMatrixAgent || $userIsMatrixAgentManager || $userIsTeamLeader) {
                        $sql->where('c.created_by', $user->id)
                            ->orWhere('cac.user_id', $user->id);
                        if ($userIsMatrixAgent) {
                            if ($userAuthorIsMatrixAgentManager) {
                                $sql->orWhere(function ($sqb) use ($user) {
                                    $sqb->where('c.created_by', $user->created_by)
                                        ->where('cl.is_corporate_landlord', '1');
                                });
                                $sql->orWhere(function ($sqb) use ($user) {
                                    $sqb->where('u.created_by', $user->created_by)
                                        ->where('cl.is_corporate_landlord', '1');
                                });
                            }
                        } else {
                            $sql->orWhere('u.created_by', $user->id);
                            if (!$userIsMatrixAgentManager && $userIsTeamLeader) {
                                $sql->orWhere('cl.is_corporate_landlord', '1');
                            }
                        }
                    } else {
                        $sql->where('c.created_by', $user->id)
                            ->orWhere('cac.user_id', $user->id)
                            ->orWhere('cl.is_corporate_landlord', '1');
                    }
                });
            }

            $qb->where(function ($query) use ($term) {
                $query->orWhere('phone_1', 'LIKE', $term)
                    ->orWhere('phone_2', 'LIKE', $term)
                    ->orWhere('mobile_1', 'LIKE', $term)
                    ->orWhere('mobile_2', 'LIKE', $term)
                    ->orWhere('qatar_id_no', '=', $term)
                    ->orWhere('cl.record_no', 'LIKE', $term)
                    ->orWhere('email_1', 'LIKE', $term)
                    ->orWhere('c.name', 'LIKE', $term)
                    ->orWhere('c.record_no', 'LIKE', $term);
            });

            $qb->whereNull('c.master_contact_id');

            $qb->select([
                'c.id', 'c.name as fullname', 'c.email_1', 'c.company_name', 'cl.record_no', 'cl.developer_name', 'n.name',
            ]);
            $items = $qb->get()->toArray();
        }
        return $items;
    }

    public function importFromOldTable()
    {
        $dbNationalities = DB::table('nationalities')->get();

        foreach ($dbNationalities as $nat) {
            $this->nationalities[$nat->name] = $nat->id;
        }

        $lastSourceContactId = 0;
        $itemsPassed = 0;
        $itemsPerPage = 50;
        while ($lastSourceContactId < 14965) {
            $contacts = $this->getLiveConn()->table('contacts')->whereNull('deleted_at')->skip($itemsPassed)->take($itemsPerPage)->get();
            foreach ($contacts as $index => $contact) {
                $this->insertContact($contact);
                $lastSourceContactId++;
            }
            $itemsPassed += $itemsPerPage;
        }
    }

    private function insertContact($dbContact)
    {
        $nextArr = (array)$dbContact;
        $nationalityStr = $dbContact->nationality;
        unset($nextArr['nationality']);
        if (isset($this->nationalities[$nationalityStr])) {
            $nextArr['nationality_id'] = $this->nationalities[$nationalityStr];
        }
        Contact::create($nextArr);
    }

    private function getLiveConn()
    {
        return DB::connection('mysql_live');
    }

    function getTowers()
    {
        return Tower::with(['geography'])->get();
    }
}
