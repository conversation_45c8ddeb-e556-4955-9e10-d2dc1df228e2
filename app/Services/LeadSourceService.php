<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LeadSourceService extends GenericService
{
    public function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $extraClause = $this->getRequestParams($request);

        $q = DB::table('lead_sources as ls')
            ->leftJoin('leads as l', 'l.platform_from', '=', 'ls.id')
            ->leftJoin('marketing__campaigns as mc', 'ls.marketing_campaign_id', '=', 'mc.id')
            ->leftJoin('marketing__projects as mp', 'mc.marketing_project_id', '=', 'mp.id');

        if (!empty($extraClause['sort'])) {
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($extraClause['sort'], $extraClause['dir']);
            } else {
                $q = $q->orderBy($extraClause['sort']);
            }
        } else {
            $q = $q->orderBy('ls.name', 'desc');
        }

        if (!empty($extraClause['q'])) {
            $q->where(function ($sql) use ($extraClause) {
                $term = '%'.trim($extraClause['q']) . '%';
                $sql->where('ls.name', 'LIKE', $term);
            });
        }

        $q->select([
            DB::raw('COUNT(l.id) as leads_no'),
            'ls.id',
            'ls.name',
            'ls.marketing_campaign_id',
            'mc.name as campaign_name',
            'mp.name as project_name'
        ]);
        $q->whereNull('l.deleted_at');
        $q->groupBy('ls.id');

        return $q;
    }
}
