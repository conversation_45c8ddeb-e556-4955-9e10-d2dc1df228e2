<?php

namespace App\Services;

use Illuminate\Http\Request;
use App\Models\Invoice;
use DB;

class AccountantService
{
    public $ajaxListMapper;

    public $paymentMethods = [
        '' => 'Please Select',
        'cash' => 'Cash',
        'cheque' => 'Cheque',
        'credit_card' => 'Credit Card',
        'debit_card' => 'Debit Card',
        'direct_debit' => 'Direct Debit',
        'etf' => 'ETF',
        'pos' => 'POS',
        'set_off' => 'SET OFF',
        'bank_transfer' => 'Bank Transfer',
        'other' => 'other',
    ];

    public function __construct()
    {
        $this->ajaxListMapper = function ($item) {

            $data_item = [];

            $actions = [];
            $actions['update'] = route('crm.deals.edit', ['id' => $item->id]);

            $data_item['deal_payment_status'] = [];
            $data_item['DT_RowId'] = $item->id;
            $data_item[9] = ""; //Manage
            $data_item[8] = $actions; //Actions
            $data_item['unit_number'] = $item->unit_number;
            $data_item['created_at'] = $item->created_at;
            $data_item['commission_landlord'] = number_format($item->commission_landlord, 0, ".", ",");
            $data_item['commission_client'] = number_format($item->commission_client, 0, ".", ",");
            $data_item['closing_agent_id'] = $item->closing_agent_id;
            $data_item['listing_agent_id'] = $item->listing_agent_id;
            $dealId = null;
            if (!empty($item->id)) {
                $dealId = $item->id;
            }
            $data_item['deal_ref_no'] = ["display" => $item->ref_no, "url" => is_null($dealId) ? "" : route("crm.deals.edit", ['id' => $dealId])];
            $data_item['landlord'] = ["name" => $item->owner_name, "invoice" => !is_null($item->landlord_invoice_no) ? $item->landlord_invoice_no : "No Invoice"];
            $data_item['client'] = ["name" => $item->client_name, "invoice" => !is_null($item->client_invoice_no) ? $item->client_invoice_no : "No Invoice"];
            $data_item['landlord_invoice_id'] = $item->landlord_invoice_id;
            $data_item['client_invoice_id'] = $item->client_invoice_id;
            $data_item['lead_source'] = $item->lead_source;
            $data_item['tower_name'] = $item->tower_name;
            $data_item['deal_payment_status'][0] = $item->landlord_commission_cashed;
            $data_item['deal_payment_status'][1] = $item->client_commission_cashed;

            $invoices = Invoice::with(['payments'])->where('deal_id', $item->id)->get();

            $landlordInvoice = $invoices
                ->where('is_client', 0)
                ->first();

            $landlordInvoiceAmount = 0;
            $landlordPayments = collect([]);
            $landlordInvoiceNo = $item->landlord_invoice_no;

            if ($landlordInvoice) {
                $landlordInvoiceAmount = (float)($landlordInvoice->amount ?? 0);
                $landlordPayments = $landlordInvoice->payments ?? collect([]);
                $landlordInvoiceNo = $landlordInvoice->ref_no ?? '';
            }

            $landlordPaidAmount = 0;
            $landlordCashedInAmount = 0;
            $landlordNotCashedInAmount = 0;
            foreach ($landlordPayments as $payment) {
                $paymentAmount = (float)($payment->amount ?? 0);
                $landlordPaidAmount += $paymentAmount;

                if ($payment->cashed_in) {
                    $landlordCashedInAmount += $paymentAmount;
                } else {
                    $landlordNotCashedInAmount += $paymentAmount;
                }
            }

            $data_item['landlord_invoice_amount'] = $landlordInvoiceAmount;
            $data_item['landlord_paid_amount'] = $landlordPaidAmount;
            $data_item['landlord_cashed_in_amount'] = $landlordCashedInAmount;
            $data_item['landlord_not_cashed_in_amount'] = $landlordNotCashedInAmount;

            $clientInvoice = $invoices
                ->where('is_client', 1)
                ->first();

            $clientInvoiceAmount = 0;
            $clientPayments = collect([]);
            $clientInvoiceNo = $item->client_invoice_no;

            if ($clientInvoice) {
                $clientInvoiceAmount = (float)($clientInvoice->amount ?? 0);
                $clientPayments = $clientInvoice->payments;
                $clientInvoiceNo = $clientInvoice->ref_no ?? '';
            }

            $clientPaidAmount = 0;
            $clientCashedInAmount = 0;
            $clientNotCashedInAmount = 0;
            foreach ($clientPayments as $payment) {
                $paymentAmount = (float)($payment->amount ?? 0);
                $clientPaidAmount += $paymentAmount;

                if ($payment->cashed_in) {
                    $clientCashedInAmount += $paymentAmount;
                } else {
                    $clientNotCashedInAmount += $paymentAmount;
                }
            }

            $data_item['client_invoice_amount'] = $clientInvoiceAmount;
            $data_item['client_paid_amount'] = $clientPaidAmount;
            $data_item['client_cashed_in_amount'] = $clientCashedInAmount;
            $data_item['client_not_cashed_in_amount'] = $clientNotCashedInAmount;
            $data_item['landlord']['invoice'] = $landlordInvoiceNo;
            $data_item['client']['invoice'] = $clientInvoiceNo;

            return $data_item;
        };
    }

    public function fetchItems($extraConfig = [], $offset = 0, $limit = 10)
    {
        $items = [];
        $q = DB::table('deals as d')
            ->leftJoin('contacts as o', 'd.owner_id', '=', 'o.id')
            ->leftJoin('contacts as c', 'd.client_id', '=', 'c.id')
            ->leftJoin('users as ua', 'd.closing_agent_id', '=', 'ua.id')
            ->leftJoin('users as ul', 'd.listing_agent_id', '=', 'ul.id')
            ->leftJoin('leads as l', 'd.lead_id', '=', 'l.id')
            ->leftJoin('lead_sources as ls', 'l.platform_from', '=', 'ls.id')
            ->leftJoin('properties as p', 'd.property_id', '=', 'p.id')
            ->leftJoin('invoices as li', function($subquery) {
                $subquery
                    ->on('d.id', '=', 'li.deal_id')
                    ->where('li.is_client', 0);
            })
            ->leftJoin('invoices as ci', function($subquery) {
                $subquery
                    ->on('d.id', '=', 'ci.deal_id')
                    ->where('ci.is_client', 1);
            })
            ->leftJoin('towers as t', 'p.tower_id', '=', 't.id')
            ->whereNull('d.deleted_at')
            ->select(
                'd.id',
                'd.created_at',
                'd.unit_number',
                'ci.ref_no as client_invoice_no',
                'li.ref_no as landlord_invoice_no',
                'ci.id as client_invoice_id',
                'li.id as landlord_invoice_id',
                'li.amount as commission_landlord',
                'ci.amount as commission_client',
                'd.landlord_commission_cashed',
                'd.client_commission_cashed',
                'ua.name as closing_agent_id',
                'ul.name as listing_agent_id',
                'o.name as owner_name',
                'c.name as client_name',
                'ls.name as lead_source',
                't.name as tower_name',
            )
            ->orderBy('d.id', 'desc');

        $clauses = [];
        if (!empty($extraConfig['wheres']) && is_array($extraConfig['wheres']) && count($extraConfig['wheres']) > 0) {
            foreach ($extraConfig['wheres'] as $k => $filterVal) {
                $clauses[] = [$k, 'LIKE', $filterVal . '%'];
            }
        }

        if (count($clauses) > 0) {
            $q = $q->where($clauses);
        }

        if (!empty($extraConfig['q'])) {
            $kw = $extraConfig['q'];
            $q = $q->where(function ($sql) use ($kw) {
                $fields = [
                    'd.id',
                    'd.unit_number',
                    'd.client_invoice_no',
                    'd.owner_invoice_no',
                    'd.commission_landlord',
                    'd.commission_client',
                    DB::raw("CONCAT('D-', LPAD(d.id, 6, '0'))"),
                    'o.name',
                    'c.name',
                    'ua.name',
                    'ul.name',
                    'p.ref_no',
                    't.name',
                    'ls.name'
                ];

                $sql->where($fields[0], 'LIKE', '%' . $kw . '%');

                for ($i = 1; $i < count($fields); $i++) {
                    $sql->orWhere($fields[$i], 'LIKE', '%' . $kw . '%');
                }
            });
        }

        if (isset($extraConfig['ids']) && is_array($extraConfig['ids']) && !empty($extraConfig['ids'])) {
            $q->whereIn('d.id', $extraConfig['ids']);
        }

        if (isset($extraConfig['searchParams'])) {
            if (isset($extraConfig['searchParams']['dealType'])) {
                $q->where('d.type', $extraConfig['searchParams']['dealType']);
            }
            if (isset($extraConfig['searchParams']['agentId'])) {
                $q->where('d.created_by', $extraConfig['searchParams']['agentId']);
            }
            if (isset($extraConfig['searchParams']['teamLeaderId'])) {
                $teamLeaderId = $extraConfig['searchParams']['teamLeaderId'];
                $q->where(function ($query) use ($teamLeaderId) {
                    $query->where('d.created_by', $teamLeaderId)
                        ->orWhereIn('d.created_by', function ($subquery) use ($teamLeaderId) {
                            $subquery->select('id')
                                ->from('users')
                                ->where('team_leader_id', $teamLeaderId);
                        });
                });
            }
            if (isset($extraConfig['searchParams']['createdAtFrom'])) {
                $q->whereRaw("d.created_at >= ? ", [$extraConfig['searchParams']['createdAtFrom'] . " 00:00:00"]);
            }
            if (isset($extraConfig['searchParams']['createdAtTo'])) {
                $q->whereRaw("d.created_at <= ? ", [$extraConfig['searchParams']['createdAtTo'] . " 23:59:59"]);
            }
            if (isset($extraConfig['searchParams']['dealEndYear'])) {
                if (isset($extraConfig['searchParams']['dealEndMonth'])) {
                    $q->whereRaw('DATE_FORMAT(d.end_date, "%Y-%m") LIKE "' . ($extraConfig['searchParams']['dealEndYear'] . '-' . $extraConfig['searchParams']['dealEndMonth']) . '"');
                } else {
                    $q->whereRaw('DATE_FORMAT(d.end_date, "%Y") LIKE "' . ($extraConfig['searchParams']['dealEndYear']) . '"');
                }
            }
            if (isset($extraConfig['searchParams']['refNo'])) {
                $q->where('p.ref_no', $extraConfig['searchParams']['refNo']);
            }
            if (isset($extraConfig['searchParams']['leadSource'])) {
                if (is_array($extraConfig['searchParams']['leadSource'])) {
                    $leadSources = array_map(function ($source) {
                        return "'" . addslashes($source) . "'";
                    }, $extraConfig['searchParams']['leadSource']);

                    $q->whereRaw("ls.name IN (" . implode(",", $leadSources) . ")");
                } else {
                    $q->whereRaw("ls.name LIKE '" . addslashes($extraConfig['searchParams']['leadSource']) . "'");
                }
            }
        }

        $count = $q->count();

        if ($limit != -1) {
            $q = $q->offset($offset);
            $q = $q->limit($limit);
        }

        $items = $q->get();
        $items = ['items' => $items, 'count' => $count];

        foreach ($items['items'] as $item) {
            $refNo = sprintf('D-%06d', $item->id);
            $item->ref_no = $refNo;
        }

        return $items;
    }

    public function getExtraConfig(Request $request)
    {
        $columns = $request->query('columns');
        $order = $request->query('order');
        $search = $request->query('search');

        $extraConfig = ['q' => ''];

        if (!empty($columns) && is_array($columns)) {
            //build the where clauses
            $wheres = [];
            foreach ($columns as $col) {
                if ($col['searchable'] && isset($col['search']) && isset($col['search']['value']) && !empty($col['search']['value'])) {
                    $wheres[$col['name']] = $col['search']['value'];
                }
            }

            $extraConfig['wheres'] = $wheres;
        }

        if (!empty($order) && is_array($order) && !empty($order[0])) {
            if (!empty($columns) && is_array($columns) && !empty($columns[$order[0]['column']])) {
                $sortBy = $columns[$order[0]['column']]['name'];
                $extraConfig['sort'] = $sortBy;
                $extraConfig['dir'] = $order[0]['dir'];
            }
        }

        if (!empty($search) && is_array($search) && !empty($search['value'])) {
            $extraConfig['q'] = $search['value'];
        }

        $vt = $request->get('vt', 'default');
        if (!in_array($vt, ['default', 'team_list', 'pending', 'approved', 'finalized'])) {
            $vt = 'default';
        }

        $extraConfig['vt'] = $vt;
        $extraConfig['searchParams'] = [];

        $type = $request->get('dealType', null);
        $agentId = $request->get('agentId', null);
        $teamLeaderId = $request->get('teamLeaderId', null);
        $createdAtFrom = $request->get('createdAtFrom', null);
        $createdAtTo = $request->get('createdAtTo', null);
        $dealEndMonth = $request->get('dealEndMonth', null);
        $dealEndYear = $request->get('dealEndYear', null);
        $refNo = $request->get('refNo', null);
        $leadSource = $request->get('leadSource', null);

        if (!is_null($type)) {
            $extraConfig['searchParams']['dealType'] = $type;
        }

        if (!is_null($agentId)) {
            $extraConfig['searchParams']['agentId'] = $agentId;
        }

        if (!is_null($teamLeaderId)) {
            $extraConfig['searchParams']['teamLeaderId'] = $teamLeaderId;
        }

        if (!is_null($createdAtFrom)) {
            $extraConfig['searchParams']['createdAtFrom'] = $createdAtFrom;
        }

        if (!is_null($createdAtTo)) {
            $extraConfig['searchParams']['createdAtTo'] = $createdAtTo;
        }

        if (!is_null($dealEndMonth)) {
            $extraConfig['searchParams']['dealEndMonth'] = $dealEndMonth;
        }

        if (!is_null($dealEndYear)) {
            $extraConfig['searchParams']['dealEndYear'] = $dealEndYear;
        }

        if (!is_null($refNo)) {
            $extraConfig['searchParams']['refNo'] = $refNo;
        }
        if (!is_null($leadSource)) {
            if (is_array($leadSource) && count($leadSource) > 0) {
                $extraConfig['searchParams']['leadSource'] = $leadSource;
            } else if (!is_array($leadSource)) {
                $extraConfig['searchParams']['leadSource'] = [$leadSource];
            }
        }

        return $extraConfig;
    }
}
