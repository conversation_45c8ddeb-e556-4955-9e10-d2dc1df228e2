<?php

namespace App\Services;

use App\Models\Crm\Deal;
use App\Models\Crm\PermissionsDef;
use App\Models\Crm\RolesDef;
use Illuminate\Http\Request;
use DB;
use Carbon\Carbon;

class DealsService
{
    public $ajaxListMapper;
    public function __construct(AuthorizationService $authoServ)
    {
        $this->ajaxListMapper = function ($item) use ($authoServ) {
            $data_item = [];
            $user = auth()->user();
            $allowedActions = $authoServ->getAllowedActions(AuthorizationService::CATEG_DEALS);
            $userHasDealsReadAllPermission = auth()->user()->hasPermissionTo(PermissionsDef::DEALS_READ_ALL);
            $isAccountant = $user->hasRole(RolesDef::ACCOUNTANT);
            $userIsTheTeamLeaderOfDealAgent = $user->id == $item->team_leader_id;
            $userCreatedByTeamLeader = $item->has_team_leader;
            $isAdmin = $userHasDealsReadAllPermission || $user->hasRole(RolesDef::TL_DEALS_APPROVER) || $userIsTheTeamLeaderOfDealAgent;
            $isAdminEdit = $user->hasRole(RolesDef::OFFICE_MANAGER) || auth()->user()->hasPermissionTo(PermissionsDef::HAS_ACCESS_TO_DEAL_FILES);
            $userIsOwnerOrClosingAgent = $user->id == $item->created_by || $user->id == $item->closing_agent_id;
            // $isAdminSerbanAmira = in_array(auth()->user()->email, ["<EMAIL>", "<EMAIL>"]);

            $refNo = "";
            $assetId = null; 
            if (!empty($item->property_ref_no)) {
                $refNo = $item->property_ref_no;
                $assetId = $item->property_asset_id;
            }
            $data_item['deal_ref_no'] = $item->ref_no;
            $data_item['ref_no'] = ["display" => $refNo, "url" => is_null($assetId) ? "" : route("inventory.edit", ['id' => $assetId])];
            $data_item['unit_number'] = $item->unit_number;

            if (!empty($item->client_id)) {
                $data_item['client'] = [
                    "display" => $item->name,
                    "url" => '#' //route("contact-read", ['id' => $item->client_id])
                ];
            } else {
                $data_item['client'] = ["display" => "N/A;", "url" => "#"];
            }

            // this should be the landlord of the contracted listing -
            if (!empty($item->owner_id) && !empty($item->property_landlord_id)) {
                $data_item['landlord'] = [
                    "display" => $item->property_landlord_name,
                    "url" => route("broker-landlords.edit", ['id' => $item->property_landlord_id])
                ];
            } else {
                $data_item['landlord'] = ["display" => "N/A;", "url" => "#"];
            }

            $data_item['price'] = $item->price;

            $data_item['start_date'] = $item->start_date;
            $data_item['end_date'] = $item->end_date;

            $actions = [];
            if ($userIsOwnerOrClosingAgent) {
                if (!is_array($allowedActions)) {
                    $allowedActions = [];
                }
                $allowedActions['read'] = true;
                $allowedActions['update'] = true;
            }
            if ($allowedActions['read']) {
                $actions['read'] = route('crm.deals.read', ['id' => $item->id]);
            }
            if ($allowedActions['update']) {
                $actions['update'] = route('crm.deals.edit', ['id' => $item->id]);
            }
            if ($allowedActions['delete']) {
                $actions['delete'] = route('crm.deals.delete', ['id' => $item->id]);
            }

            $data_item['deal_payment_status'] = [];
            $data_item[8] = $actions; //Actions
            $data_item[9] = ""; //Manage
            $data_item['deal_status'] = $item->deal_status;
            $data_item['creator_name'] = $item->creator_name;
            $data_item['DT_RowId'] = $item->id;
            $data_item['can_approve'] = $isAdmin;
            $data_item['can_update_deal_status'] = $userIsTheTeamLeaderOfDealAgent || $isAccountant || $userCreatedByTeamLeader;
            $data_item['deal_status'] = $item->deal_status;
            $data_item['deal_payment_status'][0] = $item->landlord_commission_cashed;
            $data_item['deal_payment_status'][1] = $item->client_commission_cashed;
            $data_item['reminder'] = $item->reminder_id;
            $data_item['team_leader_id'] = $item->team_leader_id;
            $data_item['can_edit'] = $isAdminEdit || $isAccountant || $userIsOwnerOrClosingAgent;
            // $data_item['dealDocumentURL'] = $item->deal_status == Deal::STATUS_FINALIZED ? route('deal.pdf.generateDealPDF', ['id' => $item->id]) : '';
            return $data_item;
        };
    }

    public function fetchItems($extraConfig = [], $offset = 0, $limit = 10)
    {
        $user = auth()->user();
        $items = [];
        $userHasDealsReadAllPermission = $user->hasPermissionTo(PermissionsDef::DEALS_READ_ALL);
        if ($user->hasPermissionTo(PermissionsDef::DEALS_READ) || $userHasDealsReadAllPermission) {
            $q = DB::table('deals as d')
                ->leftJoin('contacts as c', 'd.client_id', '=', 'c.id')
                ->leftJoin('contacts as o', 'd.owner_id', '=', 'o.id')
                ->leftJoin('properties as p', 'd.property_id', '=', 'p.id')
                ->leftJoin('contacts as pc', 'p.contact_id', '=', 'pc.id')
                ->leftJoin('deals_agents as da', 'd.id', '=', 'da.deal_id')
                ->leftJoin('users as u', 'd.created_by', '=', 'u.id')
                ->leftJoin('users as ca', 'd.closing_agent_id', '=', 'ca.id')
                ->leftJoin('model_has_roles as mhr', 'd.created_by', '=', 'mhr.model_id')
                ->leftJoin('roles as rs', function ($qb) {
                    $qb->on('mhr.role_id', '=', 'rs.id')
                    ->where('rs.name', '=', RolesDef::TEAM_LEADER);
                })
                ->leftJoin('users as tl', 'u.team_leader_id', '=', 'tl.id')
                ->leftJoin('leads as l', 'd.lead_id', '=', 'l.id')
                ->leftJoin('reminders as r', function ($qb) {
                    $qb->on('d.id', '=', 'r.object_id')
                        ->where('r.object_type', '=', 'deals');
                })
                ->whereNull('d.deleted_at')
                ->select(
                    'd.id',
                    'd.price',
                    'd.start_date',
                    'd.end_date',
                    'd.unit_number',
                    'd.deal_status',
                    'd.deal_payment_status',
                    'd.landlord_commission_cashed',
                    'd.client_commission_cashed',
                    'd.created_by',
                    'd.created_at',
                    'd.closing_agent_id',
                    'p.ref_no as property_ref_no',
                    'p.asset_id as property_asset_id',
                    'p.id as property_id',
                    'c.id as client_id',
                    'c.name as name',
                    'pc.name as property_landlord_name',
                    'pc.id as property_landlord_id',
                    'o.id as owner_id',
                    'o.name as owner_name',
                    'u.name as creator_name',
                    'r.id as reminder_id',
                    'tl.id as team_leader_id',
                    'tl.name as team_leader_name',
                    'rs.id as has_team_leader'
                );

            $clauses = [];
            if (!empty($extraConfig['wheres']) && is_array($extraConfig['wheres']) && count($extraConfig['wheres']) > 0) {
                foreach ($extraConfig['wheres'] as $k => $filterVal) {
                    $clauses[] = [$k, 'LIKE', $filterVal . '%'];
                }
            }

            $userIsAgent = $user->hasRole(RolesDef::AGENT);
            $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
            $userIsManager = $user->hasRole(RolesDef::OFFICE_MANAGER) || $userHasDealsReadAllPermission;

            if (!$userIsAgent && !$userIsTeamLeader && !$userIsManager) {
                return [];
            }

            $vt = $extraConfig['vt'];
            if ($vt === 'pending') {
                $q->whereIn('d.deal_status', ['pending', 'rejected']);
                if (!$userIsManager && ($userIsAgent || $userIsTeamLeader)) {
                    $q->where(function ($qb) use ($user, $userIsTeamLeader) {
                        $qb->where('d.closing_agent_id', $user->id)
                            ->orWhere(function ($qqb) use ($user) {
                                $qqb->whereNull('d.closing_agent_id')->where('d.created_by', $user->id);
                            });
                        if ($userIsTeamLeader) {
                            $qb->orWhere('ca.team_leader_id', $user->id)
                                ->orWhere('u.team_leader_id', $user->id);
                        }
                    });
                }
            } elseif ($vt === 'approved') {
                $q->whereIn('d.deal_status', ['approved']);
                if (!$userIsManager && ($userIsAgent || $userIsTeamLeader)) {
                    $q->where(function ($qb) use ($user, $userIsTeamLeader) {
                        $qb->where('d.closing_agent_id', $user->id)
                            ->orWhere(function ($qqb) use ($user) {
                                $qqb->whereNull('d.closing_agent_id')->where('d.created_by', $user->id);
                            });
                        if ($userIsTeamLeader) {
                            $qb->orWhere('ca.team_leader_id', $user->id)
                                ->orWhere('u.team_leader_id', $user->id);
                        }
                    });
                }
            } elseif ($vt === 'finalized') {
                $q->whereIn('d.deal_status', ['finalized']);
                if (!$userIsManager && ($userIsAgent || $userIsTeamLeader)) {
                    $q->where(function ($qb) use ($user, $userIsTeamLeader) {
                        $qb->where('d.closing_agent_id', $user->id)
                            ->orWhere(function ($qqb) use ($user) {
                                $qqb->whereNull('d.closing_agent_id')->where('d.created_by', $user->id);
                            });
                        if ($userIsTeamLeader) {
                            $qb->orWhere('ca.team_leader_id', $user->id)
                                ->orWhere('u.team_leader_id', $user->id);
                        }
                    });
                }
            } elseif ($vt === 'default') {
                if (!$userIsManager && ($userIsAgent || $userIsTeamLeader)) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where('d.closing_agent_id', $user->id)
                            ->orWhere(function ($qqb) use ($user) {
                                $qqb->whereNull('d.closing_agent_id')->where('d.created_by', $user->id);
                            });
                    });
                }
            } elseif ($vt = "team_list") {
                if ($userIsTeamLeader) {
                    $q->where(function ($qb) use ($user) {
                        $qb->where(function ($qqb) use ($user) {
                            $qqb->where('d.closing_agent_id', $user->id)
                                ->orWhere('ca.team_leader_id', $user->id);
                        })
                            ->orWhere(function ($qqb) use ($user) {
                                $qqb->whereNull('d.closing_agent_id')->where(function ($qqb) use ($user) {
                                    $qqb->where('d.created_by', $user->id)->orWhere('u.team_leader_id', $user->id);
                                });
                            });
                    });
                }
            }

            if (count($clauses) > 0) {
                $q = $q->where($clauses);
            }

            if (!empty($extraConfig['q'])) {
                $kw = $extraConfig['q'];
                $q = $q->where(function ($sql) use ($kw) {
                    $fields = ['p.ref_no', 'c.name', 'o.name', 'd.id', DB::raw("CONCAT('D-', LPAD(d.id, 6, '0'))")];
                    $sql->where('d.price', 'LIKE', $kw . '%');
                    foreach ($fields as $f) {
                        $sql = $sql->orWhere($f, 'LIKE', $kw . '%');
                    }
                });
            }

            if (isset($extraConfig['searchParams'])) {
                if (isset($extraConfig['searchParams']['dealType'])) {
                    $q->where('d.type', $extraConfig['searchParams']['dealType']);
                }
                if (isset($extraConfig['searchParams']['agentId'])) {
                    $q->where('d.created_by', $extraConfig['searchParams']['agentId']);
                }
                if (isset($extraConfig['searchParams']['teamLeaderId'])) {
                    $teamLeaderId = $extraConfig['searchParams']['teamLeaderId'];
                    $q->where(function ($query) use ($teamLeaderId) {
                        $query->where('d.created_by', $teamLeaderId)
                              ->orWhereIn('d.created_by', function ($subquery) use ($teamLeaderId) {
                                  $subquery->select('id')
                                           ->from('users')
                                           ->where('team_leader_id', $teamLeaderId);
                              });
                    });
                }
                if (isset($extraConfig['searchParams']['createdAtFrom'])) {
                    $q->whereRaw("d.created_at >= ? ", [$extraConfig['searchParams']['createdAtFrom'] . " 00:00:00"]);
                }
                if (isset($extraConfig['searchParams']['createdAtTo'])) {
                    $q->whereRaw("d.created_at <= ? ", [$extraConfig['searchParams']['createdAtTo'] . " 23:59:59"]);
                }
                // if (isset($extraConfig['searchParams']['year']) && isset($extraConfig['searchParams']['month'])) {
                //     $q->whereRaw('DATE_FORMAT(d.created_at, "%Y-%m") LIKE "' . ($extraConfig['searchParams']['year'] . '-' . $extraConfig['searchParams']['month']) . '"');
                // }
                if (isset($extraConfig['searchParams']['dealEndYear'])) {
                    if (isset($extraConfig['searchParams']['dealEndMonth'])) {
                        $q->whereRaw('DATE_FORMAT(d.end_date, "%Y-%m") LIKE "' . ($extraConfig['searchParams']['dealEndYear'] . '-' . $extraConfig['searchParams']['dealEndMonth']) . '"');
                    } else {
                        $q->whereRaw('DATE_FORMAT(d.end_date, "%Y") LIKE "' . ($extraConfig['searchParams']['dealEndYear']) . '"');
                    }
                }
                if (isset($extraConfig['searchParams']['refNo'])) {
                    $q->where('p.ref_no', $extraConfig['searchParams']['refNo']);
                }
                if (isset($extraConfig['searchParams']['leadSource'])) {
                    $leadSources = $extraConfig['searchParams']['leadSource'];
                    if (is_array($leadSources)) {
                        $q->whereIn('l.platform_from', $leadSources);
                    } else {
                        $q->where('l.platform_from', $leadSources);
                    }
                }
            }

            if (!empty($extraConfig['sort'])) {
                if (!empty($extraConfig['dir'])) {
                    $q = $q->orderBy($extraConfig['sort'], $extraConfig['dir']);
                } else {
                    $q = $q->orderBy($extraConfig['sort'], 'asc');
                }
            } else {
                $q = $q->orderBy('d.created_at', 'desc');
            }

            $count = $q->count();

            if ($limit != -1) {
                $q = $q->offset($offset);
                $q = $q->limit($limit);
            }
            $q->groupBy('d.id');

            $items = $q->get();
            $items = ['items' => $items, 'count' => $count];
        }

        foreach ($items['items'] as $item) {
            $refNo = $this->computeDealRefNo($item->id);
            $item->ref_no = $refNo;
        }

        return $items;
    }

    public function getExtraConfig(Request $request)
    {
        $columns = $request->query('columns');
        $order = $request->query('order');
        $search = $request->query('search');

        $extraConfig = ['q' => ''];

        if (!empty($columns) && is_array($columns)) {
            //build the where clauses
            $wheres = [];
            foreach ($columns as $col) {
                if ($col['searchable'] && isset($col['search']) && isset($col['search']['value']) && !empty($col['search']['value'])) {
                    $wheres[$col['name']] = $col['search']['value'];
                }
            }

            $extraConfig['wheres'] = $wheres;
        }

        if (!empty($order) && is_array($order) && !empty($order[0])) {
            if (!empty($columns) && is_array($columns) && !empty($columns[$order[0]['column']])) {
                $sortBy = $columns[$order[0]['column']]['name'];
                $extraConfig['sort'] = $sortBy;
                $extraConfig['dir'] = $order[0]['dir'];
            }
        }

        if (!empty($search) && is_array($search) && !empty($search['value'])) {
            $extraConfig['q'] = $search['value'];
        }

        $vt = $request->get('vt', 'default');
        if (!in_array($vt, ['default', 'team_list', 'pending', 'approved', 'finalized'])) {
            $vt = 'default';
        }

        $extraConfig['vt'] = $vt;
        $extraConfig['searchParams'] = [];

        $type = $request->get('dealType', null);
        $agentId = $request->get('agentId', null);
        $teamLeaderId = $request->get('teamLeaderId', null);
        $createdAtFrom = $request->get('createdAtFrom', null);
        $createdAtTo = $request->get('createdAtTo', null);
        // $month = $request->get('month', null);
        // $year = $request->get('year', null);
        $dealEndMonth = $request->get('dealEndMonth', null);
        $dealEndYear = $request->get('dealEndYear', null);
        $refNo = $request->get('refNo', null);
        $leadSource = $request->get('leadSource', null);

        if (!is_null($type)) {
            $extraConfig['searchParams']['dealType'] = $type;
        }

        if (!is_null($agentId)) {
            $extraConfig['searchParams']['agentId'] = $agentId;
        }

        if (!is_null($teamLeaderId)) {
            $extraConfig['searchParams']['teamLeaderId'] = $teamLeaderId;
        }

        if (!is_null($createdAtFrom)) {
            $extraConfig['searchParams']['createdAtFrom'] = $createdAtFrom;
        }

        if (!is_null($createdAtTo)) {
            $extraConfig['searchParams']['createdAtTo'] = $createdAtTo;
        }

        // if (!is_null($month)) {
        //     $extraConfig['searchParams']['month'] = $month;
        // }

        // if (!is_null($year)) {
        //     $extraConfig['searchParams']['year'] = $year;
        // }

        if (!is_null($dealEndMonth)) {
            $extraConfig['searchParams']['dealEndMonth'] = $dealEndMonth;
        }

        if (!is_null($dealEndYear)) {
            $extraConfig['searchParams']['dealEndYear'] = $dealEndYear;
        }

        if (!is_null($refNo)) {
            $extraConfig['searchParams']['refNo'] = $refNo;
        }
        if (!is_null($leadSource)) {
            $extraConfig['searchParams']['leadSource'] = $leadSource;
        }

        return $extraConfig;
    }

    public function getItemsForXLS()
    {
        $items = $this->fetchItems([], -1);
        $preparedItems = $items['items']->map($this->ajaxListMapper);
        return $preparedItems;
    }

    public function getPropertiesXLSHeader()
    {
        return ["Property REF", "Unit NO", "Client", "Owner", "Price", "Start Date", "End Date"];
    }

    public function computeDealRefNo($dealId)
    {
        return sprintf('D-%06d', $dealId);
    }

    public function handleDealsAgents($deal, $request)
    {
        $agentsClientToSyncWithIds = $request->has('agent_id_client') && !empty($request->get('agent_id_client')) ? explode(",", trim($request->get('agent_id_client'))) : [];
        $agentsLandlordToSyncWithIds = $request->has('agent_id_landlord') && $request->get('agent_id_landlord') ? explode(",", trim($request->get('agent_id_landlord'))) : [];

        $deal->agentCommission()->sync([]);
        if (count($agentsClientToSyncWithIds) > 0) {
            $deal->agentCommission()->syncWithPivotValues($agentsClientToSyncWithIds, ['agent_type' => 'client']);
        }
        if (count($agentsLandlordToSyncWithIds) > 0) {
            foreach ($agentsLandlordToSyncWithIds as $agentLandlordId) {
                $deal->agentCommission()->attach($agentLandlordId, ['agent_type' => 'landlord']);
            }
        }
    }

    public function getDealRefNo($deal)
    {
        return "D" . str_pad($deal->id, 6, '0', STR_PAD_LEFT);
    }
}
