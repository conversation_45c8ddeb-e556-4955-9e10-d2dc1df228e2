<?php

namespace App\Services;

use App\Models\Crm\NoteAssignment;
use App\Models\Crm\Note;
use App\Models\Crm\Reminder;
use App\Models\Lead;
use App\Models\Task;
use Log;
use Auth;
use Carbon\Carbon;

class NotesService
{

    public function getObjectNotes($object, $withTrashed = false)
    {
        $objectType = $object->getTable();
        $objectId = $object->id;

        if ($withTrashed) {
            $noteAssignments = NoteAssignment::withTrashed()->where("object_type", $objectType)->where("object_id", $objectId)->get();
        } else {
            $noteAssignments = NoteAssignment::where("object_type", $objectType)->where("object_id", $objectId)->get();
        }


        $notes = $noteAssignments->map(function ($noteAssignment) use ($withTrashed) {
            if ($withTrashed) {
                return Note::withTrashed()->find($noteAssignment->note_id);
            } else {
                return $noteAssignment->note;
            }
        });

        return $notes;
    }

    public function getSubObjectNotes($object, $subObject, $withTrashed = false)
    {
        $objectType = $object->getTable();
        $objectId = $object->id;
        $subObjectType = $subObject->getTable();
        $subObjectId = $subObject->id;

        if ($withTrashed) {
            $noteAssignments = NoteAssignment
                ::withTrashed()
                ->where("object_type", $objectType)
                ->where("object_id", $objectId)
                ->where("sub_object_type", $subObjectType)
                ->where("sub_object_id", $subObjectId)
                ->get();
        } else {
            $noteAssignments = NoteAssignment
                ::where("object_type", $objectType)
                ->where("object_id", $objectId)
                ->where("sub_object_type", $subObjectType)
                ->where("sub_object_id", $subObjectId)
                ->get();
        }


        $notes = $noteAssignments->map(function ($noteAssignment) use ($withTrashed) {
            if ($withTrashed) {
                return Note::withTrashed()->find($noteAssignment->note_id);
            } else {
                return $noteAssignment->note;
            }
        });

        return $notes;
    }

    public function updateNoteFromRequest($request, $noteId)
    {
        $note = Note::find($noteId);

        $note->type = $request->input("type");
        $note->title = $request->input("title");
        $note->text = $request->input("text");
        $note->status = $request->input("status");

        $note->save();
    }

    public function updateReminderFromRequest($request, $reminderId)
    {
        $reminder = Reminder::find($reminderId);

        $reminder->title = $request->input("title");
        $reminder->text = $request->input("text");
        $reminder->due_date = $request->input("due_date");
        $reminder->priority = $request->input("priority");
        $reminder->send_email = $request->has("send_email");

        $reminder->save();
    }

    public function deleteReminder($reminderId)
    {
        $reminder = Reminder::find($reminderId);

        if ($reminder) {
            $reminder->delete();
        }
    }

    public function addReminderFromRequest($request, $noteId)
    {
        $reminder = new Reminder();

        $reminder->title = $request->input("title");
        $reminder->text = $request->input("text");
        $reminder->due_date = $request->input("due_date");
        $reminder->priority = $request->input("priority");
        $reminder->send_email = $request->has("send_email");

        if ($request->has("send_email")) {
            $reminder->email = Auth::user()->email;
        }

        $reminder->note_id = $noteId;

        $reminder->created_by = Auth::user()->id;

        $reminder->save();

        return $reminder;
    }

    public function createNoteAndRemindersFromRequest($request, &$note, &$reminders)
    {
        $note = new Note();

        $note->type = $request->input("type");
        $note->title = $request->input("title");
        $note->text = $request->input("text");
        $note->status = $request->input("status");
        $note->created_by = auth()->user()->id;

        if ($request->has("reminders")) {
            $reminders = [];

            foreach ($request->input("reminders") as $reminder) {
                $newReminder = new Reminder();
                $newReminder->title = $reminder['title'];
                $newReminder->text = $reminder['text'];
                $newReminder->priority = $reminder['priority'];
                $newReminder->due_date = $reminder['due_date'];
                $newReminder->send_email = $reminder['send_email'];

                if ($reminder['send_email']) {
                    $newReminder->email = Auth::user()->email;
                }

                $newReminder->created_by = Auth::user()->id;

                array_push($reminders, $newReminder);
            }
        }

        return true;
    }

    public function createNote($data)
    {
        $note = new Note();

        $note->type = $data["type"];
        $note->title = $data["title"];
        $note->text = $data["text"];
        $note->status = $data["status"];

        return $note;
    }

    public function createReminder($data)
    {
        $newReminder = new Reminder();

        $newReminder->title = $data['title'];
        $newReminder->text = $data['text'];
        $newReminder->priority = $data['priority'];
        $newReminder->due_date = $data['due_date'];
        $newReminder->send_email = $data['send_email'];

        if ($data['send_email']) {
            $newReminder->email = Auth::user()->email;
        }

        return $newReminder;
    }

    public function createReminderWithObject($data, $object)
    {
        $newReminder = new Reminder();
        $newReminder->title = $data['title'];
        $newReminder->text = $data['text'];
        $newReminder->due_date = $data['due_date'];
        $newReminder->priority = $data['priority'];
        $newReminder->send_email = $data['reminder_email'];
        $newReminder->send_email_date = $data['reminder_email_date'];
        $newReminder->note_id = '0';

        if (request()->get('param') == 'dashboard') {
            $newReminder->object_type = 'dashboard';
        } elseif (request()->get('param') == 'contacts_list') {
            $newReminder->object_type = 'contacts_list';
        } else {
            $newReminder->object_type = $object->getTable();
        }
        // for properties we need to keep asset id
        if ($newReminder->object_type === "properties") {
            $newReminder->object_id = $object->asset_id;
        } elseif ($newReminder->object_type === "dashboard") {
            $newReminder->object_id = '0';
        } elseif ($newReminder->object_type === "deals") {
            if ($data['is_for_deal_end_date'] == 1) {
                $newReminder->is_for_deal_end_date = $data['is_for_deal_end_date'];
            }
            $newReminder->object_id = $object->id;
        } else {
            $newReminder->object_id = $object->id;
        }

        if ($data['reminder_email']) {
            $newReminder->email = Auth::user()->email;
        }
        $newReminder->created_by = Auth::user()->id;

        $newReminder->save();
    }

    public function createReminderForLead(Lead $object, $data, $taskData = null)
    {
        $newReminder = new Reminder();
        $newReminder->title = $data['title'];
        $newReminder->text = $data['text'];
        $newReminder->due_date = $data['due_date'];
        $newReminder->priority = $data['priority'];
        $newReminder->send_email = $data['reminder_email'];
        $newReminder->send_email_date = $data['reminder_email_date'];
        $newReminder->reminder_type = $data['reminder_type'];
        $newReminder->note_id = '0';

        $newReminder->object_type = $object->getTable();
        $newReminder->object_id = $object->id;

        if ($data['reminder_email']) {
            $newReminder->email = auth()->user()->email;
        }
        $newReminder->created_by = auth()->user()->id;
        $newReminder->save();

        if (!empty($taskData)) {
            try {
                $propertyId = null;
                if (is_array($taskData['listingSelection']) && count($taskData['listingSelection']) > 0) {
                    foreach ($taskData['listingSelection'] as $listingId => $refNo) {
                        if (is_null($propertyId)) {
                            $propertyId = $listingId;
                        }
                    }
                }
                $startDate = null;
                $startTime = null;
                if (!empty($data['due_date'])) {
                    $carbonDate = Carbon::parse($data['due_date']);
                    $startDate = $carbonDate->format('Y-m-d');
                    $startTime = $carbonDate->format('H:i');
                }
                $taskData = [
                    "object_id" => $object->id,
                    "object_type" => 'lead',
                    "meeting_type" => '', //$validData['meeting_type'],
                    "type" => $taskData['status'] == 'VIEWING_SCHEDULED' ? Task::TASK_TYPE_VIEWING : Task::TASK_TYPE_MEETING,
                    "call_response" => null,
                    "call_notes" => null,
                    'created_by' => auth()->user()->id,
                    'property_id' => $propertyId ?? null,
                    'location_id' => $data['location_id'] ?? null,
                    'subject' => $data['title'],
                    'status' => Task::TASK_STATUS_NOT_STARTED,
                    'notes' => "Automatically created. " . $data['text'],
                    'start_date' => $startDate,
                    'start_time' => $startTime,
                    'end_date' => null,
                    'end_time' => null,
                    'reminder_set' => true,
                    'reminder_id' => $newReminder->id,
                    'due_date' => $data['due_date'],
                ];
                Task::create($taskData);
            } catch (\Exception $Ex) {
                Log::error('Cannot created Task ' . $Ex->getMessage());
            }
        }
        return $newReminder;
    }

    public function attachNoteToObject($object, $note, $reminders = null)
    {
        $assignment = new NoteAssignment();
        $assignment->note_id = $note->id;
        $assignment->object_type = $object->getTable();
        $assignment->object_id = $object->id;

        $assignment->save();

        if ($reminders) {
            foreach ($reminders as $reminder) {
                $reminder->note_id = $note->id;
                $reminder->save();
            }
        }
    }

    public function attachNoteToObjectAndSubObject($object, $subObject, $note, $reminders = null)
    {
        $assignment = new NoteAssignment();
        $assignment->note_id = $note->id;
        $assignment->object_type = $object->getTable();
        $assignment->object_id = $object->id;
        $assignment->sub_object_type = $subObject->getTable();
        $assignment->sub_object_id = $subObject->id;

        $assignment->save();

        if ($reminders) {
            foreach ($reminders as $reminder) {
                $reminder->note_id = $note->id;
                $reminder->save();
            }
        }
    }
    public function deleteNoteAndReminders($noteId)
    {
        Log::info("\nDeleting note $noteId\n");
        $note = Note::with("reminders", "assignment")->find($noteId);

        $note->reminders->each(function ($reminder) {
            Reminder::find($reminder->id)->delete();
        });

        $noteAssignment = $note->assignment;
        NoteAssignment::find($noteAssignment->id)->delete();

        $note->delete();

        Log::info("\nNote deleted\n");
    }

    public function deleteObjectNotesAndReminders($object)
    {
        $noteAssignments
            = NoteAssignment::with("reminders")
            ->where("object_type", $object->getTable())
            ->where("object_id", $object->id);

        foreach ($noteAssignments as $assignment) {
            $note = $assignment->note;

            $note->reminders->each(function ($reminder) {
                Reminder::find($reminder->id)->delete();
            });

            $note->delete();

            $assignment->delete();
        }
    }
}
