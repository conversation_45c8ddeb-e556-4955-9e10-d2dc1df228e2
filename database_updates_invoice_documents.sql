-- SQL to update the invoice table to support document storage
-- This ensures the documents field can store JSON data for the new document functionality

-- Check if the documents column exists, if not add it
-- If it exists, ensure it's the correct type for JSON storage

-- For MySQL 5.7+ (with native JSON support)
ALTER TABLE invoices 
ADD COLUMN documents JSON NULL 
COMMENT 'Stores document information as JSON array with file_name, file_path, document_type, observations, and upload_date';

-- Alternative for older MySQL versions (stores as TEXT)
-- ALTER TABLE invoices 
-- MODIFY COLUMN documents TEXT NULL 
-- COMMENT 'Stores document information as JSON string with file_name, file_path, document_type, observations, and upload_date';

-- If the documents column doesn't exist at all, use this instead:
-- ALTER TABLE invoices 
-- ADD COLUMN documents JSON NULL 
-- COMMENT 'Stores document information as JSON array with file_name, file_path, document_type, observations, and upload_date';

-- Example of the JSON structure that will be stored:
-- [
--   {
--     "file_name": "invoice_copy.pdf",
--     "file_path": "landlord_documents/123/invoice_copy.pdf",
--     "document_type": "invoice",
--     "observations": "Original invoice from landlord",
--     "upload_date": "2024-01-15 10:30:00"
--   },
--   {
--     "file_name": "receipt.jpg",
--     "file_path": "landlord_documents/123/receipt.jpg", 
--     "document_type": "receipt",
--     "observations": "Payment receipt",
--     "upload_date": "2024-01-15 10:35:00"
--   }
-- ]

-- Index for better performance when querying by deal_id
CREATE INDEX IF NOT EXISTS idx_invoices_deal_id ON invoices(deal_id);

-- Index for better performance when querying by client type
CREATE INDEX IF NOT EXISTS idx_invoices_is_client ON invoices(is_client);

-- Composite index for the most common query pattern
CREATE INDEX IF NOT EXISTS idx_invoices_deal_client ON invoices(deal_id, is_client);
