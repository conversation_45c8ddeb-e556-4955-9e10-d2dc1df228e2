let leadSourcesTable;
function deleteLeadSource(id) {
    if (confirm("Are you sure you want to delete this lead source?")) {
        request(`/api/lead-source/${id}`, {
            method: 'DELETE'
        })
            .then(res => {
                showToast("Item succesfully deleted. Will refresh", 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 3000)
            })
            .catch(err => showToast(`Cannot delete the item [${err.message}]`, 'error'))
    }
}
$(document).ready(function () {
    let currentRequest = null;
    const addCustomFiltersAndSearch = (sSource, aoData, fnCallback) => {
        const extraClauses = [];

        const urlStr = parseDTParams(aoData);
        // urlStr.push(`lt=${controllerListLandlordType}`);
        urlStr.push(`${jQuery.param(extraClauses)}`);
        const data = urlStr.join("&");
        if (currentRequest && currentRequest.readyState !== 4) {
            currentRequest.abort();
        }
        currentRequest = $.ajax({
            dataType: "json",
            url: "/admin/lead-sources/search",
            data,
            success: (input) => {
                fnCallback(input);
            },
        });
    };

    const colDefs = [
        {
            data: "name",
            name: "name",
        },
        {
            data: "campaign_name",
            render(id, _, row) { 
                return row.campaign_name ? row.campaign_name : '-'
            }
        },
        {
            data: "leads_no",
            name: "leads_no",
            orderable: false,
        },
        {
            data: "id",
            render(id, _, row) {
                const editLink = `<li><a class="dropdown-item" href="javascript:toggleSourceModal('${id}')"> <span data-feather="edit"></span> Edit</a></li>`;
                const deleteLink = `<li><a class="dropdown-item" href="javascript:deleteLeadSource('${id}')"> <span data-feather="trash"></span> Delete</a></li>`;
                return `
               <div class="dropdown">
                   <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="dropdownMenuButton${id}">
                    Actions
                   </button>
                   <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton${id}">
                    ${editLink}
                    ${deleteLink}
                   </ul>
               </div>
           `;
            },
            orderable: false,
            responsivePriority: 1,
        },
    ];

    leadSourcesTable = $("#lead-sources-table").DataTable({
        serverSide: true,
        lengthMenu: [
            [10, 25, 50, -1],
            [10, 25, 50, "All"],
        ],
        pageLength: 50,
        responsive: false,
        scrollY: "calc(100vh - 320px)",
        oLanguage: {
            sProcessing: `
          <div class="spinner-border m-5" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
      `,
        },
        processing: true,
        searching: true,
        order: [[0, "desc"]],
        columns: colDefs,
        dom: `
        "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6 d-flex justify-content-end'f>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 d-flex justify-content-end'p>>"
        `,
        // orderCellsTop: true,
        fnServerData: addCustomFiltersAndSearch,
    });
});
