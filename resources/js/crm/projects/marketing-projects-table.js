let projectsTable;

/**
 * Confirm deletion of a marketing project
 * @param {number} id - The ID of the project to delete
 */
function confirmDeleteProject(id) {
    if (confirm("Are you sure you want to delete this marketing project? This will also delete all associated campaigns.")) {
        window.location.href = `/crm/marketing-projects/${id}/delete`;
    }
}

$(document).ready(function () {
    const addCustomFiltersAndSearch = (sSource, aoData, fnCallback) => {
        const extraClauses = [];

        const urlStr = parseDTParams(aoData);
        urlStr.push(`${jQuery.param(extraClauses)}`);
        const data = urlStr.join("&");
        $.ajax({
            dataType: "json",
            url: "/crm/marketing-projects",
            data,
            success: (input) => {
                fnCallback(input)
                feather.replace()
                setTimeout(() => {
                    const dropdownElementList = document.querySelectorAll('.dropdown-toggle')
                    const dropdownList = [...dropdownElementList].map(dropdownToggleEl => new bootstrap.Dropdown(dropdownToggleEl))
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }, 300)
            },
        });
    };

    const colDefs = [
        {data: "id"},
        {data: "name"},
        {data: "campaigns_count"},
        {data: "created_at"},
        {data: "created_by"},
        {
            data: "id",
            render(id, _, row) {
                return `
           <div class="dropdown">
               <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="dropdownMenuButton${id}" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Actions
               </button>
               <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton${id}">
                <li><a class="dropdown-item" href="/crm/marketing-projects/${id}/edit"> <i class="bi bi-pen"></i> Edit</a></li>
                <li><a class="dropdown-item" href="javascript:void(0)" onclick="confirmDeleteProject(${id})"> <i class="bi bi-file-x"></i> Delete</a></li>
               </ul>
           </div>
           `
            },
            orderable: false,
            responsivePriority: 1
        },
    ];
    projectsTable = $("#projects-table").DataTable({
        serverSide: true,
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
        responsive: true,
        scrollY: 'calc(100vh - 350px)',
        oLanguage: {
            sProcessing: `
          <div class="spinner-border m-5" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
      `
        },
        processing: true,
        searching: true,
        order: [[1, "asc"]],
        columns: colDefs,
        // orderCellsTop: true,
        fnServerData: addCustomFiltersAndSearch,
    });
});
