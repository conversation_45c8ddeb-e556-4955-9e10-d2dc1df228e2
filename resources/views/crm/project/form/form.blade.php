@extends('crm.layout')
@section('title')
FGRealty CRM - Project Form
@endsection
@section('content')
    <div data-bs-spy="scroll" data-bs-target="#navbar-broker-landlord-form" class="property-scrollspy"
         data-bs-offset="0"
         tabindex="0">

        @if($errors->any())
            <div class="alert alert-danger mt-4">Please review the fields marked with red</div>
        @endif

        @if (session('message.success'))
            <div class="alert alert-success mt-4">
                {{ session('message.success') }}
            </div>
        @endif

        @if (session('message.error'))
            <div class="alert alert-danger mt-4">
                {{ session('message.error') }}
            </div>
        @endif

        @component('crm.components.form.section-card', ['sectionId' => 'broker-landlord'])
            <h4> Project info</h4>
            <form style="width:100%" action="{{$formAction}}"
                  method="POST" enctype="multipart/form-data">

                @csrf

                <div class="row">
                    <div class="col-12 col-md-6">
                        @include('crm.components.form.input',
                            [
                                'fieldName' => 'name',
                                'fieldLabel' => 'Name',
                                'defaultValue' => defaultValue($item, 'name')
                            ]
                        )
                    </div>
                </div>
                <div class="mt-3 mb-3">
                    <div class="col-sm-12 header-btn text-end">
                        <button class="btn btn-outline-primary" type="submit">Save</button>
                        <a href="{{ route('crm.projects.index')}}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </div>
            </form>
        @endcomponent

    @if(isset($item->id))
        <div class="card my-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Campaigns</h5>
                <button class="btn btn-sm btn-outline-success" type="button" id="addCampaignRow">
                    + Add Campaign
                </button>
            </div>

            <div id="campaignFormWrapper" class="d-none">
                <form action="{{ route('crm.projects.campaign.create', ['id' => $item->id]) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="card-body" id="campaignsContainer"></div>
                    <div class="text-end px-3 pb-3">
                        <button type="submit" class="btn btn-primary">Save Campaigns</button>
                    </div>
                </form>
            </div>
        </div>

        @if($item->campaigns->count())
            <div class="table-responsive">
                <h6>Existing Campaigns</h6>
                <table class="table table-bordered table-striped mb-0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Hash</th>
                            <th>Name</th>
                            <th>Amount</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($item->campaigns as $campaign)
                            <tr data-id="{{ $campaign->id }}">
                                <td>{{ $campaign->id }}</td>

                                <td class="campaign-hash">
                                    <span class="hash-text">{{ $campaign->hash ?? '-' }}</span>
                                </td>

                                <td class="campaign-name">
                                    <span class="name-text">{{ $campaign->name }}</span>
                                    <input type="text" name="name" class="form-control d-none name-input" value="{{ $campaign->name }}">
                                </td>

                                <td class="campaign-amount">
                                    <span class="amount-text">{{ number_format($campaign->amount, 2) }}</span>
                                    <input type="number" step="0.01" name="amount" class="form-control d-none amount-input" value="{{ $campaign->amount }}">
                                </td>

                                <td class="text-end campaign-actions">
                                    <div class="d-inline action-buttons">
                                        <button type="button" class="btn btn-sm btn-outline-primary btn-edit">Edit</button>
                                        <button type="button" class="btn btn-sm btn-outline-danger btn-delete-campaign"
                                                data-project-id="{{ $item->id }}"
                                                data-campaign-id="{{ $campaign->id }}"
                                                data-campaign-name="{{ $campaign->name }}">
                                            Delete
                                        </button>
                                    </div>

                                    <form action="{{ route('crm.projects.campaign.update', [$item->id, $campaign->id]) }}"
                                          method="POST" class="d-inline edit-form d-none">
                                        @csrf
                                        <input type="hidden" name="name" class="hidden-name-input">
                                        <input type="hidden" name="amount" class="hidden-amount-input">
                                        <button type="submit" class="btn btn-sm btn-success">Save</button>
                                        <button type="button" class="btn btn-sm btn-secondary btn-cancel">Cancel</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif

        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const container = document.getElementById('campaignsContainer');
                const addBtn = document.getElementById('addCampaignRow');
                const wrapper = document.getElementById('campaignFormWrapper');
                let formVisible = false;

                addBtn.addEventListener('click', function () {
                    if (!formVisible) {
                        wrapper.classList.remove('d-none');
                        formVisible = true;
                    }

                    const newRow = document.createElement('div');
                    newRow.classList.add('row', 'campaign-row', 'align-items-end', 'mt-2');
                    newRow.innerHTML = `
                        <div class="col-md-5">
                            <input type="text" class="form-control" placeholder="Campaign Name" required>
                        </div>
                        <div class="col-md-4">
                            <input type="number" step="0.01" class="form-control" placeholder="Amount (e.g. 100.00)" required>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-danger remove-campaign">Remove</button>
                        </div>
                    `;

                    const nameInput = newRow.querySelectorAll('input')[0];
                    const amountInput = newRow.querySelectorAll('input')[1];
                    nameInput.setAttribute('name', `campaigns[${container.children.length}][name]`);
                    amountInput.setAttribute('name', `campaigns[${container.children.length}][amount]`);

                    container.appendChild(newRow);
                });

                container.addEventListener('click', function (e) {
                    if (e.target.classList.contains('remove-campaign')) {
                        const row = e.target.closest('.campaign-row');
                        if (row) row.remove();
                    }
                });

                document.querySelectorAll('.btn-edit').forEach(button => {
                    button.addEventListener('click', function () {
                        const row = button.closest('tr');
                        row.querySelector('.name-text').classList.add('d-none');
                        row.querySelector('.name-input').classList.remove('d-none');
                        row.querySelector('.amount-text').classList.add('d-none');
                        row.querySelector('.amount-input').classList.remove('d-none');
                        row.querySelector('.edit-form').classList.remove('d-none');
                        row.querySelector('.action-buttons').classList.add('d-none');
                    });
                });

                document.querySelectorAll('.btn-cancel').forEach(button => {
                    button.addEventListener('click', function () {
                        const row = button.closest('tr');
                        const nameInput = row.querySelector('.name-input');
                        nameInput.value = row.querySelector('.name-text').textContent.trim();
                        nameInput.classList.add('d-none');
                        row.querySelector('.name-text').classList.remove('d-none');

                        const amountInput = row.querySelector('.amount-input');
                        amountInput.value = row.querySelector('.amount-text').textContent.trim();
                        amountInput.classList.add('d-none');
                        row.querySelector('.amount-text').classList.remove('d-none');

                        row.querySelector('.edit-form').classList.add('d-none');
                        row.querySelector('.action-buttons').classList.remove('d-none');
                    });
                });

                document.querySelectorAll('.edit-form').forEach(form => {
                    form.addEventListener('submit', function (e) {
                        const row = form.closest('tr');
                        const nameValue = row.querySelector('.name-input').value;
                        const amountValue = row.querySelector('.amount-input').value;
                        row.querySelector('.hidden-name-input').value = nameValue;
                        row.querySelector('.hidden-amount-input').value = amountValue;
                    });
                });

                // Add event listeners for campaign deletion buttons
                document.querySelectorAll('.btn-delete-campaign').forEach(button => {
                    button.addEventListener('click', function() {
                        const projectId = this.getAttribute('data-project-id');
                        const campaignId = this.getAttribute('data-campaign-id');
                        const campaignName = this.getAttribute('data-campaign-name');

                        if (confirm(`Are you sure you want to delete the campaign "${campaignName}"?`)) {
                            window.location.href = `/crm/marketing-projects/${projectId}/campaign/${campaignId}/destroy`;
                        }
                    });
                });
            });
        </script>
    @endif
</div>
@endsection
