@extends('crm.layout')

@section('title')
FGRealty CRM - Projects
@endsection
@section('head-css')
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap5.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="https://cdn.datatables.net/fixedheader/3.1.9/css/fixedHeader.bootstrap5.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css"/>

    <link rel="stylesheet" type="text/css" href="/css/virtual-select.min.css"/>
    <style>
        .truncate {
            max-width: 50px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        td.details-control {
            background: url('{{asset('/images/datatable/details_open.png')}}') no-repeat center center;
            cursor: pointer;
        }

        tr.shown td.details-control {
            background: url('{{asset('/images/datatable/details_close.png')}}') no-repeat center center;
        }

        tr.child td.child {
            background-color: #f1f5fd;
        }

        tr.child td.child ul.dtr-details {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            grid-column-gap: 5px;
            grid-row-gap: 5px;
        }

        tr.child td.child ul.dtr-details li {
            display: flex;
        }

        tr.child td.child ul.dtr-details li span:first-child {
            display: inline-block;
            margin-right: 10px;
        }

        tr.child td.child ul.dtr-details li span:first-child:after {
            content: ':';
        }
    </style>
@endsection

@section('content')
    @if (session('message.success'))
        <br>
        <div class="alert alert-success">
            {{ session('message.success') }}
        </div>
    @endif
    <div class="px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Marketing Projects</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <a href="{{route('crm.projects.create')}}" role="button" class="btn btn-sm btn-outline-secondary">
                    <span data-feather="plus"></span>
                    Add new item
                </a>
            </div>
        </div>

        <div class="col mt-2">
            <table id="projects-table" class="table table-sm dataTable fw-light align-middle">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>No of Campaigns</th>
                    <th>Created at</th>
                    <th>Created by</th>
                    <th>Actions</th>
                </tr>
                </thead>
            </table>
        </div>
        @endsection

        @section('body-js')
            <script type="text/javascript" src="{{asset('/js/crm/libs/jquery.ba-throttle-debounce.min.js')}}"></script>
            <script type="text/javascript"
                    src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
            <script type="text/javascript"
                    src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
            <script type="text/javascript"
                    src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>
            <script type="text/javascript"
                    src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
            <script type="text/javascript"
                    src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.js"></script>
            <script src="{{mix('js/crm/libs/datatables-util.js')}}"></script>
            <script type="text/javascript" src="{{asset('/js/crm/marketing-projects-table.js')}}"></script>
@endsection
