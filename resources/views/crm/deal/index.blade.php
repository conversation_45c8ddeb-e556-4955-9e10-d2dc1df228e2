@extends('crm.layout')

@section('head-css')
<!-- <link rel="stylesheet" type="text/css" href="/css/dataTables.bootstrap.min.css" />
<link rel="stylesheet" type="text/css" href="/css/responsive.bootstrap.min.css" />

<link rel="stylesheet" type="text/css" href="/css/buttons.dataTables.min.css"> -->

<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/3.1.9/css/fixedHeader.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="/css/virtual-select.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<style>
    .vscomp-ele {
        max-width: 100%;
        display: block;
        width: 100%;
    }

    .truncate {
        max-width: 50px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    td.details-control {
        background: url('{{asset(' /images/datatable/details_open.png')}}') no-repeat center center;
        cursor: pointer;
    }

    tr.shown td.details-control {
        background: url('{{asset(' /images/datatable/details_close.png')}}') no-repeat center center;
    }

    td.details-control {
        width: 1em;
        height: 1em;
    }

    td.details-control {
        padding: 12px;
    }

    tr.details td.details-control:before {
        content: "-";
        background-color: #d33333;
    }

    td.details-control:before {
        left: 5px;
        height: 1em;
        width: 1em;
        margin-top: -9px;
        display: block;
        position: absolute;
        color: white;
        border: .15em solid white;
        border-radius: 1em;
        box-shadow: 0 0 0.2em #444;
        box-sizing: content-box;
        text-align: center;
        text-indent: 0 !important;
        font-family: "Courier New", Courier, monospace;
        line-height: 1em;
        content: "+";
        background-color: #0d6efd;
        cursor: pointer;
    }

    tr.child td.child {
        background-color: #f1f5fd;
    }

    tr.child td.child ul.dtr-details {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        grid-column-gap: 5px;
        grid-row-gap: 5px;
    }

    tr.child td.child ul.dtr-details li {
        display: flex;
    }

    tr.child td.child ul.dtr-details li span:first-child {
        display: inline-block;
        margin-right: 10px;
    }

    tr.child td.child ul.dtr-details li span:first-child:after {
        content: ':';
    }

    tr.dealsRow--statusPending .statusBox {
        background-color: #fd7e14;
    }

    tr.dealsRow--statusCashed .statusBox {
        background-color: #4dd4ac;
    }

    .dealsWrapper--statusPending .simpleStatusBox {
        background-color: #fd7e14;
    }

    .dealsWrapper--statusCashed .simpleStatusBox {
        background-color: #4dd4ac;
    }

    tr.dealsRow--statusPendingCashed .statusBox {
        background: linear-gradient(to right, #fd7e14 50%, #4dd4ac 50%);
        border: none; 
        padding: 10px 20px;
        cursor: pointer;
    }

    tr.dealsRow--statusCashedPending .statusBox {
        background: linear-gradient(to right, #4dd4ac 50%, #fd7e14 50%);
        border: none; 
        padding: 10px 20px; 
        cursor: pointer; 
    }
</style>
@endsection

@section('content')
<div class="px-md-2">

    @if(request()->session()->has('success') || request()->session()->has('info'))
    <div class="messages-container pt-4">
        @include('crm.components.session-alerts')
    </div>
    @endif
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Deals</h1>
        <div class="btn-toolbar mb-2 mb-md-0 gap-2">
            <div class="btn-group btn-group-sm me-2" role="group">
                <a href="{{route('crm.deals.index', ['vt' => 'pending'])}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'pending') active @endif">Pending</a>
                <a href="{{route('crm.deals.index', ['vt' => 'approved'])}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'approved') active @endif">Approved</a>
                <a href="{{route('crm.deals.index', ['vt' => 'finalized'])}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'finalized') active @endif">Finalized</a>
                @if($userIsManager)
                <!-- <a href="{{route('crm.deals.index')}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'default') active @endif">Closed</a> -->
                @else
                @if($userIsTeamLeader)
                {{--<a href="{{route('crm.deals.index', ['vt' => 'team_list'])}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'team_list') active @endif">Team
                    List</a>--}}
                @endif
                @if($userIsTeamLeader || $userIsAgent)
                <a href="{{route('crm.deals.index')}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'default') active @endif">Personal</a>
                @endif
                @endif
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 pb-2">
            <a role="button" class="btn btn-sm btn-outline-secondary" href="javascript:toggleExtraFilters()">
                <span data-feather="chevrons-right"></span>
                Filters
            </a>
        </div>
        <div class="card card-body p-2 m-2 bg-light d-none" id="extraFiltersPanel">
            @include('crm.deal.filters')
        </div>
    </div>

    <div class="col mt-2">
        <table class="table table-striped dataTable table-sm margin-top nowrap margin-top" id="deals-list">
            <thead>
                <tr>
                    <th></th>
                    <th></th>
                    <th>Ref.No</th>
                    <th>Property</th>
                    <th>Unit No.</th>
                    <th>Client</th>
                    <th>Owner</th>
                    <th>Price</th>
                    <th>Start</th>
                    <th>End</th>
                    <th>Agent</th>
                    <th>Status</th>
                    <th>Payment Status</th>
                    <th>Action</th>
                </tr>
            </thead>
            <!-- <tfoot>
                <tr id="deals-list-filters">
                    <th data-col-index="0">Ref No</th>
                    <th data-col-index="1">Property</th>
                    <th data-col-index="2">Unit No.</th>
                    <th data-col-index="3">Client</th>
                    <th data-col-index="4">Owner</th>
                    <th data-col-index="5" colspan="8">Price</th>
                </tr>
            </tfoot> -->
        </table>
    </div>
</div>
{{-- @component('crm.components.modal', [--}}
{{-- 'modalId' => 'modal-notice',--}}
{{-- 'modalLabel' => 'Request sent',--}}
{{-- 'modalTitle' => 'Success'--}}
{{-- ])--}}
{{-- An export request has been sent to the Administrator. When this is approved, you will receive an email with the link to the report.--}}
{{-- @endcomponent--}}

{{-- @component('crm.components.modal', [--}}
{{-- 'modalId' => 'modal-error',--}}
{{-- 'modalLabel' => 'Request failed',--}}
{{-- 'modalTitle' => 'Error'--}}
{{-- ])--}}
{{-- The export request cannot be sent. Please try again later.--}}
{{-- @endcomponent--}}
@include('crm.components.notes-modal')
@include('crm.components.deal-status-modal')
@include('crm.components.payment-status-modal')
@endsection

@section('body-js')
<script>
    const controllerListViewType = '{{$vt}}';
</script>
<script>
    const toggleExtraFilters = () => {
        const classList = document.querySelector('#extraFiltersPanel').classList;
        if (classList.contains('d-none')) {
            classList.remove('d-none');
        } else {
            classList.add('d-none');
        }
    }
</script>
<script src="{{mix('js/crm/datatables-action-menu.js')}}"></script>
<script type="text/javascript" src="{{asset('/js/crm/libs/jquery.ba-throttle-debounce.min.js')}}"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.0.1/js/bootstrap.bundle.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script type="text/javascript" src="{{asset('/js/crm/libs/datatables-util.js')}}"></script>
<script type="text/javascript" src="{{asset('/js/crm/deals-table.js').'?t='.time()}}"></script>
<script type="text/javascript" src="/js/virtual-select.min.js"></script>

{{-- <script type="text/javascript" src="/js/datatables.min.js"></script>--}}
{{-- <script type="text/javascript" src="/js/dataTables.responsive.min.js"></script>--}}
{{-- <script type="text/javascript" src="/js/dataTables.select.min.js"></script>--}}
{{-- <script type="text/javascript" src="/js/responsive.bootstrap.min.js"></script>--}}

{{-- @if(getHighestRole(Auth::user()) != role_agent())--}}
{{-- <script type="text/javascript" src="/js/dataTables.buttons.min.js"></script>--}}
{{-- <script type="text/javascript" src="/js/buttons.bootstrap.min.js"></script>--}}
{{-- <script type="text/javascript" src="/js/buttons.html5.min.js"></script>--}}
{{-- <script type="text/javascript" src="/js/jszip.min.js"></script>--}}

{{-- @if(isSerban(auth()->user()))--}}
{{-- <script>--}}
{{-- var tableType = 'deals';--}}
{{-- </script>--}}
{{-- <script type="text/javascript" src="/js/brix/table-export-admin.js?t={{date('H:i')}}"></script>--}}
{{-- @else--}}
{{-- @hasanyrole(RolesDef::OFFICE_MANAGER)--}}
{{-- <script type="text/javascript" src="/js/brix/table-export.js?t={{date('H:i')}}"></script>--}}
{{-- @endhasanyrole--}}
{{-- @endif;--}}
{{-- @endif--}}

{{-- <script type="text/javascript" src="/js/crm/deals.js"></script>--}}

@endsection