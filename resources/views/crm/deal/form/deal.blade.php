{{--
Expects the following data:
  \App\Model\crm\Deal                        - $item
  boolean                                    - $readonly
  boolean                                    - $empty
  boolean                                    - $delete
  string                                     - $action (required when $readonly = false)
  string                                     - $back
  --}}

{{--
  Note:
    This form only provides the Update, Read and Delete forms.
    There's no need for Create - Deals are created by promoting a Lead
  --}}
@php

$sources = App\Models\LeadSource::where('id', $item->lead->platform_from)->first();
$author = App\Models\User::where('id', $item->property->created_by)->first();
$allAgents = getAllFGRAgents();
$agentsCommission = App\Models\DealAgent::where('deal_id', $item->id)->get();
$selectedClientAgentsIds = [];
$selectedLandlordAgentsIds = [];
$tower = App\Models\API\Tower::where('id', $item->property->tower_id)->first();
$operationHistory = $item->operationHistory;
$countries = isset($additionalData) && isset($additionalData['countries']) ? $additionalData['countries'] : collect([]);
$userHasAccessToDealFiles = isset($additionalData) && isset($additionalData['userHasAccessToDealFiles']) ? $additionalData['userHasAccessToDealFiles'] : false;
$documentsSectionReadonly = isset($additionalData) && isset($additionalData['documentsSectionReadonly']) ? $additionalData['userHasAccessToDealFiles'] : '';
$userCanEditDocuments = isset($additionalData) && isset($additionalData['userCanEditDocuments']) ? $additionalData['userCanEditDocuments'] : '';
$formIsReadonly = isset($additionalData) && isset($additionalData['formIsReadonly']) ? $additionalData['formIsReadonly'] : '';
$user = auth()->user();
$userIsTeamLeaderOfOwner = $item->author->team_leader_id == $user->id;
$userIsTeamLeaderOfClosingAgent = !is_null($item->closingAgent) ? $item->closingAgent->team_leader_id == $user->id : false;
if($formIsReadonly == true) {
$readonly = true;
}

foreach ($agentsCommission as $ac) {
if($ac['agent_type'] == 'client'){
$selectedClientAgentsIds[] = $ac['agent_id'];
}else if($ac['agent_type'] == 'landlord') {
$selectedLandlordAgentsIds[] = $ac['agent_id'];
}
}
$documentOptions = app()->make(App\Services\LeadsService::class)::$documentOptions;
sort($documentOptions);
@endphp
<style>
    .vscomp-dropbox {
        z-index: 500;
    }
</style>
@if (isset($action))
<form method="POST" action={{$action}} enctype="multipart/form-data">
    {!! csrf_field() !!}
    @else
    <form>
        @endif
        <fieldset class="mx-3 py-2">
            @if (session('message.success'))
            <div class="alert alert-success">
                {{ session('message.success') }}
            </div>
            @endif
            @if($errors->any())
            <div class="alert alert-danger">Please review the fields marked with red</div>
            @endif
            @if (session('message.error'))
            <div class="alert alert-danger">
                {{ session('message.error') }}
            </div>
            @endif
            @if($item->property->trashed())
            <div class="alert alert-info">The property related to this deal has been deleted.</div>
            @endif

            @yield('form-buttons-top')
            @yield('form-top')
            <div class="row mb-3">
                <div class="col-xs-12 col-sm-6">
                    <div>
                        <label class="form-label" for="type">Deal Type</label>
                        <select id="type" name="type" class="form-select" {{$readonly ? "disabled" : ""}}>
                            <option value="RENTAL" {{ $item->type == "RENTAL" ? 'selected' : ''}}> Rental </option>
                            <option value="SALE" {{ $item->type == "SALE" ? 'selected' : ''}}> Sale </option>
                        </select>
                    </div>
                </div>
            </div>
            @if ($errors->any())
            <ul>
                @foreach ($errors->getMessages() as $field => $messages)
                <li>{{ $field }}</li>
                @endforeach
            </ul>
            @endif
            @component('crm.components.form.section-card', ['sectionId' => 'propertyDetails'])
            <h4>Property Details</h4>
            <div class="row">
                <!-- <div class="col-6">
                <div class="form-group">
                    <label class="form-label" for="property_id">Link to CRM</label>
                    @if(!empty($item) && !empty($item->property))
                    <a id="property_id" href="{{route('inventory.edit', ['id' => $item->property->asset_id])}}" target="_blank">
                        {{$item->property->ref_no}} - {{$item->property->title}}
                    </a>
                    @else
                        There is no property for this deal
                    @endif
                </div>
            </div> -->
                <input type="hidden" name="listingId" id="listingId" />
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="form-group">
                        <label class="form-label" for="">Ref No</label>
                        <input name="" type="text" class="form-control" id="listingRefNo" @if(!is_null($item->property))
                        value="{{$item->property->ref_no}}"
                        @else
                        value="-"
                        @endif
                        readonly>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="form-group">
                        <label class="form-label" for="">Property name</label>
                        <input name="" type="text" class="form-control" id="listingTitle" @if(!is_null($item->property))
                        value="{{$item->property->title}}"
                        @else
                        value="-"
                        @endif
                        readonly>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="form-group">
                        <label class="form-label" for="">Tower</label>
                        <input name="" type="text" class="form-control" id="listingTower" @if(!is_null($tower)) value="{{$tower->name}}" @else value="-" @endif readonly>
                    </div>
                </div>
                @if(!$readonly)
                <div class="col-12 mt-3 pe-3 d-flex justify-content-end gap-1">
                    <app-deal-change-listing></app-deal-change-listing>
                    <a target="_blank" href="{{route('inventory.edit', ['id' => $item->property->asset_id])}}" class="btn btn-sm btn-outline-secondary">Edit Listing in CRM</a>
                </div>
                @endif
            </div>
            @endcomponent

            @component('crm.components.form.section-card', ['sectionId' => 'landlordDetails'])
            <h4>Owner Information</h4>
            <input type="hidden" name="owner-id" value="{{!is_null($item) && !is_null($item->owner) ? $item->owner->id : '' }}">
            <div class="row">
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'owner-name',
                    'fieldLabel' => 'Full Name',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->owner : null, 'name'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    @include('crm.components.form.input-phone',
                    [
                    'fieldName' => 'mobile_1',
                    'fieldPrefix' => 'owner',
                    'fieldLabel' => 'Mobile',
                    'defaultValue' => defaultValue(!is_null($item->owner) ? $item->owner : null , 'mobile_1'),
                    'defaultPrefixValue' => defaultValue(!is_null($item) ? $item->owner : null , "prefix_mobile_1"),
                    'accessReadonly' => $readonly
                    ]
                    )
                </div>
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'owner-email_1',
                    'fieldLabel' => 'Email',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->owner : null, 'email_1'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'owner-date_of_birth',
                    'fieldLabel' => 'Date of Birth',
                    'fieldType' => 'date',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->owner : null, 'date_of_birth'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'owner-qatar_id_no',
                    'fieldLabel' => 'Qatar ID No',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->owner : null, 'qatar_id_no'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                @if(auth()->user()->hasAnyRole([\App\Models\Crm\RolesDef::OFFICE_MANAGER]) && ($errors->has('owner-mobile_1') || $errors->has('owner-email_1')))
                @php
                $showDuplicatesBtn = false;
                foreach($errors->get('owner-mobile_1') as $errMsg) {
                if(str_contains($errMsg, "belongs")) {
                $showDuplicatesBtn = true;
                }
                }
                foreach($errors->get('owner-email_1') as $errMsg) {
                if(str_contains($errMsg, "belongs")) {
                $showDuplicatesBtn = true;
                }
                }
                @endphp
                @if($showDuplicatesBtn)
                <div class="alert alert-danger text-center"><button class="btn btn-outline-secondary btn-sm" type="button" onclick="showContactDuplicatesModal('{{$item->owner->id}}')">Show duplicates</button></div>
                @endif
                @endif
            </div>
            @endcomponent


            @component('crm.components.form.section-card', ['sectionId' => 'clientDetails'])
            <h4>Client Information</h4>
            <div class="row">
                <input type="hidden" name="client-id" value="{{!is_null($item) && !is_null($item->client) ? $item->client->id : '' }}">
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'client-name',
                    'fieldLabel' => 'Full Name',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->client : null, 'name'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    @include('crm.components.form.input-phone',
                    [
                    'fieldName' => 'mobile_1',
                    'fieldPrefix' => 'client',
                    'fieldLabel' => 'Mobile',
                    'defaultValue' => defaultValue(!is_null($item->client) ? $item->client : null , 'mobile_1'),
                    'defaultPrefixValue' => defaultValue(!is_null($item) ? $item->client : null , "prefix_mobile_1"),
                    'accessReadonly' => $readonly
                    ]
                    )
                </div>
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'client-email_1',
                    'fieldLabel' => 'Email',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->client : null, 'email_1'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'client-date_of_birth',
                    'fieldLabel' => 'Date of Birth',
                    'fieldType' => 'date',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->client : null, 'date_of_birth'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-12 col-sm-4 col-lg-4">
                    @include('crm.components.form.input', [
                    'fieldName' => 'client-qatar_id_no',
                    'fieldLabel' => 'Qatar ID No',
                    'defaultValue' => defaultValue(!is_null($item) ? $item->client : null, 'qatar_id_no'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                @if(auth()->user()->hasAnyRole([\App\Models\Crm\RolesDef::OFFICE_MANAGER]) && ($errors->has('client-mobile_1') || $errors->has('client-email_1')))
                @php
                $showDuplicatesBtn = false;
                foreach($errors->get('client-mobile_1') as $errMsg) {
                if(str_contains($errMsg, "belongs")) {
                $showDuplicatesBtn = true;
                }
                }
                foreach($errors->get('client-email_1') as $errMsg) {
                if(str_contains($errMsg, "belongs")) {
                $showDuplicatesBtn = true;
                }
                }
                @endphp
                @if($showDuplicatesBtn)
                <div class="alert alert-danger text-center">
                    <button class="btn btn-outline-secondary btn-sm" type="button" onclick="showContactDuplicatesModal('{{$item->client->id}}')">Show duplicates</button>
                </div>
                @endif
                @endif
            </div>
            @endcomponent

            @component('crm.components.form.section-card', ['sectionId' => 'otherDetails'])
            <h4>Deal details</h4>
            <div class="row mb-3">
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'unit_number',
                    'fieldLabel' => 'Unit Number',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'unit_number'),
                    'accessReadonly' => 'disabled',

                    ])
                </div>
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'price',
                    'fieldLabel' => 'Price',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'price'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-xs-12 col-sm-6 mt-2">
                    @include('crm.components.form.input', [
                    'fieldName' => 'deposit',
                    'fieldLabel' => 'Deposit',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'deposit'),
                    'accessReadonly' => $readonly
                    ])
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'commission_landlord',
                    'fieldLabel' => 'Commission Landlord',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'commission_landlord'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                {{-- <div class="col-xs-12 col-sm-6">
                    <label class="form-label" for="commission_landlord_agents">Landlord Agents</label>
                    <div id="commission_landlord_agents" style="display: block"></div>
                </div> --}}
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'owner_invoice_no',
                    'fieldLabel' => 'Landlord Invoice No.',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'owner_invoice_no'),
                    'accessReadonly' => $readonly
                    ])
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'commission_client',
                    'fieldLabel' => 'Commission Client',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'commission_client'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                {{--<div class="col-xs-12 col-sm-6">
                    <label class="form-label" for="commission_client_agents">Client Agents</label>
                    <div id="commission_client_agents" style="display: block"></div>
                </div>--}}
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'client_invoice_no',
                    'fieldLabel' => 'Client Invoice No.',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'client_invoice_no'),
                    'accessReadonly' => $readonly
                    ])
                </div>
            </div>
            <div id="startDateEndDateRow" class="row mb-3 @if($item->type == 'SALE') d-none @endif">
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'start_date',
                    'fieldLabel' => 'Start Date',
                    'fieldType' => 'date',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'start_date'),
                    'accessReadonly' => $readonly
                    ])
                </div>
                <div class="col-xs-12 col-sm-6">
                    @include('crm.components.form.input', [
                    'fieldName' => 'end_date',
                    'fieldLabel' => 'End Date',
                    'fieldType' => 'date',
                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'end_date'),
                    'accessReadonly' => $readonly
                    ])
                </div>
            </div>
            <div class="row mb-3">
                {{--<div class="col-xs-12 col-sm-6">
                    <div class="form-group">
                        <label class="form-label" for="status">Status</label>
                        <input id="status"
                            name="status"
                            type="text"
                            class="form-control"
                            value="{{$item->status}}"
                {{$readonly ? "readonly" : ""}}>
            </div>
            </div> --}}
            <div class="col-xs-12 col-sm-6">
                @php
                $value = "Unknown";
                if($item->lead->platform_from && isset($sources) && !empty($sources->name)) {
                $value = $sources->name;
                }
                @endphp
                @include('crm.components.form.input', [
                'fieldName' => 'platform_from',
                'fieldLabel' => 'Source of lead',
                'defaultValue' => $value,
                'accessReadonly' => 'disabled'

                ])
            </div>
            <div class="col-xs-12 col-sm-6">
                @include('crm.components.form.input', [
                'fieldName' => '',
                'fieldLabel' => 'Agent who listed the property',
                'defaultValue' => $author->name,
                'accessReadonly' => 'disabled'
                ])
            </div>
            </div>
            <div class="row mb-3">
                {{-- <div class="col-xs-12 col-sm-6">
                    <input
                        class="form-check-input"
                        name="shared_deals"
                        type="checkbox"
                        id="sharedDeals"
                        onclick="trueCheckbox()"
                        @if(old('shared_deals', defaultValue($item, 'shared_deals')) == '1') checked @endif
                        >
                    <label class="form-check-label" for="sharedDeals">
                        Shared Deal
                    </label>
                </div> --}}
                <div class="col">
                    <div class="row" id="divSharedDealsList">
                        <div class="">
                            <div class="d-flex gap-3">
                                <div class="col-xs-12 col-sm-3"><label for="listing_agent_id" class="form-label">Listing Agent</label>
                                    <select id="listing_agent_id" name="listing_agent_id" class="form-select" disabled>
                                        <option value=""> N/A </option>
                                        @foreach (getAllFGRAgents() as $option)
                                        <option value="{{$option->id}}" {{ defaultValueRequest($item, 'listing_agent_id') == $option->id ? 'selected' : ''}}> {{$option->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-xs-12 col-sm-3">
                                    @include('crm.components.form.input', [
                                    'prefixText' => 'QAR',
                                    'fieldName' => 'listing_agent_shared_commission',
                                    'fieldLabel' => 'Shared Commission',
                                    'fieldType' => 'number',
                                    'extraAttrs' => ['step' => '0.01'],
                                    'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'listing_agent_shared_commission'),
                                    'accessReadonly' => true
                                    ])
                                </div>
                                <div class="form-check mt-4">
                                    <input
                                        class="form-check-input"
                                        name="listing_agent_cash_in"
                                        type="checkbox"
                                        value="1"
                                        @if(old('listing_agent_cash_in', defaultValue($item, 'listing_agent_cash_in' ))=='1' ) checked @endif
                                        disabled>
                                    <label class="form-check-label">
                                        Cash In
                                    </label>
                                </div>
                                <div class="">
                                    @include('crm.components.form.input', [
                                    'fieldName' => 'listing_agent_month',
                                    'fieldLabel' => 'Select month',
                                    'fieldType' =>'month',
                                    'defaultValue' => optional(\Carbon\Carbon::parse(defaultValue($item, 'listing_agent_month')))->format('Y-m'),
                                    'accessReadonly' => true
                                    ])
                                </div>
                            </div>
                            <div class="">
                                <div class="d-flex gap-3">
                                    <div class="col-xs-12 col-sm-3">
                                        <label for="referred_listing_agent_id" class="form-label">Referred Listing Agent</label>
                                        <select id="referred_listing_agent_id" name="referred_listing_agent_id" class="form-select" disabled>
                                            <option value=""> N/A </option>
                                            @foreach (getAllFGRAgents() as $option)
                                            <option value="{{$option->id}}" {{ defaultValueRequest($item, 'referred_listing_agent_id') == $option->id ? 'selected' : ''}}>{{$option->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xs-12 col-sm-3">
                                        @include('crm.components.form.input', [
                                        'prefixText' => 'QAR',
                                        'fieldName' => 'referred_listing_agent_shared_commission',
                                        'fieldLabel' => 'Shared Commission',
                                        'fieldType' => 'number',
                                        'extraAttrs' => ['step' => '0.01'],
                                        'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'referred_listing_agent_shared_commission'),
                                        'accessReadonly' => true
                                        ])
                                    </div>
                                    <div class="form-check mt-4">
                                        <input
                                            class="form-check-input"
                                            name="referred_listing_agent_cash_in"
                                            type="checkbox"
                                            value="1"
                                            @if(old('referred_listing_agent_cash_in', defaultValue($item, 'referred_listing_agent_cash_in' ))=='1' ) checked @endif
                                            disabled>
                                        <label class="form-check-label">
                                            Cash In
                                        </label>
                                    </div>
                                    <div class="">
                                        @include('crm.components.form.input', [
                                        'fieldName' => 'referred_listing_agent_month',
                                        'fieldLabel' => 'Select month',
                                        'fieldType' =>'month',
                                        'defaultValue' => optional(\Carbon\Carbon::parse(defaultValue($item, 'referred_listing_agent_month')))->format('Y-m'),
                                        'accessReadonly' => true
                                        ])
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div>
                                <div class="d-flex gap-3">
                                    <div class="col-xs-12 col-sm-3">
                                        <label for="closing_agent_id" class="form-label">Closing Agent</label>
                                        <select id="closing_agent_id" name="closing_agent_id" class="form-select" disabled>
                                            <option value=""> N/A </option>
                                            @foreach (getAllFGRAgents() as $option)
                                            <option value="{{$option->id}}" {{ !$empty && defaultValueRequest($item, 'closing_agent_id') == $option->id ? 'selected' : ''}}> {{$option->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xs-12 col-sm-3">
                                        @include('crm.components.form.input', [
                                        'prefixText' => 'QAR',
                                        'fieldName' => 'closing_agent_shared_commission',
                                        'fieldLabel' => 'Shared Commission',
                                        'fieldType' => 'number',
                                        'extraAttrs' => ['step' => '0.01'],
                                        'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'closing_agent_shared_commission'),
                                        'accessReadonly' => true
                                        ])
                                    </div>
                                    <div class="form-check mt-4">
                                        <input
                                            class="form-check-input"
                                            name="closing_agent_cash_in"
                                            type="checkbox"
                                            value="1"
                                            @if(old('closing_agent_cash_in', defaultValue($item, 'closing_agent_cash_in' ))=='1' ) checked @endif
                                            disabled>
                                        <label class="form-check-label">
                                            Cash In
                                        </label>
                                    </div>
                                    <div class="">
                                        @include('crm.components.form.input', [
                                        'fieldName' => 'closing_agent_month',
                                        'fieldLabel' => 'Select month',
                                        'fieldType' =>'month',
                                        'defaultValue' => optional(\Carbon\Carbon::parse(defaultValue($item, 'closing_agent_month')))->format('Y-m'),
                                        'accessReadonly' => true
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="">
                                <div class="d-flex gap-3">
                                    <div class="col-xs-12 col-sm-3">
                                        <label for="referred_closing_agent_id" class="form-label">Referred Closing Agent</label>
                                        <select id="referred_closing_agent_id" name="referred_closing_agent_id" class="form-select" disabled>
                                            <option value=""> N/A </option>
                                            @foreach (getAllFGRAgents() as $option)
                                            <option value="{{$option->id}}" {{ !$empty && (defaultValueRequest($item, 'referred_closing_agent_id') == $option->id) ? 'selected' : ''}}> {{$option->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xs-12 col-sm-3">
                                        @include('crm.components.form.input', [
                                        'prefixText' => 'QAR',
                                        'fieldName' => 'referred_closing_agent_shared_commission',
                                        'fieldLabel' => 'Shared Commission',
                                        'fieldType' => 'number',
                                        'extraAttrs' => ['step' => '0.01'],
                                        'defaultValue' => defaultValue(!is_null($item) ? $item : null, 'referred_closing_agent_shared_commission'),
                                        'accessReadonly' => true
                                        ])
                                    </div>
                                    <div class="form-check mt-4">
                                        <input
                                            class="form-check-input"
                                            name="referred_closing_agent_cash_in"
                                            type="checkbox"
                                            value="1"
                                            @if(old('referred_closing_agent_cash_in', defaultValue($item, 'referred_closing_agent_cash_in' ))=='1' ) checked @endif
                                            disabled>
                                        <label class="form-check-label">
                                            Cash In
                                        </label>
                                    </div>
                                    <div class="">
                                        @include('crm.components.form.input', [
                                        'fieldName' => 'referred_closing_agent_month',
                                        'fieldLabel' => 'Select month',
                                        'fieldType' =>'month',
                                        'defaultValue' => optional(\Carbon\Carbon::parse(defaultValue($item, 'referred_closing_agent_month')))->format('Y-m'),
                                        'accessReadonly' => true
                                        ])
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endcomponent

            @if(auth()->user()->hasAnyRole([\App\Models\Crm\RolesDef::OFFICE_MANAGER, \App\Models\Crm\RolesDef::ACCOUNTANT]) || $userIsTeamLeaderOfOwner || $userIsTeamLeaderOfClosingAgent || $user->id == $item->created_by || $userHasAccessToDealFiles)
            <div class="row mb-3">
                @component('crm.components.form.section-card', ['sectionId' => 'documents'])
                <h4>Documents</h4>
                <div class="col-12 mt-4">
                    <!-- <label class="form-label">Documents</label> -->
                    <table class="table table-bordered table-condensed">
                        <tr>
                            <th>Preview</th>
                            <th>Options</th>
                            <th>Last update date</th>
                            <th>Download</th>
                            <th>Sent by email</th>
                            <th>&nbsp;</th>
                        </tr>
                        @if(count($item->forms ?? []) > 0)
                        @foreach($item->forms as $form)
                        @php
                        $documentsEncode = json_decode(json_encode($item->documents_options),true);
                        $fileOption = "-";
                        if(!empty($form->option)) {
                        $documentOptionArr = array_filter($documentOptions, function($opt) use($form) {
                        return $opt['value'] == $form->option;
                        });
                        if(count($documentOptionArr) > 0) {
                        $vals = array_values($documentOptionArr);
                        $fileOption = $vals[0]['text'];
                        }
                        }
                        $fileExtension = pathinfo($form->path, PATHINFO_EXTENSION);
                        $isImage = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif']);
                        @endphp
                        <tr>
                            <td style="max-width:200px; word-wrap:break-word;">
                            @if ($isImage)
                                <a href="{{ route('deal.forms.preview', ['id' => $item->id, 'filePath' => $form->path]) }}"
                                class="glightbox"
                                data-type="image">
                                {{$form->title}}
                                </a>
                            @else
                                <a href="javascript:void(0);"
                                class="preview-link"
                                data-url="{{ route('deal.forms.preview', ['id' => $item->id, 'filePath' => $form->path]) }}">
                                {{$form->title}}
                                </a>
                            @endif
                            </td>
                            <td>{{$fileOption}}</td>
                            <td>{{$form->aTime ?? '-'}}</td>
                            <td style="max-width:200px; word-wrap:break-word;"><a href="{{ route('deal.forms.download', ['id' => $item->id, 'filePath' => $form->path]) }}">Download</a></td>
                            <td>{{property_exists($form, 'sentAt') ? (is_null($form->sentAt) ? 'No' : 'Yes ('.$form->sentAt.')') : 'Yes'}}</td>
                            <td><label><input name="deal-forms-remove[]" type="checkbox" value="{{$loop->index}}" {{$readonly ? 'disabled' : ''}}> Mark for removal</label></td>
                        </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="5">
                                <div>No documents uploaded</div>
                            </td>
                        </tr>
                        @endif
                    </table>
                    <div id="formsFilesContainer" class="d-flex flex-column gap-1"></div>
                    <div class="p-1 d-flex">
                        <button
                            @if(!$documentsSectionReadonly) ''
                            @elseif($readonly)
                            disabled
                            @else ''
                            @endif
                            type="button" id="formsAddFilesBtn" class="btn btn-secondary btn-sm" {{$readonly && !$userCanEditDocuments ? 'disabled' : ''}}>Add file</button>
                    </div>
                    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <iframe id="previewIframe" src="" frameborder="0" style="width: 100%; height: 650px;"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endcomponent
            </div>
            @endif

            <h4>Comments</h4>
            <div class="row mb-4">
                <div class="col-12">
                    <textarea name="comments" class="form-control" id="" cols="30" rows="5" disabled> {{$item->comments ? $item->comments : 'No comments'}}</textarea>
                </div>
            </div>

            <h4>Remarks</h4>
            <div class="row">
                <div class="col-12">
                    <div style="max-height:300px; overflow-y: auto;">
                        @foreach($operationHistory as $remark)
                        <article class="pb-2">
                            <aside>{{ $remark->content }}</aside>
                            <footer><small>{{ $remark->created_at->format('d M Y H:i') }} - <cite title="Source Title">{{ $remark->author ? $remark->author->name : '-' }}</cite></small>
                            </footer>
                        </article>
                        @endforeach
                    </div>
                    <div class="mt-3">
                        <label class="form-label" for="operation-history">Remarks</label>
                        <textarea {{$readonly ? 'disabled' : ''}} name="operation-history" class="form-control" id="" cols="30" rows="5"></textarea>
                    </div>
                </div>
            </div>
            @yield('form-buttons-bottom')
        </fieldset>
        <contact-duplicates-modal></contact-duplicates-modal>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>
    <script type="text/javascript" src="{{asset('/js/virtual-select.min.js')}}"></script>
    <script>
        const dealAgentsMap = JSON.parse('{!! addslashes($allAgents->mapWithKeys(function($v) { return [$v->id => $v->name];  })->toJSON()) !!}');
        const dealAgents = Object.keys(dealAgentsMap).map((key) => ({
            label: dealAgentsMap[key],
            value: key
        }))
        const selectedClientAgents = JSON.parse('{{ empty(old('
            agent_id_client ')) ? (!empty($agentsCommission) ? json_encode($selectedClientAgentsIds) : ' []
            ') : ' ['.old('
                agent_id_client ').'
            ]
            '}}');
        const selectedLandlordAgents = JSON.parse('{{ empty(old('
            agent_id_landlord ')) ? (!empty($agentsCommission) ? json_encode($selectedLandlordAgentsIds) : ' []
            ') : ' ['.old('
                agent_id_landlord ').'
            ]
            '}}');
        const closingAgent = document.getElementById('closing_agent_id');
        const listingAgent = document.getElementById('listing_agent_id');

        const dealClientAgentsConfig = {
            ele: '#commission_client_agents',
            options: dealAgents,
            search: true,
            multiple: true,
            name: 'agent_id_client',
            placeholder: 'Please select',
            selectedValue: selectedClientAgents
        };

        const dealLandlordAgentsConfig = {
            ele: '#commission_landlord_agents',
            options: dealAgents,
            search: true,
            multiple: true,
            name: 'agent_id_landlord',
            placeholder: 'Please select',
            selectedValue: selectedLandlordAgents
        };

        const configs = [
            dealClientAgentsConfig,
            dealLandlordAgentsConfig,
        ];

        configs.forEach(config => {
            if (document.querySelector(config.ele)) {
                VirtualSelect.init(config);
            }
        })

        class FileUploader {
            filesContainer = document.querySelector('#formsFilesContainer');

            addFile() {
                const fileInput = document.createElement('input');
                fileInput.setAttribute('type', 'file');
                fileInput.setAttribute('name', 'deal-forms[]');
                fileInput.classList.add('form-control', 'form-control-sm');

                const fileWrapperDiv = document.createElement('div');
                fileWrapperDiv.classList.add('d-flex', 'align-items-center')

                const wrapperDiv = document.createElement('div');
                wrapperDiv.classList.add('p-1', 'd-flex', 'gap-1');

                const div2 = document.createElement('div');
                const optionsSelect = document.createElement('select');
                optionsSelect.setAttribute('name', 'documents_options[]');
                optionsSelect.classList.add('form-select', 'form-select-sm');
                optionsSelect.style.width = "250px";
                optionsSelect.style.maxWidth = "250px";
                const opt = document.createElement('option');
                opt.text = "Select a option";
                opt.value = "";
                optionsSelect.add(opt);
                const optQID = document.createElement('option');
                optQID.text = "QID";
                optQID.value = "qid";
                optionsSelect.add(optQID);

                const options = JSON.parse('{!! json_encode($documentOptions); !!}')
                // [{
                //         text: "Contract",
                //         value: "contract",
                //         disabled: true
                //     },
                //     {
                //         text: "Lease Agreement",
                //         value: "leaseAgreement"
                //     },
                //     {
                //         text: "Sales and Purchase Agreement (SPA)",
                //         value: "SalesAndPurchaseAgreement"
                //     },
                //     {
                //         text: "Passport",
                //         value: "passport"
                //     },
                //     {
                //         text: "PVF",
                //         value: "pvf"
                //     },
                //     {
                //         text: "Breakdown Payment",
                //         value: "breakdown"
                //     },
                //     {
                //         text: "Copy of Payments",
                //         value: "copy"
                //     },
                //     {
                //         text: "Salary Certificate",
                //         value: "salary"
                //     },
                //     {
                //         text: "Credit Bureau",
                //         value: "credit"
                //     },
                //     {
                //         text: "Booking Form",
                //         value: "bookingForm"
                //     },
                //     {
                //         text: "Receipt",
                //         value: "receipt"
                //     },
                //     {
                //         text: "Handover Form",
                //         value: "handoverForm"
                //     },
                //     {
                //         text: "Trading License",
                //         value: "tradingLicense"
                //     },
                //     {
                //         text: "Company Registration",
                //         value: "companyRegistration"
                //     }
                // ];

                options.forEach(optionData => {
                    const option = document.createElement('option');
                    option.text = optionData.text;
                    option.value = optionData.value;

                    if (optionData.disabled) {
                        option.disabled = true;
                    }

                    optionsSelect.add(option);
                });

                div2.classList.add("p-2")
                div2.appendChild(optionsSelect)

                fileWrapperDiv.appendChild(fileInput);
                wrapperDiv.appendChild(fileWrapperDiv);
                wrapperDiv.appendChild(div2);
                this.filesContainer.appendChild(wrapperDiv);
            }
        }

        const handleTypeChange = () => {
            const dealType = document.querySelector('#type').value;
            const startDateInput = document.querySelector('#start_date')
            const startDateEndDateRow = document.querySelector('#startDateEndDateRow');
            if (dealType.toLowerCase() == 'sale') {
                startDateEndDateRow.classList.add('d-none');
                startDateInput.removeAttribute('required');
            } else {
                startDateEndDateRow.classList.remove('d-none');
                startDateInput.setAttribute('required', '');
            }
        }

        window.addEventListener('load', () => {
            const fu = new FileUploader();
            const addFileBtn = document.querySelector('#formsAddFilesBtn');
            addFileBtn.addEventListener('click', () => fu.addFile());

            const selectSelection = document.querySelectorAll('.input-group .dropdown-menu')
            for (const selection of selectSelection) {
                selection.addEventListener('click', (evt) => {
                    if (evt.target.classList.contains('countryLi')) {
                        const node = evt.target;
                        const countryName = node.getAttribute('data-countryname')
                        const countryPrefix = node.getAttribute('data-countryprefix')
                        const countryCode = node.getAttribute('data-countrycode')
                        const captionContainer = node.closest('.fg-dropdown').querySelector('.dropdown__caption')
                        const hiddenInput = node.closest('.fg-dropdown').querySelector('.phonePrefixInput')
                        hiddenInput.value = countryPrefix;
                        captionContainer.innerHTML = `<span class="flag flag-${countryCode}"></span> ${countryPrefix}`;
                    }
                })
            }


            const dealTypeSelector = document.querySelector('#type');
            dealTypeSelector.addEventListener('change', handleTypeChange);

            const previewLinks = document.querySelectorAll('.preview-link');
            const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
            const previewIframe = document.getElementById('previewIframe');
            previewLinks.forEach(link => {
                link.addEventListener('click', () => {
                    const url = link.getAttribute('data-url');
                    previewIframe.src = url;
                    previewModal.show();
                });
            });
            document.getElementById('previewModal').addEventListener('hidden.bs.modal', () => {
                previewIframe.src = '';
            });
            GLightbox({
                selector: '.glightbox',
                openEffect: 'zoom',
                closeEffect: 'fade',
            });
        });

        closingAgent.setAttribute('required', '');
        listingAgent.setAttribute('required', '');

        function handleDealListingChange(listingObject) {
            if (!!listingObject) {
                document.querySelector('#listingRefNo').value = listingObject.refNo;
                document.querySelector('#listingTitle').value = listingObject.listingTitle;
                document.querySelector('#listingTower').value = `${listingObject.towerName ? listingObject.towerName : ''} ${listingObject.geographyName ? listingObject.geographyName : ''}`;
                document.querySelector('#listingId').value = listingObject.id;
            }
        }
    </script>
    @section('head-css')
    <link rel="stylesheet" href="{{asset('/css/crm-virtual-select.min.css')}}">
    <link href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" rel="stylesheet">
    <style>
        .vscomp-ele {
            max-width: 100%;
        }

        .vscomp-wrapper.opened {
            z-index: 100;
        }
    </style>
    @endsection

    @section('body-js')
    <script type="text/javascript" src="{{mix('/js/contact-duplicates-modal.js')}}" async defer></script>
    <script type="text/javascript" src="{{mix('/js/deal-change-listing.js')}}" async defer></script>
    @endsection