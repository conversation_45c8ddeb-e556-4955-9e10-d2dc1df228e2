@extends('crm.layout')

@section('head-css')

<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/3.1.9/css/fixedHeader.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="/css/virtual-select.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<style>
    .vscomp-ele {
        max-width: 100%;
        display: block;
        width: 100%;
    }

    .truncate {
        max-width: 50px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    td.details-control {
        background: url('{{asset(' /images/datatable/details_open.png')}}') no-repeat center center;
        cursor: pointer;
    }

    tr.shown td.details-control {
        background: url('{{asset(' /images/datatable/details_close.png')}}') no-repeat center center;
    }

    td.details-control {
        width: 1em;
        height: 1em;
    }

    td.details-control {
        padding: 12px;
    }

    tr.details td.details-control:before {
        content: "-";
        background-color: #d33333;
    }

    td.details-control:before {
        left: 5px;
        height: 1em;
        width: 1em;
        margin-top: -9px;
        display: block;
        position: absolute;
        color: white;
        border: .15em solid white;
        border-radius: 1em;
        box-shadow: 0 0 0.2em #444;
        box-sizing: content-box;
        text-align: center;
        text-indent: 0 !important;
        font-family: "Courier New", Courier, monospace;
        line-height: 1em;
        content: "+";
        background-color: #0d6efd;
        cursor: pointer;
    }

    tr.child td.child {
        background-color: #f1f5fd;
    }

    tr.child td.child ul.dtr-details {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        grid-column-gap: 5px;
        grid-row-gap: 5px;
    }

    tr.child td.child ul.dtr-details li {
        display: flex;
    }

    tr.child td.child ul.dtr-details li span:first-child {
        display: inline-block;
        margin-right: 10px;
    }

    tr.child td.child ul.dtr-details li span:first-child:after {
        content: ':';
    }

    tr.dealsRow--statusPending .statusBox {
        background-color: #fd7e14;
    }

    tr.dealsRow--statusCashed .statusBox {
        background-color: #4dd4ac;
    }

    .dealsWrapper--statusPending .simpleStatusBox {
        background-color: #fd7e14;
    }

    .dealsWrapper--statusCashed .simpleStatusBox {
        background-color: #4dd4ac;
    }

    tr.dealsRow--statusPendingCashed .statusBox {
        background: linear-gradient(to right, #fd7e14 50%, #4dd4ac 50%);
        border: none;
        padding: 10px 20px;
        cursor: pointer;
    }

    tr.dealsRow--statusCashedPending .statusBox {
        background: linear-gradient(to right, #4dd4ac 50%, #fd7e14 50%);
        border: none;
        padding: 10px 20px;
        cursor: pointer;
    }
</style>
@endsection

@section('content')
<div class="px-md-2">

    @if(request()->session()->has('success') || request()->session()->has('info'))
    <div class="messages-container pt-4">
        @include('crm.components.session-alerts')
    </div>
    @endif
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Invoices</h1>
        <!-- <div class="btn-toolbar mb-2 mb-md-0 gap-2">
            <div class="btn-group btn-group-sm me-2" role="group">
                <a href="{{route('crm.accountant.index')}}" type="button" class="btn btn-sm btn-outline-secondary">Invoices</a>
            </div>
        </div> -->
    </div>

    <div class="row">
        <div class="col-12 pb-2">
            <a role="button" class="btn btn-sm btn-outline-secondary" href="javascript:toggleExtraFilters()">
                <span data-feather="chevrons-right"></span>
                Filters
            </a>
        </div>
        <div class="card card-body p-2 m-2 bg-light d-none" id="extraFiltersPanel">
            @include('crm.accountant.filters')
        </div>
    </div>

    <div class="d-flex justify-content-between align-items-center mb-2">
        <div class="d-flex justify-content-start align-items-center mb-2">
            <div class="input-group input-group-sm" style="width: auto; max-width: 300px;">
                <span class="input-group-text">With selection (<span id="selectedCount">0</span>):</span>
                <select id="bulkAction" class="form-select form-select-sm" disabled>
                    <option value="">Select an action</option>
                    <option value="invoce_export">Export to Excel</option>
                </select>
            </div>
            <button id="applyAction" class="btn btn-primary btn-sm ms-2" disabled>Apply</button>
        </div>
        <div>
            <div class="border rounded p-2" style="min-width: 250px; max-width: 400px">
                <div>Cash in (this month): <strong><span class="cash-in">-</span></strong></div>
                <div>Pending: <strong><span class="cash-pending">-</span></strong></div>
                <div>To collect: <strong><span class="cash-to-collect">-</span></strong></div>
            </div>
        </div>
    </div>

    <div class="col mt-2">
        <table class="table table-striped dataTable table-sm margin-top nowrap margin-top" id="deals-invoices-list">
            <thead>
                <tr>
                    <th></th>
                    <th><input type="checkbox" id="selectAll"></th>
                    <th>Ref No.</th>
                    <th>Tower Name</th>
                    <th>Unit No</th>
                    <th>Landlord</th>
                    <th>Landlord Invoice No.</th>
                    <th>Landlord Cash In<br><small class="text-muted">(QAR)</small></th>
                    <th>Landlord Pending<br><small class="text-muted">(QAR)</small></th>
                    <th>Landlord Total<br><small class="text-muted">(QAR)</small></th>
                    <th>Listing Agent</th>
                    <th>Client</th>
                    <th>Client Invoice No.</th>
                    <th>Client Cash In<br><small class="text-muted">(QAR)</small></th>
                    <th>Client Pending<br><small class="text-muted">(QAR)</small></th>
                    <th>Client Total<br><small class="text-muted">(QAR)</small></th>
                    <th>Closing Agent</th>
                    <th>Source</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>
</div>
@endsection

@section('body-js')

<script type="text/javascript" src="{{asset('/js/crm/libs/jquery.ba-throttle-debounce.min.js')}}"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.0.1/js/bootstrap.bundle.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.js"></script>


<script src="{{mix('js/crm/datatables-action-menu.js')}}"></script>
<script type="text/javascript" src="{{asset('/js/crm/libs/datatables-util.js')}}"></script>
<script type="text/javascript" src="{{asset('/js/crm/deals-invoices-table.js').'?t='.time()}}"></script>

<script>
    const toggleExtraFilters = () => {
        const classList = document.querySelector('#extraFiltersPanel').classList;
        if (classList.contains('d-none')) {
            classList.remove('d-none');
        } else {
            classList.add('d-none');
        }
    }

    window.addEventListener('load', () => {
        request('/crm/accountant/summary')
            .then(data => {
                if (data.cash_in !== undefined) {
                    document.querySelector('.cash-in').textContent = data.cash_in;
                }
                if (data.pending !== undefined) {
                    document.querySelector('.cash-pending').textContent = data.pending;
                }
                if (data.to_collect !== undefined) {
                    document.querySelector('.cash-to-collect').textContent = data.to_collect;
                }
            })
    });
</script>
@endsection