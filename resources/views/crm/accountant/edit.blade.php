@extends('crm.layout')

@php
$documentOptions = App\Models\InvoiceDocument::getDocumentOptions();
@endphp

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Edit Deal - {{ sprintf('D-%06d', $deal->id) }}</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('crm.accountant.index') }}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Invoices
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Deal Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Deal Reference:</strong> {{ sprintf('D-%06d', $deal->id) }}</p>
                            <p><strong>Unit Number:</strong> {{ $deal->unit_number }}</p>
                            <p><strong>Landlord:</strong> {{ $deal->owner->name ?? 'N/A' }}</p>
                            <p><strong>Client:</strong> {{ $deal->client->name ?? 'N/A' }}</p>
                            <p><strong>Listing Agent:</strong> {{ $deal->listingAgent->name ?? 'N/A' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Landlord Commission:</strong> {{ $deal->commission_landlord }}</p>
                            <p><strong>Landlord Invoice No:</strong> {{ $deal->landlord_invoice_no ?? '-' }}</p>
                            <p><strong>Client Commission:</strong> {{ $deal->commission_client }}</p>
                            <p><strong>Client Invoice No:</strong> {{ $deal->client_invoice_no ?? '-' }}</p>
                            <p><strong>Closing Agent:</strong> {{ $deal->closingAgent->name ?? 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form action="{{ route('crm.accountant.update', ['id' => $deal->id]) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('POST')
        <!-- Debug info to check form submission -->
        <input type="hidden" name="debug_timestamp" value="{{ time() }}">

        <!-- Landlord Invoice Section -->
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Landlord Invoice Details</h5>
                    </div>
                    <div class="card-body">
                        <!-- Invoice Information Section (Always Visible) -->
                        <div class="mb-4">
                            <h6 class="mb-3"><i class="bi bi-receipt me-2"></i>Invoice Information</h6>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label for="landlord_invoice_number" class="form-label">Invoice No</label>
                                        <input type="text" class="form-control" id="landlord_invoice_number" name="landlord_invoice_number" value="{{ $landlordInvoiceDetails->ref_no ?? '' }}" autocomplete="off">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label for="landlord_invoice_amount" class="form-label">Amount</label>
                                        <input type="text" class="form-control" id="landlord_invoice_amount" name="landlord_invoice_amount" value="{{ $landlordInvoiceDetails->amount ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label for="landlord_invoice_date" class="form-label">Issue Date</label>
                                        <input type="date" class="form-control" id="landlord_invoice_date" name="landlord_invoice_date" value="{{ $landlordInvoiceDetails->issue_date ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3 {{$errors->has('landlord_document') ? 'is-invalid' : ''}}">
                                        <label for="landlord_document" class="form-label">Document Invoice</label>
                                        @if(!is_null($landlordInvoiceDetails) && !empty($landlordInvoiceDetails->documents_path))
                                            <div class="mb-2">
                                                <a href="{{route('crm.accountant.landlord.download-invoice', ['id' => $landlordInvoiceDetails->deal->id, 'inoviceId' => $landlordInvoiceDetails->id])}}"
                                                   class="link-primary">{{$landlordInvoiceDetails->documents_title ?? 'Document'}}</a>
                                            </div>
                                        @endif
                                        <input class="form-control form-control" id="landlord_document" name="landlord_document"
                                               type="file">
                                        @if(!is_null($landlordInvoiceDetails) && !empty($landlordInvoiceDetails->documents_path))
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="landlord_document_remove"
                                                       value="1" id="landlord_document_remove">
                                                <label class="form-check-label" for="landlord_document_remove">
                                                    Remove file
                                                </label>
                                            </div>
                                        @endif
                                    </div>
                                    @if($errors->has('landlord_document'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('landlord_document') }}
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Invoice Actions</label>
                                        <div class="d-flex gap-2">
                                            @if($landlordInvoiceDetails && $landlordInvoiceDetails->id)
                                                <a href="{{ route('deal.invoice.view', ['id' => $deal->id, 'invoiceId' => $landlordInvoiceDetails->id]) }}"
                                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye"></i> View Invoice
                                                </a>
                                                <a href="{{ route('deal.invoice.download', ['id' => $deal->id, 'invoiceId' => $landlordInvoiceDetails->id]) }}"
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="bi bi-download"></i> Download
                                                </a>
                                            @else
                                                <span class="text-muted small">No invoice available</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Documents Management Section (Collapsible) -->
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <h6 class="mb-0 me-2">
                                    <i class="bi bi-folder me-2"></i>Documents Management
                                    <span class="badge bg-secondary ms-2">{{ $landlordDocuments ? $landlordDocuments->count() : 0 }}</span>
                                </h6>
                                <button class="btn btn-sm accordion-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#landlordDocumentsSection" aria-expanded="false" aria-controls="landlordDocumentsSection">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse" id="landlordDocumentsSection">
                                <div class="p-3">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Preview</th>
                                                <th>Option</th>
                                                <th>Observations</th>
                                                <th>Uploaded at</th>
                                                <th>Download</th>
                                                <th>Mark for removal</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if($landlordDocuments && $landlordDocuments->count() > 0)
                                                @foreach($landlordDocuments as $index => $document)
                                                    <tr>
                                                        <td>
                                                            @if(in_array(strtolower(pathinfo($document->file_name, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']))
                                                                <a href="{{ route('crm.accountant.landlord.preview-document', ['id' => $deal->id, 'documentId' => $document->id]) }}"
                                                                   class="preview-link" data-url="{{ route('crm.accountant.landlord.preview-document', ['id' => $deal->id, 'documentId' => $document->id]) }}">
                                                                    <i class="bi bi-eye"></i> Preview
                                                                </a>
                                                            @else
                                                                <span class="text-muted">No preview</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @php
                                                                $documentTypeLabel = '';
                                                                if (!empty($document->document_type)) {
                                                                    $documentOption = collect($documentOptions)->firstWhere('value', $document->document_type);
                                                                    $documentTypeLabel = $documentOption['text'] ?? ucfirst($document->document_type);
                                                                }
                                                            @endphp
                                                            {{ $documentTypeLabel ?: '-' }}
                                                            @if($document->document_type === 'receipt' && $document->receipt_no)
                                                                <br><small class="text-muted">Receipt no: {{ $document->receipt_no }}</small>
                                                            @elseif($document->document_type === 'cheque' && $document->cheque_number)
                                                                <br><small class="text-muted">Cheque number: {{ $document->cheque_number }}</small>
                                                            @endif
                                                        </td>
                                                        <td style="max-width: 300px; word-wrap: break-word;">{{ $document->observations ?: '-' }}</td>
                                                        <td>{{ $document->upload_date ? $document->upload_date->format('d.m.Y H:i') : '-' }}</td>
                                                        <td style="max-width: 300px; word-wrap: break-word;">
                                                            <a href="{{ route('crm.accountant.landlord.download-document', ['id' => $deal->id, 'documentId' => $document->id]) }}"
                                                               class="text-decoration-none">
                                                                <i class="bi bi-download me-1"></i>{{ $document->file_name }}
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <label>
                                                                <input name="landlord_documents_remove[]" type="checkbox" value="{{ $document->id }}">
                                                                Mark for removal
                                                            </label>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="6" class="text-center">No documents uploaded</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Dynamic file upload container -->
                                <div id="landlordDocumentsContainer" class="d-flex flex-column gap-1 mt-3"></div>

                                <!-- Add file button -->
                                <div class="p-1 d-flex mt-2">
                                    <button type="button" id="landlordAddDocumentBtn" class="btn btn-secondary btn-sm">
                                        <i class="bi bi-plus"></i> Add file
                                    </button>
                                </div>
                            </div>
                        </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payments Management Section (Collapsible) -->
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <h6 class="mb-0 me-2">
                                    <i class="bi bi-credit-card me-2"></i>Payments Management
                                    <span class="badge bg-secondary ms-2">{{ $landlordPayments ? $landlordPayments->count() : 0 }}</span>
                                </h6>
                                <button class="btn btn-sm accordion-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#landlordPaymentsSection" aria-expanded="false" aria-controls="landlordPaymentsSection">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse" id="landlordPaymentsSection">
                                <div class="p-3">
                                <!-- Landlord Payment Documents Table -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Invoice No</th>
                                                        <th>Amount</th>
                                                        <th>Issue Date</th>
                                                        <th>Payment Type</th>
                                                        <th>Cashed in</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @if($landlordPayments->count() > 0)
                                                        @foreach($landlordPayments as $payment)
                                                            @php
                                                                $paymentDocs = [];
                                                                if (!empty($payment->payment_documents)) {
                                                                    $docs = is_string($payment->payment_documents)
                                                                        ? json_decode($payment->payment_documents, true)
                                                                        : $payment->payment_documents;

                                                                    if (is_array($docs) && count($docs) > 0) {
                                                                        foreach ($docs as $doc) {
                                                                            $docId = md5($doc['file_path'] ?? '');
                                                                            $paymentDocs[] = [
                                                                                'id' => $docId,
                                                                                'payment_id' => $payment->id,
                                                                                'file_name' => $doc['file_name'] ?? 'Unknown',
                                                                                'file_path' => $doc['file_path'] ?? '',
                                                                                'upload_date' => $doc['upload_date'] ?? now()->format('Y-m-d H:i:s')
                                                                            ];
                                                                        }
                                                                    }
                                                                }
                                                            @endphp

                                                            <tr>
                                                                <td>{{ $landlordInvoiceDetails->ref_no ?? 'N/A' }}</td>
                                                                <td>{{ $payment->amount ?? 'N/A' }}</td>
                                                                <td>{{ $payment->payment_date ? date('d.m.Y', strtotime($payment->payment_date)) : 'N/A' }}</td>
                                                                <td>
                                                                    @if(isset($payment->payment_type) && isset($paymentMethods[$payment->payment_type]))
                                                                        {{ $paymentMethods[$payment->payment_type] }}
                                                                    @else
                                                                        {{ $payment->payment_type ?? 'N/A' }}
                                                                    @endif
                                                                </td>
                                                                <td>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input cashed-in-checkbox"
                                                                               type="checkbox"
                                                                               data-payment-id="{{ $payment->id }}"
                                                                               {{ $payment->cashed_in ? 'checked' : '' }}>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div class="d-flex justify-content-start">
                                                                        <button type="button" class="btn btn-outline-danger delete-document-btn" data-deal-id="{{ $deal->id }}" data-document-id="{{ $payment->id }}">
                                                                            <i class="bi bi-trash"></i> Delete
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    @else
                                                        <tr>
                                                            <td colspan="6" class="text-center">No payment documents found</td>
                                                        </tr>
                                                    @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Landlord Payment Form -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <h6 class="mb-3">Add Payment</h6>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="landlord_amount" class="form-label">Amount</label>
                                                    <input type="number" step="0.01" class="form-control" id="landlord_amount" name="landlord_amount" value="">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="landlord_payment_date" class="form-label">Payment Issue Date</label>
                                                    <input type="date" class="form-control" id="landlord_payment_date" name="landlord_payment_date" value="">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="landlord_payment_type" class="form-label">Payment Type</label>
                                                    <select class="form-select" id="landlord_payment_type" name="landlord_payment_type">
                                                        <option value="">Select payment type</option>
                                                        @foreach($paymentMethods as $value => $label)
                                                            <option value="{{ $value }}">{{ $label }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Invoice Section -->
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Client Invoice Details</h5>
                    </div>
                    <div class="card-body">
                        <!-- Invoice Information Section (Always Visible) -->
                        <div class="mb-4">
                            <h6 class="mb-3"><i class="bi bi-receipt me-2"></i>Invoice Information</h6>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label for="client_invoice_number" class="form-label">Invoice No</label>
                                        <input type="text" class="form-control" id="client_invoice_number" name="client_invoice_number" value="{{ $clientInvoiceDetails->ref_no ?? '' }}" autocomplete="off">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label for="client_invoice_amount" class="form-label">Amount</label>
                                        <input type="text" class="form-control" id="client_invoice_amount" name="client_invoice_amount" value="{{ $clientInvoiceDetails->amount ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label for="client_invoice_date" class="form-label">Issue Date</label>
                                        <input type="date" class="form-control" id="client_invoice_date" name="client_invoice_date" value="{{ $clientInvoiceDetails->issue_date ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3 {{$errors->has('client_document') ? 'is-invalid' : ''}}">
                                        <label for="client_document" class="form-label">Document Invoice</label>
                                        @if(!is_null($clientInvoiceDetails) && !empty($clientInvoiceDetails->documents_path))
                                            <div class="mb-2">
                                                <a href="{{route('crm.accountant.client.download-invoice', ['id' => $landlordInvoiceDetails->id, 'inoviceId' => $landlordInvoiceDetails->id])}}"
                                                   class="link-primary">{{$clientInvoiceDetails->documents_title ?? 'Document'}}</a>
                                            </div>
                                        @endif
                                        <input class="form-control form-control" id="client_document" name="client_document"
                                               type="file">
                                        @if(!is_null($clientInvoiceDetails) && !empty($clientInvoiceDetails->documents_path))
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="client_document_remove"
                                                       value="1" id="client_document_remove">
                                                <label class="form-check-label" for="client_document_remove">
                                                    Remove file
                                                </label>
                                            </div>
                                        @endif
                                    </div>
                                    @if($errors->has('client_document'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('client_document') }}
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Invoice Actions</label>
                                        <div class="d-flex gap-2">
                                            @if($clientInvoiceDetails && $clientInvoiceDetails->id)
                                                <a href="{{ route('deal.invoice.view', ['id' => $deal->id, 'invoiceId' => $clientInvoiceDetails->id]) }}"
                                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye"></i> View Invoice
                                                </a>
                                                <a href="{{ route('deal.invoice.download', ['id' => $deal->id, 'invoiceId' => $clientInvoiceDetails->id]) }}"
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="bi bi-download"></i> Download
                                                </a>
                                            @else
                                                <span class="text-muted small">No invoice available</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Documents Management Section (Collapsible) -->
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <h6 class="mb-0 me-2">
                                    <i class="bi bi-folder me-2"></i>Documents Management
                                    <span class="badge bg-secondary ms-2">{{ $clientDocuments ? $clientDocuments->count() : 0 }}</span>
                                </h6>
                                <button class="btn btn-sm accordion-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#clientDocumentsSection" aria-expanded="false" aria-controls="clientDocumentsSection">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse" id="clientDocumentsSection">
                                <div class="p-3">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Preview</th>
                                                <th>Option</th>
                                                <th>Observations</th>
                                                <th>Uploaded at</th>
                                                <th>Download</th>
                                                <th>Mark for removal</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if($clientDocuments && $clientDocuments->count() > 0)
                                                @foreach($clientDocuments as $index => $document)
                                                    <tr>
                                                        <td>
                                                            @if(in_array(strtolower(pathinfo($document->file_name, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']))
                                                                <a href="{{ route('crm.accountant.client.preview-document', ['id' => $deal->id, 'documentId' => $document->id]) }}"
                                                                   class="preview-link" data-url="{{ route('crm.accountant.client.preview-document', ['id' => $deal->id, 'documentId' => $document->id]) }}">
                                                                    <i class="bi bi-eye"></i> Preview
                                                                </a>
                                                            @else
                                                                <span class="text-muted">No preview</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @php
                                                                $documentTypeLabel = '';
                                                                if (!empty($document->document_type)) {
                                                                    $documentOption = collect($documentOptions)->firstWhere('value', $document->document_type);
                                                                    $documentTypeLabel = $documentOption['text'] ?? ucfirst($document->document_type);
                                                                }
                                                            @endphp
                                                            {{ $documentTypeLabel ?: '-' }}
                                                            @if($document->document_type === 'receipt' && $document->receipt_no)
                                                                <br><small class="text-muted">Receipt no: {{ $document->receipt_no }}</small>
                                                            @elseif($document->document_type === 'cheque' && $document->cheque_number)
                                                                <br><small class="text-muted">Cheque number: {{ $document->cheque_number }}</small>
                                                            @endif
                                                        </td>
                                                        <td style="max-width: 300px; word-wrap: break-word;">{{ $document->observations ?: '-' }}</td>
                                                        <td>{{ $document->upload_date ? $document->upload_date->format('d.m.Y H:i') : '-' }}</td>
                                                        <td style="max-width: 300px; word-wrap: break-word;">
                                                            <a href="{{ route('crm.accountant.client.download-document', ['id' => $deal->id, 'documentId' => $document->id]) }}"
                                                               class="text-decoration-none">
                                                                <i class="bi bi-download me-1"></i>{{ $document->file_name }}
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <label>
                                                                <input name="client_documents_remove[]" type="checkbox" value="{{ $document->id }}">
                                                                Mark for removal
                                                            </label>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="6" class="text-center">No documents uploaded</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Dynamic file upload container -->
                                <div id="clientDocumentsContainer" class="d-flex flex-column gap-1 mt-3"></div>

                                <!-- Add file button -->
                                <div class="p-1 d-flex mt-2">
                                    <button type="button" id="clientAddDocumentBtn" class="btn btn-secondary btn-sm">
                                        <i class="bi bi-plus"></i> Add file
                                    </button>
                                </div>
                                </div>
                            </div>
                        </div>
                            </div>
                        </div>

                        <!-- Payments Management Section (Collapsible) -->
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <h6 class="mb-0 me-2">
                                    <i class="bi bi-credit-card me-2"></i>Payments Management
                                    <span class="badge bg-secondary ms-2">{{ $clientPayments ? $clientPayments->count() : 0 }}</span>
                                </h6>
                                <button class="btn btn-sm accordion-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#clientPaymentsSection" aria-expanded="false" aria-controls="clientPaymentsSection">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse" id="clientPaymentsSection">
                                <div class="p-3">
                                <!-- Client Payment Documents Table -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Invoice No</th>
                                                        <th>Amount</th>
                                                        <th>Issue Date</th>
                                                        <th>Payment Type</th>
                                                        <th>Cashed in</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @if($clientPayments->count() > 0)
                                                        @foreach($clientPayments as $payment)
                                                            @php
                                                                $paymentDocs = [];
                                                                if (!empty($payment->payment_documents)) {
                                                                    $docs = is_string($payment->payment_documents)
                                                                        ? json_decode($payment->payment_documents, true)
                                                                        : $payment->payment_documents;

                                                                    if (is_array($docs) && count($docs) > 0) {
                                                                        foreach ($docs as $doc) {
                                                                            $docId = md5($doc['file_path'] ?? '');
                                                                            $paymentDocs[] = [
                                                                                'id' => $docId,
                                                                                'payment_id' => $payment->id,
                                                                                'file_name' => $doc['file_name'] ?? 'Unknown',
                                                                                'file_path' => $doc['file_path'] ?? '',
                                                                                'upload_date' => $doc['upload_date'] ?? now()->format('Y-m-d H:i:s')
                                                                            ];
                                                                        }
                                                                    }
                                                                }
                                                            @endphp

                                                            <tr>
                                                                <td>{{ $clientInvoiceDetails->ref_no ?? 'N/A' }}</td>
                                                                <td>{{ $payment->amount ?? 'N/A' }}</td>
                                                                <td>{{ $payment->payment_date ? date('d.m.Y', strtotime($payment->payment_date)) : 'N/A' }}</td>
                                                                <td>
                                                                    @if(isset($payment->payment_type) && isset($paymentMethods[$payment->payment_type]))
                                                                        {{ $paymentMethods[$payment->payment_type] }}
                                                                    @else
                                                                        {{ $payment->payment_type ?? 'N/A' }}
                                                                    @endif
                                                                </td>
                                                                <td>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input cashed-in-checkbox"
                                                                               type="checkbox"
                                                                               data-payment-id="{{ $payment->id }}"
                                                                               {{ $payment->cashed_in ? 'checked' : '' }}>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div class="d-flex justify-content-start">
                                                                        <button type="button" class="btn btn-outline-danger delete-document-btn" data-deal-id="{{ $deal->id }}" data-document-id="{{ $payment->id }}">
                                                                            <i class="bi bi-trash"></i> Delete
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    @else
                                                        <tr>
                                                            <td colspan="6" class="text-center">No payment documents found</td>
                                                        </tr>
                                                    @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Client Payment Form -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <h6 class="mb-3">Add Payment</h6>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="client_amount" class="form-label">Amount</label>
                                                    <input type="number" step="0.01" class="form-control" id="client_amount" name="client_amount" value="">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="client_payment_date" class="form-label">Payment Issue Date</label>
                                                    <input type="date" class="form-control" id="client_payment_date" name="client_payment_date" value="">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="client_payment_type" class="form-label">Payment Type</label>
                                                    <select class="form-select" id="client_payment_type" name="client_payment_type">
                                                        <option value="">Select payment type</option>
                                                        @foreach($paymentMethods as $value => $label)
                                                            <option value="{{ $value }}">{{ $label }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commissions Section -->
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Commissions</h5>
                    </div>
                    <div class="card-body">
                        <!-- Listing Agent -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="listing_agent_id" class="form-label">Listing Agent</label>
                                <select id="listing_agent_id" name="listing_agent_id" class="form-select">
                                    <option value="">N/A</option>
                                    @foreach (getAllFGRAgents() as $agent)
                                        <option value="{{ $agent->id }}" {{ $deal->listing_agent_id == $agent->id ? 'selected' : '' }}>
                                            {{ $agent->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="listing_agent_shared_commission" class="form-label">Shared Commission</label>
                                <div class="input-group">
                                    <span class="input-group-text">QAR</span>
                                    <input type="number" step="0.01" class="form-control" id="listing_agent_shared_commission"
                                           name="listing_agent_shared_commission" value="{{ $deal->listing_agent_shared_commission }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="listing_agent_cash_in" value="1"
                                           {{ $deal->listing_agent_cash_in ? 'checked' : '' }}>
                                    <label class="form-check-label">Cash In</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="listing_agent_month" class="form-label">Select Month</label>
                                <input type="month" class="form-control" id="listing_agent_month" name="listing_agent_month"
                                       value="{{ $deal->listing_agent_month ? \Carbon\Carbon::parse($deal->listing_agent_month)->format('Y-m') : '' }}">
                            </div>
                        </div>

                        <!-- Referred Listing Agent -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="referred_listing_agent_id" class="form-label">Referred Listing Agent</label>
                                <select id="referred_listing_agent_id" name="referred_listing_agent_id" class="form-select">
                                    <option value="">N/A</option>
                                    @foreach (getAllFGRAgents() as $agent)
                                        <option value="{{ $agent->id }}" {{ $deal->referred_listing_agent_id == $agent->id ? 'selected' : '' }}>
                                            {{ $agent->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="referred_listing_agent_shared_commission" class="form-label">Shared Commission</label>
                                <div class="input-group">
                                    <span class="input-group-text">QAR</span>
                                    <input type="number" step="0.01" class="form-control" id="referred_listing_agent_shared_commission"
                                           name="referred_listing_agent_shared_commission" value="{{ $deal->referred_listing_agent_shared_commission }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="referred_listing_agent_cash_in" value="1"
                                           {{ $deal->referred_listing_agent_cash_in ? 'checked' : '' }}>
                                    <label class="form-check-label">Cash In</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="referred_listing_agent_month" class="form-label">Select Month</label>
                                <input type="month" class="form-control" id="referred_listing_agent_month" name="referred_listing_agent_month"
                                       value="{{ $deal->referred_listing_agent_month ? \Carbon\Carbon::parse($deal->referred_listing_agent_month)->format('Y-m') : '' }}">
                            </div>
                        </div>

                        <!-- Closing Agent -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="closing_agent_id" class="form-label">Closing Agent</label>
                                <select id="closing_agent_id" name="closing_agent_id" class="form-select">
                                    <option value="">N/A</option>
                                    @foreach (getAllFGRAgents() as $agent)
                                        <option value="{{ $agent->id }}" {{ $deal->closing_agent_id == $agent->id ? 'selected' : '' }}>
                                            {{ $agent->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="closing_agent_shared_commission" class="form-label">Shared Commission</label>
                                <div class="input-group">
                                    <span class="input-group-text">QAR</span>
                                    <input type="number" step="0.01" class="form-control" id="closing_agent_shared_commission"
                                           name="closing_agent_shared_commission" value="{{ $deal->closing_agent_shared_commission }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="closing_agent_cash_in" value="1"
                                           {{ $deal->closing_agent_cash_in ? 'checked' : '' }}>
                                    <label class="form-check-label">Cash In</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="closing_agent_month" class="form-label">Select Month</label>
                                <input type="month" class="form-control" id="closing_agent_month" name="closing_agent_month"
                                       value="{{ $deal->closing_agent_month ? \Carbon\Carbon::parse($deal->closing_agent_month)->format('Y-m') : '' }}">
                            </div>
                        </div>

                        <!-- Referred Closing Agent -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="referred_closing_agent_id" class="form-label">Referred Closing Agent</label>
                                <select id="referred_closing_agent_id" name="referred_closing_agent_id" class="form-select">
                                    <option value="">N/A</option>
                                    @foreach (getAllFGRAgents() as $agent)
                                        <option value="{{ $agent->id }}" {{ $deal->referred_closing_agent_id == $agent->id ? 'selected' : '' }}>
                                            {{ $agent->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="referred_closing_agent_shared_commission" class="form-label">Shared Commission</label>
                                <div class="input-group">
                                    <span class="input-group-text">QAR</span>
                                    <input type="number" step="0.01" class="form-control" id="referred_closing_agent_shared_commission"
                                           name="referred_closing_agent_shared_commission" value="{{ $deal->referred_closing_agent_shared_commission }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="referred_closing_agent_cash_in" value="1"
                                           {{ $deal->referred_closing_agent_cash_in ? 'checked' : '' }}>
                                    <label class="form-check-label">Cash In</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="referred_closing_agent_month" class="form-label">Select Month</label>
                                <input type="month" class="form-control" id="referred_closing_agent_month" name="referred_closing_agent_month"
                                       value="{{ $deal->referred_closing_agent_month ? \Carbon\Carbon::parse($deal->referred_closing_agent_month)->format('Y-m') : '' }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <a href="{{ route('crm.accountant.index') }}" class="btn btn-outline-secondary"><i class="bi bi-x-circle"></i> Cancel</a>
            <button type="submit" class="btn btn-primary" id="saveButton">Save</button>
        </div>
    </form>
</div>
@endsection

@section('body-js')
<style>
    .table th, .table td {
        vertical-align: middle;
    }
    .form-check-input:checked {
        background-color: #198754;
        border-color: #198754;
    }

    /* Accordion styling */
    .accordion-toggle {
        transition: all 0.3s ease;
        border: none !important;
        background: transparent !important;
        color: #6c757d;
    }

    .accordion-toggle:hover {
        color: #495057;
        background: rgba(0,0,0,0.05) !important;
    }

    .accordion-toggle i {
        transition: transform 0.3s ease;
    }

    .accordion-toggle[aria-expanded="true"] i {
        transform: rotate(180deg);
    }

    .section-header {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .collapse {
        transition: all 0.3s ease;
    }

    /* Badge styling for counts */
    .badge {
        font-size: 0.75em;
        font-weight: 500;
    }

    .badge.bg-secondary {
        background-color: #6c757d !important;
        color: white;
    }
</style>

<script>
    class LandlordDocumentUploader {
        documentsContainer = document.querySelector('#landlordDocumentsContainer');

        addDocument() {
            const documentFileInput = document.createElement('input');
            documentFileInput.setAttribute('type', 'file');
            documentFileInput.setAttribute('name', 'landlord_documents[]');
            documentFileInput.setAttribute('required', 'required');
            documentFileInput.classList.add('form-control', 'form-control-sm');
            documentFileInput.style.width = "220px";
            documentFileInput.style.maxWidth = "220px";

            const optionsSelect = document.createElement('select');
            optionsSelect.setAttribute('name', 'landlord_documents_options[]');
            optionsSelect.setAttribute('required', 'required');
            optionsSelect.classList.add('form-select', 'form-select-sm');
            optionsSelect.style.width = "220px";
            optionsSelect.style.maxWidth = "220px";

            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.text = "Select document type";
            defaultOption.value = "";
            optionsSelect.add(defaultOption);

            // Add document options from PHP
            const options = {!! json_encode($documentOptions) !!};
            options.forEach(option => {
                const opt = document.createElement('option');
                opt.text = option.text;
                opt.value = option.value;
                if (option.disabled) {
                    opt.disabled = true;
                }
                optionsSelect.add(opt);
            });

            const wrapperDiv = document.createElement('div');
            wrapperDiv.classList.add('d-flex', 'gap-2', 'align-items-center', 'p-2', 'border', 'rounded');

            const fileDiv = document.createElement('div');
            fileDiv.appendChild(documentFileInput);

            const selectDiv = document.createElement('div');
            selectDiv.appendChild(optionsSelect);

            // Conditional field for Receipt no / Cheque number
            const conditionalInput = document.createElement('input');
            conditionalInput.setAttribute('type', 'text');
            conditionalInput.setAttribute('placeholder', 'Select document type first');
            conditionalInput.classList.add('form-control', 'form-control-sm');
            conditionalInput.style.width = "120px";
            conditionalInput.style.maxWidth = "120px";
            conditionalInput.style.display = "none";

            const conditionalDiv = document.createElement('div');
            conditionalDiv.appendChild(conditionalInput);

            // Add event listener to show/hide conditional field
            optionsSelect.addEventListener('change', function() {
                if (this.value === 'receipt') {
                    conditionalInput.setAttribute('name', 'landlord_documents_receipt_no[]');
                    conditionalInput.setAttribute('placeholder', 'Receipt no');
                    conditionalInput.style.display = 'block';
                } else if (this.value === 'cheque') {
                    conditionalInput.setAttribute('name', 'landlord_documents_cheque_number[]');
                    conditionalInput.setAttribute('placeholder', 'Cheque number');
                    conditionalInput.style.display = 'block';
                } else {
                    conditionalInput.style.display = 'none';
                    conditionalInput.removeAttribute('name');
                }
            });

            const observationsInput = document.createElement('input');
            observationsInput.setAttribute('type', 'text');
            observationsInput.setAttribute('name', 'landlord_documents_observations[]');
            observationsInput.setAttribute('placeholder', 'Observations (optional)');
            observationsInput.classList.add('form-control', 'form-control-sm');
            observationsInput.style.width = "100%";

            const observationsDiv = document.createElement('div');
            observationsDiv.classList.add('flex-grow-1');
            observationsDiv.appendChild(observationsInput);

            const removeBtn = document.createElement('button');
            removeBtn.setAttribute('type', 'button');
            removeBtn.classList.add('btn', 'btn-outline-danger', 'btn-sm');
            removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
            removeBtn.addEventListener('click', () => {
                wrapperDiv.remove();
            });

            wrapperDiv.appendChild(fileDiv);
            wrapperDiv.appendChild(selectDiv);
            wrapperDiv.appendChild(conditionalDiv);
            wrapperDiv.appendChild(observationsDiv);
            wrapperDiv.appendChild(removeBtn);
            this.documentsContainer.appendChild(wrapperDiv);
        }
    }

    class ClientDocumentUploader {
        documentsContainer = document.querySelector('#clientDocumentsContainer');

        addDocument() {
            const documentFileInput = document.createElement('input');
            documentFileInput.setAttribute('type', 'file');
            documentFileInput.setAttribute('name', 'client_documents[]');
            documentFileInput.setAttribute('required', 'required');
            documentFileInput.classList.add('form-control', 'form-control-sm');
            documentFileInput.style.width = "220px";
            documentFileInput.style.maxWidth = "220px";

            const optionsSelect = document.createElement('select');
            optionsSelect.setAttribute('name', 'client_documents_options[]');
            optionsSelect.setAttribute('required', 'required');
            optionsSelect.classList.add('form-select', 'form-select-sm');
            optionsSelect.style.width = "220px";
            optionsSelect.style.maxWidth = "220px";

            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.text = "Select document type";
            defaultOption.value = "";
            optionsSelect.add(defaultOption);

            // Add document options from PHP
            const options = {!! json_encode($documentOptions) !!};
            options.forEach(option => {
                const opt = document.createElement('option');
                opt.text = option.text;
                opt.value = option.value;
                if (option.disabled) {
                    opt.disabled = true;
                }
                optionsSelect.add(opt);
            });

            const wrapperDiv = document.createElement('div');
            wrapperDiv.classList.add('d-flex', 'gap-2', 'align-items-center', 'p-2', 'border', 'rounded');

            const fileDiv = document.createElement('div');
            fileDiv.appendChild(documentFileInput);

            const selectDiv = document.createElement('div');
            selectDiv.appendChild(optionsSelect);

            // Conditional field for Receipt no / Cheque number
            const conditionalInput = document.createElement('input');
            conditionalInput.setAttribute('type', 'text');
            conditionalInput.setAttribute('placeholder', 'Select document type first');
            conditionalInput.classList.add('form-control', 'form-control-sm');
            conditionalInput.style.width = "120px";
            conditionalInput.style.maxWidth = "120px";
            conditionalInput.style.display = "none";

            const conditionalDiv = document.createElement('div');
            conditionalDiv.appendChild(conditionalInput);

            // Add event listener to show/hide conditional field
            optionsSelect.addEventListener('change', function() {
                if (this.value === 'receipt') {
                    conditionalInput.setAttribute('name', 'client_documents_receipt_no[]');
                    conditionalInput.setAttribute('placeholder', 'Receipt no');
                    conditionalInput.style.display = 'block';
                } else if (this.value === 'cheque') {
                    conditionalInput.setAttribute('name', 'client_documents_cheque_number[]');
                    conditionalInput.setAttribute('placeholder', 'Cheque number');
                    conditionalInput.style.display = 'block';
                } else {
                    conditionalInput.style.display = 'none';
                    conditionalInput.removeAttribute('name');
                }
            });

            const observationsInput = document.createElement('input');
            observationsInput.setAttribute('type', 'text');
            observationsInput.setAttribute('name', 'client_documents_observations[]');
            observationsInput.setAttribute('placeholder', 'Observations (optional)');
            observationsInput.classList.add('form-control', 'form-control-sm');

            const observationsDiv = document.createElement('div');
            observationsDiv.classList.add('flex-grow-1');
            observationsDiv.appendChild(observationsInput);

            const removeBtn = document.createElement('button');
            removeBtn.setAttribute('type', 'button');
            removeBtn.classList.add('btn', 'btn-outline-danger', 'btn-sm');
            removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
            removeBtn.addEventListener('click', () => {
                wrapperDiv.remove();
            });

            wrapperDiv.appendChild(fileDiv);
            wrapperDiv.appendChild(selectDiv);
            wrapperDiv.appendChild(conditionalDiv);
            wrapperDiv.appendChild(observationsDiv);
            wrapperDiv.appendChild(removeBtn);
            this.documentsContainer.appendChild(wrapperDiv);
        }
    }

    function deleteDocument(dealId, documentId) {
        if (confirm('Are you sure you want to delete this payment?')) {
            fetch(`/crm/accountant/${dealId}/delete-document/${documentId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.redirected) {
                    window.location.href = response.url;
                    return;
                }
                const row = document.querySelector(`button[data-document-id="${documentId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }

                showAlert('success', 'Payment deleted successfully');
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'An error occurred while deleting the payment');
            });
        }
    }

    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        const formContainer = document.querySelector('.container-fluid.px-4');
        formContainer.insertBefore(alertDiv, formContainer.firstChild);

        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 150);
        }, 5000);
    }

    window.addEventListener('load', () => {
        // Initialize landlord document uploader
        const landlordDocumentUploader = new LandlordDocumentUploader();
        const landlordAddDocumentBtn = document.querySelector('#landlordAddDocumentBtn');
        if (landlordAddDocumentBtn) {
            landlordAddDocumentBtn.addEventListener('click', () => landlordDocumentUploader.addDocument());
        }

        // Initialize client document uploader
        const clientDocumentUploader = new ClientDocumentUploader();
        const clientAddDocumentBtn = document.querySelector('#clientAddDocumentBtn');
        if (clientAddDocumentBtn) {
            clientAddDocumentBtn.addEventListener('click', () => clientDocumentUploader.addDocument());
        }

        document.addEventListener('click', function(event) {
            const deleteButton = event.target.closest('.delete-document-btn');
            if (deleteButton) {
                event.preventDefault();
                event.stopPropagation();

                const dealId = deleteButton.getAttribute('data-deal-id');
                const documentId = deleteButton.getAttribute('data-document-id');
                deleteDocument(dealId, documentId);

                return false;
            }
        });


        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            const formData = new FormData(form);
            for (let [key, value] of formData.entries()) {
                if (value instanceof File) {
                    console.log(`${key}: File (${value.name}, ${value.size} bytes)`);
                } else {
                    console.log(`${key}: ${value}`);
                }
            }
        });

        // Handle cashed-in checkbox changes
        document.addEventListener('change', function(event) {
            if (event.target.classList.contains('cashed-in-checkbox')) {
                const paymentId = event.target.getAttribute('data-payment-id');
                const cashedIn = event.target.checked;

                fetch(`/crm/accountant/payment/${paymentId}/update-cashed-in`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        cashed_in: cashedIn
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success toast notification
                        showToast(data.message, 'success');
                    } else {
                        // Show error toast notification
                        showToast(data.message, 'error');
                        // Revert checkbox state on error
                        event.target.checked = !cashedIn;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Show error toast notification
                    showToast('error', 'An error occurred while updating payment status');
                    // Revert checkbox state on error
                    event.target.checked = !cashedIn;
                });
            }
        });
    });
</script>
@endsection
