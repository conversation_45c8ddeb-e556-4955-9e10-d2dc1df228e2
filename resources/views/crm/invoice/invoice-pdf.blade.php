<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice->ref_no }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        
        .logo {
            max-width: 200px;
            height: auto;
        }
        
        .company-info {
            text-align: right;
            font-size: 12px;
            color: #666;
        }
        
        .invoice-title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px 0;
        }
        
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .invoice-info, .client-info {
            width: 48%;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 5px;
        }
        
        .info-row {
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        
        .property-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .amount-section {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .amount-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .amount-value {
            font-size: 32px;
            font-weight: bold;
            color: #27ae60;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #e0e0e0;
            padding-top: 20px;
        }
        
        .payment-terms {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .payment-terms-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            @if(file_exists($logo_path))
                <img src="{{ $logo_path }}" alt="FGREALTY Logo" class="logo">
            @else
                <h2 style="color: #2c3e50; margin: 0;">FGREALTY</h2>
            @endif
        </div>
        <div class="company-info">
            <strong>FGREALTY</strong><br>
            Real Estate Services<br>
            Doha, Qatar<br>
            www.fgrealty.qa
        </div>
    </div>

    <div class="invoice-title">
        INVOICE
    </div>

    <div class="invoice-details">
        <div class="invoice-info">
            <div class="section-title">Invoice Details</div>
            <div class="info-row">
                <span class="info-label">Invoice No:</span>
                {{ $invoice->ref_no }}
            </div>
            <div class="info-row">
                <span class="info-label">Date:</span>
                {{ $invoice_date }}
            </div>
            <div class="info-row">
                <span class="info-label">Deal Ref:</span>
                D{{ sprintf('%06d', $deal->id) }}
            </div>
            <div class="info-row">
                <span class="info-label">Type:</span>
                {{ $invoice->is_client ? 'Client Invoice' : 'Landlord Invoice' }}
            </div>
        </div>

        <div class="client-info">
            <div class="section-title">
                {{ $invoice->is_client ? 'Bill To (Client)' : 'Bill To (Landlord)' }}
            </div>
            @php
                $contact = $invoice->is_client ? $client : $owner;
            @endphp
            @if($contact)
                <div class="info-row">
                    <span class="info-label">Name:</span>
                    {{ $contact->name }}
                </div>
                @if($contact->email_1)
                    <div class="info-row">
                        <span class="info-label">Email:</span>
                        {{ $contact->email_1 }}
                    </div>
                @endif
                @if($contact->mobile_1)
                    <div class="info-row">
                        <span class="info-label">Phone:</span>
                        {{ $contact->prefix_mobile_1 ?? '' }} {{ $contact->mobile_1 }}
                    </div>
                @endif
                @if($contact->qatar_id_no)
                    <div class="info-row">
                        <span class="info-label">Qatar ID:</span>
                        {{ $contact->qatar_id_no }}
                    </div>
                @endif
            @endif
        </div>
    </div>

    <div class="property-details">
        <div class="section-title">Property Details</div>
        <div class="info-row">
            <span class="info-label">Property Ref:</span>
            {{ $property->ref_no ?? 'N/A' }}
        </div>
        @if($location)
            <div class="info-row">
                <span class="info-label">Location:</span>
                {{ $location->path() ?? 'N/A' }}
            </div>
        @endif
        @if($tower)
            <div class="info-row">
                <span class="info-label">Building:</span>
                {{ $tower->name ?? 'N/A' }}
            </div>
        @endif
        @if($deal->unit_number)
            <div class="info-row">
                <span class="info-label">Unit Number:</span>
                {{ $deal->unit_number }}
            </div>
        @endif
    </div>

    <div class="amount-section">
        <div class="amount-title">Invoice Amount</div>
        <div class="amount-value">QAR {{ number_format($invoice->amount, 2) }}</div>
    </div>

    @if($invoice->type)
        <div class="payment-terms">
            <div class="payment-terms-title">Service Type</div>
            {{ ucfirst($invoice->type) }} Commission
        </div>
    @endif

    <div class="footer">
        <p><strong>Thank you for choosing FGREALTY!</strong></p>
        <p>For any inquiries regarding this invoice, please contact <NAME_EMAIL></p>
        <p>This is a computer-generated invoice and does not require a signature.</p>
    </div>
</body>
</html>
