@extends('crm.layout')

@section('content')
    <div class="px-md-2">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Lead Sources</h1>
            <div class="btn-toolbar mb-2 mb-md-0 gap-2">
                <button onclick="javascript:toggleSourceModal(); return false;" role="button" class="btn btn-sm btn-outline-secondary">
                        <span data-feather="plus"></span>
                        Add new item
                    </button>
            </div>
        </div>

        <div class="row">
            <div class="col mt-2">
                <table class="table table-striped dataTable fw-light align-middle" style="width:100%;" id="lead-sources-table">
                    <thead>
                        <tr>
                            <th>Lead Source</th>
                            <th>Campaign</th>
                            <th>Leads No</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <div class="modal fade" id="lead-platform-add">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sourceModalTitle">Add new source</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contact-create-body">
                <div class="mb-3">
                    <label for="leadSource" class="form-label">Source Name</label>
                    <input type="text" class="form-control" id="leadSource" placeholder="">
                    <div id="leadSourceNotification"></div>
                </div>
                <div class="mb-3">
                    <label for="marketingCampaign" class="form-label">Marketing Campaign</label>
                    <select class="form-select" id="marketingCampaign">
                        <option value="">Select a campaign</option>
                        <!-- Will be populated via JavaScript -->
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button id="addSourceBtn" type="button" class="btn btn-outline-primary" onclick="saveSource()">Save</button>
                <button id="cancelSourceBtn" type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('body-js')
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.js"></script>

<script src="{{mix('js/crm/libs/datatables-util.js')}}"></script>
<script src="{{mix('js/crm/datatables-action-menu.js')}}"></script>
<script src="{{mix('js/admin/lead-source-table.js')}}"></script>
<script>
    let leadSourceModal;
    const modalAddBtn = document.querySelector('#addSourceBtn');
    const modalCancelBtn = document.querySelector('#cancelSourceBtn');
    let currentSourceId = null;
    let marketingCampaigns = [];

    window.addEventListener('load', () => {
        leadSourceModal = new bootstrap.Modal('#lead-platform-add', {
            keyboard: false
        });

        // Fetch marketing campaigns
        fetchMarketingCampaigns();
    });

    function fetchMarketingCampaigns() {
        request('/api/marketing-campaigns', {
            method: 'GET'
        })
        .then(data => {
            marketingCampaigns = data;
            populateCampaignsDropdown();
        })
        .catch(err => {
            console.error('Failed to fetch marketing campaigns', err);
        });
    }

    function populateCampaignsDropdown() {
        const dropdown = document.querySelector('#marketingCampaign');
        dropdown.innerHTML = '<option value="">Select a campaign</option>';

        marketingCampaigns.forEach(campaign => {
            const option = document.createElement('option');
            option.value = campaign.id;
            option.textContent = `[${campaign.project_name}] ${campaign.name}`;
            dropdown.appendChild(option);
        });
    }

    function toggleSourceModal(sourceId = null) {
        resetForm();

        if (sourceId) {
            // Edit mode
            currentSourceId = sourceId;
            document.querySelector('#sourceModalTitle').textContent = 'Edit source';
            modalAddBtn.textContent = 'Update';

            // Fetch source data
            request(`/api/lead-source/${sourceId}`, {
                method: 'GET'
            })
            .then(data => {
                document.querySelector('#leadSource').value = data.name;
                if (data.marketing_campaign_id) {
                    document.querySelector('#marketingCampaign').value = data.marketing_campaign_id;
                }
                leadSourceModal.show();
            })
            .catch(err => {
                showToast("Failed to fetch source data", 'error');
            });
        } else {
            // Add mode
            currentSourceId = null;
            document.querySelector('#sourceModalTitle').textContent = 'Add new source';
            modalAddBtn.textContent = 'Add';
            leadSourceModal.toggle();
        }
    }

    function resetForm() {
        document.querySelector('#leadSource').value = '';
        document.querySelector('#marketingCampaign').value = '';
        document.querySelector('#leadSourceNotification').innerHTML = '';
    }

    function saveSource() {
        const sourceVal = document.querySelector('#leadSource').value.trim();
        const campaignId = document.querySelector('#marketingCampaign').value;

        modalAddBtn.setAttribute('disabled', 'true');
        modalCancelBtn.setAttribute('disabled', 'true');

        const endpoint = currentSourceId ? `/api/lead-source/${currentSourceId}` : '/crm/leads/source';
        const method = currentSourceId ? 'PUT' : 'POST';

        request(endpoint, {
            method: method,
            body: {
                name: sourceVal,
                marketing_campaign_id: campaignId || null
            }
        })
        .then(data => {
            showToast(`Item successfully ${currentSourceId ? 'updated' : 'created'}. Will refresh`, 'success');
            setTimeout(() => {
                leadSourceModal.hide();
                document.querySelector('#leadSource').value = '';
                document.querySelector('#marketingCampaign').value = '';
                window.location.reload();
            }, 3000);
        })
        .catch(response => {
            showToast(`Cannot ${currentSourceId ? 'update' : 'save'} item`, 'error');
            response.json().then(errData => {
                if (errData.errors && errData.errors.name) {
                    document.querySelector('#leadSourceNotification').innerHTML = `<p class="text-danger">${errData.errors.name[0]}</p>`;
                }
            });
            modalAddBtn.removeAttribute('disabled');
            modalCancelBtn.removeAttribute('disabled');
        });
    }
</script>
@endsection

@section('head-css')

<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/3.1.9/css/fixedHeader.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" />
@endsection
