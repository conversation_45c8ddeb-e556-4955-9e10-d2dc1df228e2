export enum TableViewType {
    Personal = "personal",
    Masterlist = "master",
}

export enum ViewType {
    Table = "table",
    Edit = "edit",
    Add = "add",
}

export interface KeyValue {
    key: number;
    value: string;
}

export interface IdLabel {
    id: number;
    label: string;
}

export interface SelectableIdLabel extends IdLabel {
    isSelected: boolean;
}

export interface LabelValue {
    value: any;
    label: string;
}

export interface Country {
    id: number;
    phone_prefix: string;
    name: string;
    code: string;
}

export interface Contact {
    agent_name: string;
    created_at: string;
    company_name: string;
    date_of_birth: string;
    contact_id: number | null;
    is_master_contact: number; // can be 1 or null
    master_contact_id: number; //
    master_contact: any;
    landlord_id: number | null;
    email_1: string;
    email_2?: string;
    gender: string;
    id: number;
    prefix_mobile_1: string;
    mobile_1: string;
    prefix_mobile_2: string;
    mobile_2: string;
    phone_1?: string;
    phone_2?: string;
    name: string;
    nationality_id: number;
    occupation: string;
    position: string;
    qatar_id_no: string;
    residency: string;
    tag_labels: Array<string>;
    tags: Array<string>;
    type: string;
    verified: boolean;
    leads_ids: Array<number>;
    deals_ids: Array<number>;
    operation_history?: Array<OperationHistory>;
    remarks: string;
    can_delete?: boolean;
    reminders: string;
    metadata?: any;
}

export interface Configs {
    API_URL: string;
    GOOGLE_MAPS_API_KEY: string;
}

export interface VirtualSelectConfig {
    multiple?: boolean;
    search?: boolean;
    showValueAsTags?: boolean;
    allowNewOption?: boolean;
    selectedValue?: Array<string>;
    disabled?: boolean;
}

export interface OperationHistory {
    created_by: string;
    created_at: string;
    content: string;
}

// START component state definitions
export interface ContactListEditState extends Contact {
    location: string;
}

export type ContactListSearchState = {
    term?: string;
    page?: number;
    vt?: TableViewType;
    tags?: Array<number>;
    callLog?: Array<string>;
    verified?: boolean;
    isLandlord?: boolean;
    hasLeads?: boolean;
    records?: Array<any>;
    recordsTotal?: number;
    currentPage?: number;
    perPage?: number;
    isStoreLoaded?: boolean;
    allTags?: Array<{ label: string; value: number; }>
    isLoading?: boolean;
    // New filter fields
    operationType?: string;
    propertyTypes?: Array<number>;
    dateCreatedFrom?: string;
    dateCreatedTo?: string;
    budgetFrom?: number;
    budgetTo?: number;
};

export interface ContactsListComponentState {
    viewType: ViewType;
    tableViewType: TableViewType;
    // tags: Array<{ value: any; label: string }>;
    editEntry: Contact | null | undefined;
    saving: {
        isSuccess: boolean;
        isFailure: boolean;
    };
    moreFiltersPanelVisible: boolean;
    // table: {
    //     recordsNo: number;
    //     allRecords: number;
    //     currentPage: number;
    //     perPage: number;
    // };
    userIsManager: boolean;
    modal: {
        isOpen: boolean;
        title: string;
    };
    exportType: 'whatsapp' | 'newsletter',
    hasExportLimit: boolean,
    exportLimit: number
}

export interface ContactsListFormState {
    editedEntity: Contact;
    form: {
        errors: { [key: string]: any };
        isPristine: boolean;
    };
    loading: boolean;
    nationalities: Array<any>;
    nationalitiesSelectorConfig: VirtualSelectConfig;
    tagsSelectorConfig: VirtualSelectConfig;
    operationHistory: Array<OperationHistory>;
    isContactExistsCheckInProgress: boolean;
    tags: Array<any>;
    saving: {
        isSuccess: boolean;
        isFailure: boolean;
        isAlertVisible: boolean;
    };
    existingAgentIdByEmail: number;
    existingAgentIdByPhone: number;
    isActivityPanelModalOpen: boolean;
}

export interface RoomCard {
    id: number;
    availabilityStartDate: string;
    availabilityEndDate: string;
    maximumOccupancy: number;
    basePrice: number;
    commissionType: "percent" | "value";
    commissionValue: number;
    unitNo: string;
    isActive: boolean;
}

export interface RoomCardDetails {
    id: number;
    availabilityStartDate: string;
    availabilityEndDate: string;
    unitNo: string;
    isActive: boolean;
}
// END component state definitions

// lead dashboard
export type LeadStatus =
    | "NEW"
    | "INITIAL_CONTACT"
    | "NOT_ANSWERED"
    | "CONTINUING_DISCUSSION"
    | "OFFER_SENT"
    | "MEETING_SCHEDULED"
    | "VIEWING_SCHEDULED"
    | "OTHER_OPTIONS"
    | "FOLLOW_UP"
    | "OFFER_NEGOTIATION"
    | "CONTRACT_PENDING"
    | "CLOSED_WON"
    | "POST_CLOSURE"
    | "NOT_QUALIFIED"
    | "GHOSTED"
    | "CLOSED_LOSE"
    | "POST_CLOSURE"
    | "NOT_QUALIFIED"
    | "COLD_WARM_HOT"
    | "FAR_MOVING_DECISION"
    ;

export interface LeadRow {
    DT_RowId: number;
    assigned_at: string;
    assigned_to: {
        name: string;
        color: string;
    };
    budget: string;
    created_at: string;
    created_at_date: string;
    email: string;
    platform_from: string;
    status: {
        id: number;
        name: LeadStatus;
    };
}

export interface LeadDashboardCurrentOperationState {
    fromConfig: any;
    toConfig: any;
    newIndex: number;
}

export interface DashboardColumnConfig {
    title: string;
    leads: Array<any>;
    recordStats: {
        recordsTotal: number;
        recordsFiltered: number;
    };
    status: LeadStatus;
    commitFn?: (config: LeadDashboardCurrentOperationState) => Promise<void>;
}

export interface PropertySnapshotImage {
    img_url: string;
    alt: string;
    is_primary: boolean;
}

export interface PropertySnapshot {
    id: number;
    ref_no: string;
    title: string;
    status: string;
    images: Array<PropertySnapshotImage>;
    primary_image: string;
}

export interface HotelSelectedRoomType {
    roomTypeId: number;
    bedsNo: number;
    extraBedsNo: number;
    extraCotsNo: number;
    maxPeopleNo: number;
    minPeopleNo: number;
    label: string;
    listingId: number;
    listingRefNo: string;
    snapshot: PropertySnapshot;
}

export interface HotelFormState {
    form: {
        name: string; // and other form fields
        user: {
            id: number;
            name: string;
        };
        location: {
            id: number;
            name: string;
        };
        marker: {
            lat: number;
            lng: number;
        };
        address: string;
        reservationEmail: string;
        reservationPhone: string;
        is_active: boolean;
    };
    map: {
        lat: number;
        lng: number;
    };
    entityId: string;
    isLoading: boolean;
    errors: Record<string, any>;
    locations: Array<any>;
    users: Array<any>;
    viewType: "read" | "edit";
    errorMessage: string;
    successMessage: string;
    roomTypes: Array<HotelSelectedRoomType>;
    allRoomTypes: Array<SelectableIdLabel>;
    isSearcherModalOpen: boolean;
}

export interface ListingSelection {
    [key: string]: string;
}
// export enum LeadActivityType {
//     CallLog = 'call_log',
//     Meeting = 'meeting',
//     Viewing = 'viewing',
// };

export enum TaskType {
    CallLog = 'Call Log',
    Meeting = 'Meeting',
    Viewing = 'Viewing',
    Reminder = 'Reminder',
    FollowUp = 'Follow up',
};

export enum TaskActionType {
    Call = 'call',
    Email = 'email',
    Nurture = 'nurture',
    InPersonVisit = 'in person visit',
    Whatsapp = 'whatsapp',
    TextMessage = 'text message',
};

export enum TaskCallResponseType {
    NotAnswered = 'not_answered',
    Answered = 'answered',
    NotInterested = 'not_interested',
    Inactive = 'inactive',
}

export enum PanelType {
    CallLog = 'callLog',
    Marketing = 'marketing'
}

export enum LeadMeetingType {
    Online = 'online',
    Offline = 'offline',
    Office = 'office'
}
export enum LeadMeetingStatus {
    NotStarted = 'not_started',
    Completed = 'completed',
    InProgress = 'in_progress'
}
export interface BELeadActivityCallLogEntry {
    id: number;
    call_response: string;
    created_at: string;
    updated_at: string;
    notes: string;
}
export interface BEProperty {
    id: number;
    ref_no: string;
}
export interface BELeadActivityMeetingEntry {
    id: number;
    created_at: string;
    end_date: string;
    end_time: string;
    lead_activity_id: number;
    location_id: number;
    property_id: number;
    property: BEProperty;
    meeting_type: LeadMeetingType;
    notes: string;
    reminder_set: number;
    start_date: string;
    start_time: string;
    completion_date: string;
    status: LeadMeetingStatus;
    subject: string;
    updated_at: string;
}
// export interface BELeadActivityEntry {
//     id: number;
//     activity_type: LeadActivityType;
//     created_at: string;
//     lead_activity_call_log: BELeadActivityCallLogEntry
//     lead_activity_meeting: BELeadActivityMeetingEntry,
//     author: {
//         id: number;
//         name: string;
//     },
//     lead: {
//         contact: {
//             name: string;
//         }
//     };
//     status: LeadMeetingStatus;
//     reminder_set: '0' | '1';
//     completion_date: string;
// }

export interface BETask {
    id: number;
    object_id: number;
    object_type: 'lead' | 'contact';
    author: {
        id: number;
        name: string;
    };
    contact?: {
        id: number;
        name: string;
        email_1: string;
        mobile_1: string;
    },
    lead?: {
        contact: {
            id: number;
            name: string;
            email_1: string;
            mobile_1: string;
        },
        lead_status?: {
            id: number;
            name: string;
        },
        rating: string;
    };
    subject: string;
    type: TaskType;
    action_type: TaskActionType;
    due_date: string;
    completion_date: string;
    status: LeadMeetingStatus;
    call_response: TaskCallResponseType;
    call_notes: string;
    meeting_type: LeadMeetingType;
    location: {
        id: number;
        name: string;
    };
    location_id: number;
    property: {
        id: number;
        ref_no: string;
    };
    start_date: string;
    start_time: string;
    end_date: string;
    end_time: string;
    notes: string;
    reminder_set: number;
    created_at: string;
    updated_at: string;
    automatization_id: number;
}

export interface TaskWriteModel {
    id?: number;
    subject: string;
    type: TaskType;
    action_type: TaskActionType;
    due_date: string;
    status: LeadMeetingStatus;
    call_response: TaskCallResponseType;
    call_notes: string;
    meeting_type: LeadMeetingType;
    location_id: number;
    property_id: number;
    start_date: string;
    start_time: string;
    end_date: string;
    end_time: string;
    notes: string;
    reminder_set: number;
    automatization_id?: number;
}

export interface ActivityCallLogFormData {
    id?: number;
    subject: string;
    callResponse: string;
    callNotes: string;
    createdAt?: string;
    updatedAt?: string;
}

export interface ActivityFollowupFormData {
    id?: number;
    subject: string;
    actionType: TaskActionType;
    status: LeadMeetingStatus;
    notes: string;
    createdAt?: string;
    updatedAt?: string;
    automatization_id?: number;
    contact_name: string;
    contact_phone: string;
    contact_id: number;
    contact_email: string;
}

export interface ActivityMeetingFormData {
    id?: number;
    subject: string;
    meetingType: LeadMeetingType | '';
    status: LeadMeetingStatus;
    notes: string;
    location: {
        id: number;
    }
    locationId: string;
    propertyId: number;
    property: BEProperty;
    reminderSet: boolean;
    startDate: string;
    startTime: string;
    completionDate: string;
    endDate: string;
    endTime: string;
}

export interface FETask {
    id: number;
    type: TaskType;
    createdAt: string;
    updatedAt: string;
    author?: {
        id: number;
        name: string;
    },
    contact?: {
        id: number;
        name: string;
        email_1: string;
        mobile_1: string;
    },
    location: {
        id: number;
    }
    property?: {
        id: number;
        ref_no: string;
    }
    meetingType: LeadMeetingType,
    status: LeadMeetingStatus;
    completionDate: string;
    reminderSet: boolean;
    subject: string;
    actionType: TaskActionType,
    callResponse: TaskCallResponseType,
    callNotes: string;
    notes: string;
    startDate: string;
    startTime: string;
    endDate: string;
    endTime: string;
    dueDate: string;
    automatization_id: number;
}

export interface Permission {
    name: string;
}

export interface ContactTag {
    id: number;
    label: number;
    config: any;
}