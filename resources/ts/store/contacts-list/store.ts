import { debounce } from 'lodash';
import { Contact, ContactListSearchState, TableViewType } from './../../model/fe-models.interface';
import { createStore } from 'vuex'
import ContactsListService from './../../components/contacts-list/contacts-list.service';
import { BEContactsResponse } from './../../model/be-models.interface';

export declare const bootstrap: any;

const debouncedSearch = debounce((commit, nextSearchState) => {
    localStorage.setItem(
        "contactsTableFilters",
        JSON.stringify(nextSearchState)
    );
    commit("updateIsLoadingState", true);
    ContactsListService.fetch(nextSearchState).then(
        (response: BEContactsResponse) => {
            commit('updateResults', {
                records: response.data,
                recordsTotal: response.recordsTotal,
            });
            setTimeout(() => {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }, 200);
        }
    );
}, 300);

const fetchAndLoadTags = (commit: any) => {
    ContactsListService.fetchTags()
        .then(
            (data) => {
                const tags = data.map((item) => ({
                    label: item.label,
                    value: item.id,
                }));
                commit('updateStoreTags', tags)
            }
        )
        .catch((err: any) => console.log(err));
};

const store = createStore({
    state() {
        const initialState: ContactListSearchState = {
            term: '',
            page: 1,
            vt: TableViewType.Personal,
            tags: [],
            callLog: [],
            verified: false,
            isLandlord: false,
            hasLeads: false,
            records: [],
            recordsTotal: 0,
            currentPage: 1,
            perPage: 100,
            isStoreLoaded: false,
            allTags: [],
            isLoading: true,
            // New filter fields
            operationType: '',
            propertyTypes: [],
            dateCreatedFrom: '',
            dateCreatedTo: '',
            budgetFrom: undefined,
            budgetTo: undefined,
        };
        return initialState;
    },
    actions: {
        loadStore({ commit, state }, localStorageFilters: ContactListSearchState) {
            fetchAndLoadTags(commit);
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                ...localStorageFilters,
            };
            debouncedSearch(commit, nextSearchState);
            commit('loadStore', localStorageFilters);
        },
        updateVT({ commit, state }, viewType: TableViewType) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                vt: viewType,
                currentPage: 1,
                page: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateVT', viewType);
        },
        updateSearchTerm({ commit, state }, keyword: string) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                term: keyword,
                currentPage: 1,
                page: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateSearchTerm', keyword);
        },
        updatePage({ commit, state }, newPage: number) {
            console.log('update page', state);

            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                page: newPage,
                currentPage: newPage,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updatePage', newPage);
        },
        updateIsLandlord({ commit, state }, data: boolean) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                isLandlord: data,
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateIsLandlord', data);
        },
        updateHasLeads({ commit, state }, data: boolean) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                hasLeads: data,
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateHasLeads', data);
        },
        updateVerified({ commit, state }, isVerified: boolean) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                verified: isVerified,
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateVerified', isVerified);
        },
        updateTags({ commit, state }, tags: any) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                tags: tags ?? [],
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateTags', tags);
        },
        updateCallLog({ commit, state }, callLog: any) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                callLog: callLog ?? [],
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateCallLog', callLog);
        },
        async updateStarredState({ commit, state }, contact: Contact) {
            await ContactsListService.updateStar(contact)
                .then(() => {
                    contact.verified = !contact.verified;
                    commit("replaceContactInState", contact);
                })
                .catch((err: any) => console.log("err", err));
        },
        updateOperationType({ commit, state }, operationType: string) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                operationType: operationType,
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateOperationType', operationType);
        },
        updatePropertyTypes({ commit, state }, propertyTypes: Array<number>) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                propertyTypes: propertyTypes ?? [],
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updatePropertyTypes', propertyTypes);
        },
        updateDateCreated({ commit, state }, { dateFrom, dateTo }: { dateFrom: string, dateTo: string }) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                dateCreatedFrom: dateFrom,
                dateCreatedTo: dateTo,
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateDateCreated', { dateFrom, dateTo });
        },
        updateBudget({ commit, state }, { budgetFrom, budgetTo }: { budgetFrom: number, budgetTo: number }) {
            const nextSearchState: ContactListSearchState = {
                ...(state as ContactListSearchState),
                budgetFrom: budgetFrom,
                budgetTo: budgetTo,
                page: 1,
                currentPage: 1,
            };
            debouncedSearch(commit, nextSearchState);
            commit('updateBudget', { budgetFrom, budgetTo });
        }
    },
    mutations: {
        loadStore(state, action) {
            state.currentPage = action?.currentPage;
            state.term = action?.term;
            state.page = action?.page;
            state.vt = action.vt;
            state.tags = action.tags;
            state.callLog = action.callLog;
            state.verified = action.verified;
            state.isLandlord = action.isLandlord;
            state.hasLeads = action.hasLeads;
            state.records = action.records;
            state.recordsTotal = action.recordsTotal;
            state.perPage = action.perPage;
            // Load new filter fields
            state.operationType = action.operationType || '';
            state.propertyTypes = action.propertyTypes || [];
            state.dateCreatedFrom = action.dateCreatedFrom || '';
            state.dateCreatedTo = action.dateCreatedTo || '';
            state.budgetFrom = action.budgetFrom;
            state.budgetTo = action.budgetTo;
            state.isStoreLoaded = true;
        },
        updateResults(state, action) {
            const { records, recordsTotal } = action;
            state.records = records;
            state.recordsTotal = recordsTotal;
            state.isStoreLoaded = true;
            state.isLoading = false;
        },
        updateVT(state, action) {
            state.vt = action;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateSearchTerm(state, term) {
            state.term = term;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updatePage(state, page) {
            state.page = page;
            state.currentPage = page;
            state.isStoreLoaded = true;
        },
        updateVerified(state, verified: boolean) {
            state.verified = verified;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateHasLeads(state, hasLeads: boolean) {
            state.hasLeads = hasLeads;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateIsLandlord(state, isLandlord: boolean) {
            state.isLandlord = isLandlord;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateTags(state, tags: Array<number>) {
            state.tags = tags;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateCallLog(state, callLog: Array<string>) {
            state.callLog = callLog;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateStoreTags(state, tags: Array<{ label: string; value: number; }>) {
            state.allTags = tags;
        },
        replaceContactInState(state, contact: Contact) {
            state.records = [...state.records.map(eachContact => {
                return eachContact.id == contact.id ? contact : eachContact;
            })]
        },
        updateIsLoadingState(state, nextValue) {
            state.isLoading = nextValue;
        },
        updateOperationType(state, operationType: string) {
            state.operationType = operationType;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updatePropertyTypes(state, propertyTypes: Array<number>) {
            state.propertyTypes = propertyTypes;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateDateCreated(state, { dateFrom, dateTo }: { dateFrom: string, dateTo: string }) {
            state.dateCreatedFrom = dateFrom;
            state.dateCreatedTo = dateTo;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        },
        updateBudget(state, { budgetFrom, budgetTo }: { budgetFrom: number, budgetTo: number }) {
            state.budgetFrom = budgetFrom;
            state.budgetTo = budgetTo;
            state.isStoreLoaded = true;
            state.page = 1;
            state.currentPage = 1;
        }
    },
    getters: {}
})

export default store;