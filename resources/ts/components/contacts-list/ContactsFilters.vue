<template>
    <div class="card card-body p-2 m-2 bg-light" :class="{
        'g-2': true,
        filters: true,
        'd-none': !moreFiltersVisible
    }" id="contactsFiltersPanel">
        <div class="row">
            <div class="col-12 text-right mb-1">
                <a href="javascript:void(0)" @click="resetFilters()" class="small">Reset filters</a>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Rent/Sale</label>
                <select v-model="operationType" class="form-select form-select-sm" @change="onOperationTypeChange">
                    <option value="">Please select</option>
                    <option value="rent">Rent</option>
                    <option value="sale">Sale</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Property Type</label>
                <FGVirtualSelect :selectorClassName="propertyTypeSelectorClass" :class="{
                    'vscomp-ele': true,
                    [propertyTypeSelectorClass]: true,
                }" :options="propertyTypeOptions" :config="propertyTypeSelectorConfig"
                    :selectedValue="selectedPropertyTypes" @change="onPropertyTypeChange($event)" />
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Date Created</label>
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control form-control-sm" id="contactCreationDateRangeInput">
                    <button @click="clearDaterange('contactCreationDateRangeInput')"
                        class="btn btn-xs btn-outline-secondary">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Budget</label>
                <div class="input-group input-group-sm">
                    <input v-model="budgetFrom" type="number" placeholder="From" class="form-control form-control-sm"
                        @input="onBudgetChange">
                    <input v-model="budgetTo" type="number" placeholder="To" class="form-control form-control-sm"
                        @input="onBudgetChange">
                </div>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Tags</label>
                <FGVirtualSelect :selectorClassName="tagsFilterClass" :class="{
                    'vscomp-ele': true,
                    [tagsFilterClass]: true,
                }" :options="storeState.allTags" :config="filterTagsSelectorConfig" :selectedValue="selectedTags"
                    @change="updateTags($event)" />
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Call Response</label>
                <select v-model="storeState.callLog" class="form-select form-select-sm" @change="updateCallLog($event)">
                    <option value="">Please select</option>
                    <option v-for="cr of callResponses" :key="cr.value" :value="cr.value">{{ cr.label }}</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-8 col-lg-6 mb-2 d-flex gap-3 align-items-center">
                <div class="form-check">
                    <input v-model="storeState.verified" type="checkbox" class="form-check-input" id="verifiedCheckbox"
                        @change="(event: any) => updateVerified(event?.target?.checked)" />
                    <label for="verifiedCheckbox" class="form-check-label small">Verified</label>
                </div>
                <div class="form-check">
                    <input v-model="storeState.isLandlord" type="checkbox" class="form-check-input"
                        id="isLandlordCheckbox" @change="(event: any) => updateIsLandlord(event?.target?.checked)" />
                    <label for="isLandlordCheckbox" class="form-check-label small">Is Landlord</label>
                </div>
                <div class="form-check">
                    <input v-model="storeState.hasLeads" type="checkbox" class="form-check-input" id="hasLeadsCheckbox"
                        @change="(event: any) => updateHasLeads(event?.target?.checked)" />
                    <label for="hasLeadsCheckbox" class="form-check-label small">Has Leads</label>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from "vue";
import { useStore } from "vuex";
import FGVirtualSelect from "../FGVirtualSelect.vue";
import { isEqual } from "lodash";

declare const request: Function;
declare const $: any;

export default defineComponent({
    name: "ContactsFilters",
    components: {
        FGVirtualSelect,
    },
    setup() {
        const store = useStore();

        // Reactive data
        const operationType = ref('');
        const budgetFrom = ref<number | undefined>(undefined);
        const budgetTo = ref<number | undefined>(undefined);
        const propertyTypeOptions = ref<Array<{ value: number; label: string }>>([]);
        const moreFiltersVisible = ref(false); // Start collapsed by default

        // Call responses data
        const callResponses = [{
            value: 'not_answered',
            label: 'Not answered',
        }, {
            value: 'answered',
            label: 'Answered',
        }, {
            value: 'not_interested',
            label: 'Not interested',
        }, {
            value: 'inactive',
            label: 'Inactive',
        }];

        // Virtual select class names
        function virtualSelectClassName(): string {
            return "r" + (Math.random() + 1).toString(36).substring(7);
        }
        const propertyTypeSelectorClass = virtualSelectClassName();
        const tagsFilterClass = virtualSelectClassName();

        // Computed properties
        const storeState = computed(() => store.state);

        const selectedPropertyTypes = computed(() => {
            return storeState.value.propertyTypes || [];
        });

        const propertyTypeSelectorConfig = computed(() => {
            return {
                multiple: true,
                search: true,
                showValueAsTags: true,
                maxHeight: '32px',
                dropboxWrapper: 'form-select-sm'
            };
        });

        // Tags computed properties
        const selectedTags = computed(() => {
            return storeState.value.tags?.map((eachTagId: string) => Number(eachTagId)) || [];
        });

        const filterTagsSelectorConfig = computed(() => {
            const allTags = storeState.value.allTags;
            const selectedTagIds = storeState.value?.tags ?? [];
            const selectedTags = [...allTags.filter((eachTag: any) => selectedTagIds.includes(eachTag.value))];
            return {
                multiple: true,
                search: true,
                showValueAsTags: true,
                selectedValue: selectedTags,
                maxHeight: '32px',
                dropboxWrapper: 'form-select-sm'
            };
        });

        // Methods
        const onOperationTypeChange = () => {
            store.dispatch('updateOperationType', operationType.value);
        };

        const onPropertyTypeChange = (propertyTypes: Array<number>) => {
            store.dispatch('updatePropertyTypes', propertyTypes);
        };

        const onBudgetChange = () => {
            store.dispatch('updateBudget', {
                budgetFrom: budgetFrom.value,
                budgetTo: budgetTo.value
            });
        };

        const clearDaterange = (inputId: string) => {
            const input = document.getElementById(inputId) as HTMLInputElement;
            if (input) {
                input.value = '';
                store.dispatch('updateDateCreated', { dateFrom: '', dateTo: '' });
            }
        };

        const resetFilters = () => {
            operationType.value = '';
            budgetFrom.value = undefined;
            budgetTo.value = undefined;

            // Clear date range picker
            clearDaterange('contactCreationDateRangeInput');

            // Reset store - both new and existing filters
            store.dispatch('updateOperationType', '');
            store.dispatch('updatePropertyTypes', []);
            store.dispatch('updateDateCreated', { dateFrom: '', dateTo: '' });
            store.dispatch('updateBudget', { budgetFrom: undefined, budgetTo: undefined });
            store.dispatch('updateTags', []);
            store.dispatch('updateCallLog', '');
            store.dispatch('updateVerified', false);
            store.dispatch('updateIsLandlord', false);
            store.dispatch('updateHasLeads', false);
        };

        // Methods for existing filters
        const updateTags = (tags: string) => {
            if (!isEqual(tags, storeState.value.tags)) {
                store.dispatch('updateTags', tags);
            }
        };

        const updateCallLog = (event: any) => {
            const selectedValue = event.target.value;
            store.dispatch('updateCallLog', selectedValue);
        };

        const updateVerified = (data: boolean) => {
            store.dispatch('updateVerified', data);
        };

        const updateIsLandlord = (data: boolean) => {
            store.dispatch('updateIsLandlord', data);
        };

        const updateHasLeads = (data: boolean) => {
            store.dispatch('updateHasLeads', data);
        };

        // Load property types
        const loadPropertyTypes = async () => {
            try {
                const data = await request("/api/listing-type");
                propertyTypeOptions.value = data.map((item: any) => ({
                    value: item.id,
                    label: item.label,
                }));
            } catch (error) {
                console.error('Failed to load property types:', error);
            }
        };

        // Initialize date range picker
        const initializeDateRangePicker = () => {
            setTimeout(() => {
                if (typeof $ !== 'undefined' && typeof $.fn.daterangepicker !== 'undefined') {
                    $("#contactCreationDateRangeInput").daterangepicker(
                        {
                            opens: "left",
                            autoApply: true,
                        },
                        function (start: any, end: any) {
                            store.dispatch('updateDateCreated', {
                                dateFrom: start.format("YYYY-MM-DD"),
                                dateTo: end.format("YYYY-MM-DD")
                            });
                        }
                    );
                }
            }, 500);
        };

        // Watch store state to sync local values
        watch(() => storeState.value.operationType, (newValue) => {
            operationType.value = newValue || '';
        });

        watch(() => storeState.value.budgetFrom, (newValue) => {
            budgetFrom.value = newValue;
        });

        watch(() => storeState.value.budgetTo, (newValue) => {
            budgetTo.value = newValue;
        });

        // Lifecycle
        onMounted(async () => {
            await loadPropertyTypes();
            initializeDateRangePicker();

            // Initialize values from store
            operationType.value = storeState.value.operationType || '';
            budgetFrom.value = storeState.value.budgetFrom;
            budgetTo.value = storeState.value.budgetTo;
        });

        return {
            // Reactive data
            operationType,
            budgetFrom,
            budgetTo,
            propertyTypeOptions,
            moreFiltersVisible,
            callResponses,
            // Virtual select classes
            propertyTypeSelectorClass,
            tagsFilterClass,
            // Computed properties
            storeState,
            selectedPropertyTypes,
            propertyTypeSelectorConfig,
            selectedTags,
            filterTagsSelectorConfig,
            // Methods
            onOperationTypeChange,
            onPropertyTypeChange,
            onBudgetChange,
            clearDaterange,
            resetFilters,
            updateTags,
            updateCallLog,
            updateVerified,
            updateIsLandlord,
            updateHasLeads,
        };
    },
});
</script>

<style>

/* Compact styles for the filters */
.form-select-sm,
.form-control-sm {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    min-height: 30px;
}

/* Make virtual select more compact */
.vscomp-ele {
    --vscomp-font-size: 0.875rem;
    --vscomp-option-padding: 4px 8px;
    max-height: 32px;
    height: 32px;
}

.vscomp-toggle-button {
    max-height: 32px;
    height: 32px;
    line-height: 1;
    display: flex;
    align-items: center;
}
</style>
