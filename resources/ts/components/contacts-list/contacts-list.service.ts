import { Id<PERSON>abel } from "./../../model/fe-models.interface";
import { BEContactsResponse } from "./../../model/be-models.interface";
import config from "./../../config";
import {
    Contact,
    ContactListSearchState,
    KeyValue,
} from "../../model/fe-models.interface.js";

export declare const document: Document;
export declare const request: Function;

export default class ContactsListService {
    static phoneToContactIdMap: Map<string, number> = new Map();
    static emailToContactIdMap: Map<string, number> = new Map();
    static fetch(
        searchState: ContactListSearchState = { term: "" },
        isExport: boolean = false,
        exportType: 'whatsapp' | 'newsletter' = null,
        exportLimit: number = null
    ): Promise<BEContactsResponse> {
        let appLocale = document.querySelector("html")?.getAttribute("lang");
        if (!appLocale) {
            appLocale = "en";
        }
        const searchParams = new URLSearchParams();
        searchParams.append(
            "page",
            !!searchState.page ? searchState.page.toString() : "1"
        );
        searchParams.append("locale", appLocale);
        searchParams.append("pp", "100");
        searchParams.append("q", !!searchState.term ? searchState.term : "");
        searchParams.append("vt", !!searchState.vt ? searchState.vt : "");
        if (!!searchState.isLandlord) {
            searchParams.append("isLandlord", "1");
        }
        if (!!searchState.hasLeads) {
            searchParams.append("hasLeads", "1");
        }
        if (!!searchState.tags) {
            searchParams.append("tags", searchState.tags?.join(",") ?? "");
        }
        if (!!searchState.verified) {
            searchParams.append("verified", "1");
        }
        if (!!searchState.callLog) {
            const callLogValue = Array.isArray(searchState.callLog) ? searchState.callLog.join(",") : searchState.callLog;
            searchParams.append("callLog", callLogValue);
        }
        // New filter parameters
        if (!!searchState.operationType) {
            searchParams.append("operationType", searchState.operationType);
        }
        if (!!searchState.propertyTypes && searchState.propertyTypes.length > 0) {
            searchParams.append("propertyTypes", searchState.propertyTypes.join(","));
        }
        if (!!searchState.dateCreatedFrom) {
            searchParams.append("dateCreatedFrom", searchState.dateCreatedFrom);
        }
        if (!!searchState.dateCreatedTo) {
            searchParams.append("dateCreatedTo", searchState.dateCreatedTo);
        }
        if (searchState.budgetFrom !== undefined && searchState.budgetFrom !== null) {
            searchParams.append("budgetFrom", searchState.budgetFrom.toString());
        }
        if (searchState.budgetTo !== undefined && searchState.budgetTo !== null) {
            searchParams.append("budgetTo", searchState.budgetTo.toString());
        }

        if (isExport) {
            searchParams.append('export', '1');
            if (!!exportType) {
                searchParams.append('exportType', exportType)
                if (!!exportLimit) {
                    searchParams.append('exportLimit', String(exportLimit))
                }
            }
        }

        const url = `${config.API_URL}/contacts-list?${searchParams}`;

        if (isExport) {
            const csrfTag = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            let apiToken = "";
            const apiTokenNode = document.querySelector('meta[name="api-token"]');
            if (!!apiTokenNode) {
                apiToken = apiTokenNode.getAttribute('content');
            }

            // default headers

            const configObject: {
                headers: {
                    [key: string]: string
                }
            } = {
                headers: {
                    "X-CSRF-TOKEN": csrfTag
                }
            }

            if (apiToken) {
                configObject.headers = {
                    ...configObject.headers,
                    "Authorization": `Bearer ${apiToken}`
                }
            }

            return fetch(url, configObject)
                .then((response) => {
                    if (response.ok) {
                        response.blob().then((blob) => {
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.style.display = 'none';
                            a.href = url;
                            // the filename you want
                            a.download = 'export.csv';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                        })
                    }

                    if (response.status === 422) {
                        return Promise.reject(response);
                    }

                    throw new Error(response.statusText);
                })
        }

        return request(url);
    }

    static fetchSingleContact = (contactId: number, leadId: string) => {
        return request(
            `${config.API_URL}/contacts-list/${contactId}${!!leadId ? "?leadId=" + leadId : ""
            }`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
            }
        );
    };

    static async sendContactListToServer(evt: any) {
        const fileInput = evt.target;
        const fileList = fileInput.files;

        const fileContent = await ContactsListService.readFile(fileList[0]);
        if (!!fileContent) {
            const data = {
                fileContent,
                fileName: fileList[0].name,
            };
            return request(`${config.API_URL}/contacts-list/check`, {
                body: data,
                method: "POST",
            });
        }
    }

    static async importContacts(evt: any) {
        const fileInput = evt.target;
        const fileList = fileInput.files;

        const fileContent = await ContactsListService.readFile(fileList[0]);
        if (!!fileContent) {
            const data = {
                fileContent,
                fileName: fileList[0].name,
            };
            return request(`${config.API_URL}/contacts-list/import`, {
                body: data,
                method: "POST",
            });
        }

        // if (fileList && fileList.length) {
        //     let formData = new FormData();
        //     formData.append("importFile", fileList[0]);
        //     formData.append("name", "Test");
        //     return request(`${config.API_URL}/contacts-list/import`, {
        //         method: "POST",
        //         formData,
        //     });
        // }
        // return null;
    }

    static readFile(file: File): Promise<any> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (evt) => {
                const documentContent = (evt?.target?.result as string).split(
                    ","
                )[1];
                resolve(documentContent);
            };
            reader.onerror = (err) => reject(err);
            reader.readAsDataURL(file);
        });
    }

    static updateStar(item: Contact) {
        const nextVerifiedValue = !item.verified;
        return request(
            `${config.API_URL}/contacts-list/${item.id}/update-verified`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
                body: {
                    contact_id: item.id,
                    verified: nextVerifiedValue,
                },
            }
        );
    }

    static fetchNationalities(): Promise<Array<KeyValue>> {
        return request(`${config.API_URL}/nationality`, {
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        });
    }

    static fetchDuplicatesForContact(contactId: number): Promise<{
        data: Array<Contact>;
        recordsFiltered: number;
        recordsTotal: number;
    }> {
        return request(
            `${config.API_URL}/contacts-list?master_contact_id=${contactId}&duplicationType=duplicate`,
            {
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
            }
        );
    }

    static fetchAllDuplicates(term: string = null): Promise<Array<any>> {
        const params = !!term ? `?q=${term}` : ``
        return request(`${config.API_URL}/contacts-list-duplicates${params}`, {
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        });
    }

    static fetchDuplicatesFor(contactId: number): Promise<Array<any>> {
        return request(
            `${config.API_URL}/contacts-list-duplicates/${contactId}`,
            {
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
            }
        );
    }

    static markMasterContact(contactId: number): Promise<any> {
        return request(
            `${config.API_URL}/contacts-list-duplicates/${contactId}/mark-master`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
            }
        );
    }

    static saveEntry(data: Contact, leadId?: string): Promise<Contact> {
        const requestMethod = !!data.id ? "PATCH" : "POST";
        const leadIdSuffix = !!leadId ? "?leadId=" + leadId : "";
        const requestURL = !!data.id
            ? `${config.API_URL}/contacts-list/${data.id}${leadIdSuffix}`
            : `${config.API_URL}/contacts-list${leadIdSuffix}`;

        return request(requestURL, {
            method: requestMethod,
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            body: {
                ...data,
            },
        });
    }

    static editEntry(data: Contact): Promise<Contact> {
        return request(`${config.API_URL}/contacts-list/${data.id}`, {
            method: "PATCH",
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            body: {
                ...data,
            },
        });
    }

    static fetchTags = (): Promise<Array<IdLabel>> => {
        return request(`${config.API_URL}/contacts-list/tags`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        });
    };

    static checkContactExists = (
        data: { email: string; id: number },
        abortController: AbortController
    ): Promise<{
        existingContactId: number | undefined;
    }> => {
        if (ContactsListService.emailToContactIdMap.has(data.email)) {
            return Promise.resolve({
                existingContactId: ContactsListService.emailToContactIdMap.get(
                    data.email
                ),
            });
        } else {
            return request("/api/contacts-list/check-contact-exists", {
                signal: abortController.signal,
                body: data,
                method: "POST",
            }).then((serverReturn: { existingContactId: number }) => {
                if (!!serverReturn && !!serverReturn.existingContactId) {
                    ContactsListService.emailToContactIdMap.set(
                        data.email,
                        serverReturn.existingContactId
                    );
                }
                return serverReturn;
            });
        }
    };

    static checkContactExistsByPhone = (
        data: { phone: string; id: number },
        abortController: AbortController
    ): Promise<{
        existingContactId: number | undefined;
    }> => {
        if (ContactsListService.phoneToContactIdMap.has(data.phone)) {
            return Promise.resolve({
                existingContactId: ContactsListService.phoneToContactIdMap.get(
                    data.phone
                ),
            });
        } else {
            return request("/api/contacts-list/check-contact-exists-phone", {
                signal: abortController.signal,
                body: data,
                method: "POST",
            }).then((serverReturn: { existingContactId: number }) => {
                if (!!serverReturn && !!serverReturn.existingContactId) {
                    ContactsListService.phoneToContactIdMap.set(
                        data.phone,
                        serverReturn.existingContactId
                    );
                }
                return serverReturn;
            });
        }
    };

    static connectContactWithUser = (contactId: number) => {
        return request(`/api/crm/landlord/${contactId}/connect`, {
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            body: {},
            method: "POST",
        });
    };

    static deleteContact = (contactId: number, forceDelete?: boolean) => {
        let requestUrl = `/api/contacts-list/${contactId}`;
        if (forceDelete) {
            requestUrl += '?forceDelete=1';
        }
        return request(requestUrl, {
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            body: {},
            method: "DELETE",
        });
    };

    static checkContactInformation = (contactId: string) => {
        return request(`/api/contacts-list/${contactId}/state`, {
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            }
        });
    }
}
