import { IdLabel } from "../../model/fe-models.interface.js";
import { BEContactsResponse } from "../../model/be-models.interface.js";
import config from "../../config.js";

export declare const document: Document;
export declare const request: Function;

export default class AgentsCompetition {
   
    static fetchAgentsData = (month: any) => {
        return request(
            `/admin/competition`,
            {
                method: "POST",
                body: {
                    month,
                },
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
            }
        );
    };
    
}
