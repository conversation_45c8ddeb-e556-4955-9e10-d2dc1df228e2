<template>
    <div class="d-flex flex-row">
        <!-- <div style="margin-right: 10px;" :class="{ 'd-none': !state.isSerban }" class="col-xs-12 col-sm-6 col-lg-3">
            <label class="mb-1" for="">Select agents</label>
            <div>
                <FGVirtualSelect :selectorClassName="FGVirtualSelectAgentsClass"
                    :class="{ 'vscomp-ele': true, [FGVirtualSelectAgentsClass]: true, }" :options="options.agents"
                    :config="state.agentsSelectorConfig" :selectedValue="state.selectedAgents"
                    @change="onAgentChange" />
            </div>
        </div> -->
        <div class="col-xs-12 col-sm-6 col-lg-3">
            <label class="mb-1" for="">Select month</label>
            <input type="month" class="form-control" v-model="state.month" @change="onMonthChange" />
        </div>
    </div>
    <div style="max-height: 600px; overflow: auto;">
        <table class="table table-striped">
            <thead class="thead-dark">
                <tr>
                    <th scope="col">Criteria/Agents</th>
                    <th v-for="item in options.criteria">{{ item.criteriaName }}</th>

                </tr>
            </thead>
            <tbody>
                <tr v-for="(value, agentName) in state.agentData">
                    <td class="td-names"><img alt="" class="img-thumbnail" style="max-width: 60px;"
                            :src="value['profileImage']"> {{ value.agentName }} ({{ value['points'] }})</td>
                    <td class="td-center" :class="{ 'target-reached': value['newContacts'] >= newContactsTarget }">{{
                        value['newContacts'] }}</td>
                    <td class="td-center" :class="{ 'target-reached': value['newListings'] >= newListingsTarget }">{{
                        value['newListings'] }}</td>
                    <td class="td-center" :class="{ 'target-reached': value['newDeals'] >= newDealsTarget }">{{
                        value['newDeals'] }}</td>
                    <td class="td-center"
                        :class="{ 'target-reached': value['reassignedLeads'] >= reassignedLeadsTarget }">{{
                            value['reassignedLeads'] }}</td>
                    <td class="td-center"
                        :class="{ 'target-reached': value['selfGeneratedLeads'] >= selfGeneratedLeadsTarget }">{{
                            value['selfGeneratedLeads'] }}</td>
                    <td class="td-center" :class="{ 'target-reached': value['visitScheduled'] >= visitScheduledTarget }">
                        {{ value['visitScheduled'] }}</td>
                    <td class="td-center" :class="{ 'target-reached': value['cashIn'] >= cashInTarget }">{{
                        getFormattedCashIn(value['cashIn']) }}</td>
                    <td class="td-center"
                        :class="{ 'target-reached': value['verifiedListings'] >= verifiedListingsTarget }">{{
                            value['verifiedListings'] }} </td>
                    <td class="td-center" :class="{ 'target-reached': value['ratings'] >= ratingsTarget }">{{
                        value['ratings'] }} </td>
                </tr>

            </tbody>
        </table>
    </div>
</template>

<script lang="ts">
import FGVirtualSelect from "../FGVirtualSelect.vue";
import { defineComponent, onMounted, reactive } from "vue";
import AgentsCompetition from "./agents-competition.service";
import UtilService from "../../services/utils.service";
import VueDatePicker from "@vuepic/vue-datepicker";
import { format } from "date-fns";

const newContactsTarget = 60;
const newListingsTarget = 30;
const newDealsTarget = 10;
const reassignedLeadsTarget = 30;
const selfGeneratedLeadsTarget = 40;
const visitScheduledTarget = 40;
const cashInTarget = 40000;
const verifiedListingsTarget = 5;
const ratingsTarget = 15;

const criteria = [
    {
        criteriaKey: "new_contacts",
        criteriaName: `New contacts(${newContactsTarget})`,
    },
    {
        criteriaKey: "new_listings",
        criteriaName: `New listings(${newListingsTarget})`,
    },
    {
        criteriaKey: "new_deals",
        criteriaName: `New deals(${newDealsTarget})`,
    },
    {
        criteriaKey: "reassigned_leads",
        criteriaName: `Reassigned leads(${reassignedLeadsTarget})`,
    },
    {
        criteriaKey: "leads_website",
        criteriaName: `Self Generated leads(${selfGeneratedLeadsTarget})`,
    },
    {
        criteriaKey: "visit_scheduled",
        criteriaName: `Visit scheduled(${visitScheduledTarget})`,
    },
    {
        criteriaKey: "cash_in",
        criteriaName: `Cash in(${cashInTarget})`,
    },
    {
        criteriaKey: "verified_listings",
        criteriaName: `Verified listings(${verifiedListingsTarget})`,
    },
    {
        criteriaKey: "ratings",
        criteriaName: `Ratings(${ratingsTarget})`,
    },
];

interface Criteria {
    criteriaKey: string;
    criteriaName: string;
}


interface AgentsData {
    agentData: {
        [key: string]: {
            newContacts: number,
            newListings: number,
            newDeals: number,
            reassignedLeads: number,
            selfGeneratedLeads: number,
            visitScheduled: number,
            cashIn: number,
            verifiedListings: number,
            ratings: number,
            profileImage: string,
            points: number
        };
    },
    profileImage: Array<any>,
    agentsSelectorConfig: {},
    agent: Array<any>,
    selectedAgents: Array<number>,
    isSerban: boolean,
    month: string
}

export declare const agents: any;
export declare const isSerbanConnected: boolean;
export declare const isITDevConnected: boolean;
export default defineComponent({
    name: "AgentCompetitionContainer",
    setup() {
        const FGVirtualSelectAgentsClass = UtilService.virtualSelectClassName();

        const initialState: AgentsData = {
            agentData: {},
            profileImage: [],
            agentsSelectorConfig: {
                disabled: true,
                multiple: true,
                search: true,
            },
            agent: [],
            selectedAgents: [],
            isSerban: false,
            month: "",
        }
        const state = reactive<AgentsData>(initialState);

        const options = reactive({
            agents: [],
            criteria: criteria,
        })

        const onAgentChange = (selectedValue: any) => {
            state.agent = selectedValue;
            sendData(state.month)
        };
        const onMonthChange = (event: Event) => {
            const target = event.target as HTMLInputElement;
            state.month = target.value;
            sendData(state.month)
        };
        const sendData = (month: any) => {
            AgentsCompetition.fetchAgentsData(month)
                .then((data: any) => {
                    state.agentData = data;
                })
                .catch((err: Error) => console.log(err));
        }

        const getFormattedCashIn = (no: number): string => {
            return new Intl.NumberFormat().format(
                no,
            );
        }

        onMounted(() => {
            const activeAgents = agents.filter((agent: any) => agent.is_active_for_competition === 1);
            console.log('activeAgents', activeAgents);
            options.agents = agents.map((agent: any) => {
                return {
                    label: agent.name,
                    value: agent.id,
                    is_active: agent.is_active_for_competition
                };
            });
            state.selectedAgents = activeAgents.map((agent: any) => agent.id);
            state.isSerban = isSerbanConnected || isITDevConnected;

            const currentMonth = format(new Date(), "yyyy-MM");
            state.month = currentMonth;
            sendData(state.month);
        });

        return {
            options,
            state,
            criteria,
            newContactsTarget,
            newListingsTarget,
            newDealsTarget,
            reassignedLeadsTarget,
            selfGeneratedLeadsTarget,
            visitScheduledTarget,
            cashInTarget,
            verifiedListingsTarget,
            ratingsTarget,
            agents,
            getFormattedCashIn,

            FGVirtualSelectAgentsClass,
            onAgentChange,
            onMonthChange
        };
    },
    components: {
        FGVirtualSelect,
        VueDatePicker
    },

});
</script>
<style lang="scss">
.target-reached {
    background-color: #3CB371 !important;
    color: #fff !important;
}

.td-names {
    text-align: left;
    min-width: 220px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.td-center {
    text-align: center;
}
</style>