<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Invoice;
use App\Models\Crm\Deal;
use App\Models\Contact;
use App\Models\Property;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class InvoicePDFTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_generate_invoice_pdf_view()
    {
        // Create test data
        $client = Contact::factory()->create(['name' => 'Test Client']);
        $owner = Contact::factory()->create(['name' => 'Test Owner']);
        $property = Property::factory()->create();
        
        $deal = Deal::factory()->create([
            'client_id' => $client->id,
            'owner_id' => $owner->id,
            'property_id' => $property->id,
        ]);

        $invoice = Invoice::factory()->create([
            'deal_id' => $deal->id,
            'ref_no' => 'INV-001',
            'amount' => 5000.00,
            'is_client' => true,
        ]);

        // Test the view route
        $response = $this->get(route('deal.invoice.view', [
            'id' => $deal->id,
            'invoiceId' => $invoice->id
        ]));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/pdf');
    }

    /** @test */
    public function it_can_generate_invoice_pdf_download()
    {
        // Create test data
        $client = Contact::factory()->create(['name' => 'Test Client']);
        $owner = Contact::factory()->create(['name' => 'Test Owner']);
        $property = Property::factory()->create();
        
        $deal = Deal::factory()->create([
            'client_id' => $client->id,
            'owner_id' => $owner->id,
            'property_id' => $property->id,
        ]);

        $invoice = Invoice::factory()->create([
            'deal_id' => $deal->id,
            'ref_no' => 'INV-002',
            'amount' => 3000.00,
            'is_client' => false,
        ]);

        // Test the download route
        $response = $this->get(route('deal.invoice.download', [
            'id' => $deal->id,
            'invoiceId' => $invoice->id
        ]));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/pdf');
        $response->assertHeader('content-disposition', 'attachment; filename="invoice_INV-002.pdf"');
    }

    /** @test */
    public function it_returns_404_for_non_existent_invoice()
    {
        $deal = Deal::factory()->create();

        $response = $this->get(route('deal.invoice.view', [
            'id' => $deal->id,
            'invoiceId' => 999999
        ]));

        $response->assertStatus(404);
    }

    /** @test */
    public function it_returns_404_for_invoice_not_belonging_to_deal()
    {
        $deal1 = Deal::factory()->create();
        $deal2 = Deal::factory()->create();
        
        $invoice = Invoice::factory()->create([
            'deal_id' => $deal2->id,
        ]);

        $response = $this->get(route('deal.invoice.view', [
            'id' => $deal1->id,
            'invoiceId' => $invoice->id
        ]));

        $response->assertStatus(404);
    }
}
